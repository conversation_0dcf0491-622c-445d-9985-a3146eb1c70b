<?php
/* Smarty version 3.1.33, created on 2022-02-04 21:06:16
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/agent.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_61fd24c8810ab8_39131475',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '6fd0437656a527534d2b9590c7a0f387cd9ba1d6' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/agent.tpl',
      1 => 1588140622,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:user/main.tpl' => 1,
    'file:table/checkbox.tpl' => 1,
    'file:table/table.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:user/footer.tpl' => 1,
    'file:table/js_1.tpl' => 1,
    'file:table/lang_chinese.tpl' => 1,
    'file:table/js_3.tpl' => 1,
    'file:table/js_delete.tpl' => 1,
  ),
),false)) {
function content_61fd24c8810ab8_39131475 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:user/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Agent</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">代理中心</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/shop" class="btn btn-sm btn-neutral">商店购买</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">代理说明</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>自己SSR拼接订阅链接为：<?php echo $_smarty_tpl->tpl_vars['config']->value['subUrl'];?>
<code>SSR链接码</code>?sub=1&extend=0</p>
                            <p class="description badge-dot mr-4"><i class="bg-warning"></i>自己V2ray拼接订阅链接为：<?php echo $_smarty_tpl->tpl_vars['config']->value['subUrl'];?>
<code>SSR链接码</code>?sub=3&extend=0</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>用户等级：<?php echo $_smarty_tpl->tpl_vars['levels']->value;?>
</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>流量包商品、同等级(如：客户等级是<code>铂金</code>你购买铂金套餐.)商品可以叠加.</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>高等级购买低等级套餐(如：客户等级是<code>王者</code>你购买铂金套餐)会导致降级,但流量会叠加.</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>低等级购买高等级套餐(如：客户等级是<code>铂金</code>你购买王者套餐)会导致本身的套餐被商品的套餐替代，原有套餐清空.</p>
						<?php if ($_smarty_tpl->tpl_vars['config']->value["enable_admin_contact"] == 'true') {?>
								<p>有问题请联系管理员&nbsp;联系方式：</p>
							<?php if ($_smarty_tpl->tpl_vars['config']->value["admin_contact1"] != null) {?>
								<p>
									<?php echo $_smarty_tpl->tpl_vars['config']->value["admin_contact1"];?>

								</p>
							<?php }?>
							<?php if ($_smarty_tpl->tpl_vars['config']->value["admin_contact2"] != null) {?>
								<p>
									<?php echo $_smarty_tpl->tpl_vars['config']->value["admin_contact2"];?>

								</p>
							<?php }?>
							<?php if ($_smarty_tpl->tpl_vars['config']->value["admin_contact3"] != null) {?>
								<p>
									<?php echo $_smarty_tpl->tpl_vars['config']->value["admin_contact3"];?>

								</p>
							<?php }?>
						<?php }?>
						<p>
							<i class="ni ni-card ni-lg icon-ver"></i>当前余额：<font color="#399AF2" size="3"><?php echo $_smarty_tpl->tpl_vars['user']->value->money;?>
</font> 元&nbsp;&nbsp;&nbsp;&nbsp;<a class="btn btn-success btn-sm" href="/user/code" >点此充值</a>
						</p>
						<p class="description badge-dot mr-4"><i class="bg-warning"></i>显示表项: <?php $_smarty_tpl->_subTemplateRender('file:table/checkbox.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?></p>
						
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
			
            <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h4 class="mb-0">客户记录</h4>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive">
				  <?php $_smarty_tpl->_subTemplateRender('file:table/table.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
				</div>
              </div>
            </div><!--card-->
			
			<div class="card">
				<div class="card-header bg-transparent">
					<h4 class="mb-0">添加用户(邮箱=密码)</h4>
				</div>
				<div class="card-body">
					<div class="form-group">
						<label class="form-control-label">用户昵称:</label>
						<input id="userName" class="form-control form-control-sm" type="text" placeholder="请填写用户昵称...">
					</div>
					<div class="form-group">
						<label class="form-control-label">填写邮箱:</label>
						<input id="email" class="form-control form-control-sm" type="text" placeholder="请输入用户邮箱...">
					</div>
					<label class="form-control-label">选择套餐:</label>
					<select id="sptype" class="form-control form-control-sm">
						<option value="0"><a href="javascript:void(0)" onclick="return false;">选择您要开通的套餐</a></option>
						<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['shop_name']->value, 'value', false, 'key');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['key']->value => $_smarty_tpl->tpl_vars['value']->value) {
?>
							<option value="<?php echo $_smarty_tpl->tpl_vars['value']->value["id"];?>
"><?php echo $_smarty_tpl->tpl_vars['value']->value["name"];?>
--<?php echo $_smarty_tpl->tpl_vars['value']->value["price"];?>
</option>
						<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
					</select>
					
					<div class="custom-control custom-checkbox mt-3">
						<input id="issend" type="checkbox" class="custom-control-input icon-ver">
						<label class="custom-control-label" for="issend">是否发送注册成功邮件,提交后勿关闭页面,等待刷新</label>
					</div>
				</div>
				<div class="modal-footer">
                    <button id="adds_input" type="button" class="btn btn-primary">确认提交</button>
				</div>
				<div class="card-body">
				    <div class="table-responsive">
						<table class="table align-items-center table-flush">
						  <tbody class="thead-light">
							<tr>
								<th>ID</th>
								<th>账号</th>
								<th>ssr链接</th>
								<th>VIP等级</th>
								<th>等级到期</th>
							</tr>
							<tr id="adduserResult">
										
							</tr>
						  </tbody>
						</table>
					</div>
				</div>	
			</div>
			
			
        </div>
      </div><!--row-->
	  <!--pay modal-->
	    <div class="modal fade" id="user_modal" tabindex="-1" role="dialog" aria-labelledby="userModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="userModalLabel">用户信息确认</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p id="userid" class="description">ID：</p>
					<p id="user_email" class="description">邮箱：</p>
					<select id="shop_id" class="form-control form-control-sm">
						<option value="0"><a href="javascript:void(0)" onclick="return false;">选择您要开通的套餐</a></option>
						<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['shop_name']->value, 'shop');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['shop']->value) {
?>
							<option value="<?php echo $_smarty_tpl->tpl_vars['shop']->value->id;?>
"><?php echo $_smarty_tpl->tpl_vars['shop']->value->name;?>
--<?php echo $_smarty_tpl->tpl_vars['shop']->value->price;?>
</option>
						<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
					</select>
				</div>	 
		      </div>
			  <div class="modal-footer">
                    <button id="user_input" type="button" class="btn btn-primary">确认提交</button>
               </div>
		    </div>
		  </div>
		</div>
		 <!--delete modal-->
	   <div class="modal fade" id="delete_modal" tabindex="-1" role="dialog" aria-labelledby="deleteidModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="deleteidModalLabel" class="text-danger">删除信息确认</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p id="deleteid" class="description">ID：</p>
					<p id="delete_email" class="description">邮箱：</p>
		        </div>
		      </div>
			   <div class="modal-footer">
                    <button id="delete_input" type="button" class="btn btn-primary">确认提交</button>
               </div>
		   </div>
		 </div>
	   </div>
	  <?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php $_smarty_tpl->_subTemplateRender('file:user/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
echo '<script'; ?>
 src="//cdn.jsdelivr.net/npm/datatables.net@1.10.19"><?php echo '</script'; ?>
>  
<?php echo '<script'; ?>
 src="/theme/czssr/main/js/dataTables.material.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
>
function Copy(link) {
    $link = link;
    const clipboard = new ClipboardJS('.copy-config', {
        text: function() {
            return $link;
        }
    });
    clipboard.on('success', function(e) {
		    $("#result").modal();
			$("#msg").html("复制成功，部分手机可能不支持");
		
		}
    );
	
}
<?php echo '</script'; ?>
>
<?php echo '<script'; ?>
>
//续费操作
	function del(delete_id,delete_email) {
	
		$("#deleteid").html("用户ID: "+delete_id);
		$("#delete_email").html("邮箱: "+delete_email);
		deleteid = delete_id;
        $("#delete_modal").modal();
	}

	function buy(id,user_email) {
	
		$("#userid").html("用户ID: " + id);
		$("#user_email").html("邮箱: " + user_email);
		userid = id;
		$("#user_modal").modal();
	}

 <?php $_smarty_tpl->_subTemplateRender('file:table/js_1.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
    window.addEventListener('load', () => {
       table_1 = $('#table_1').DataTable({
           order: [[1, 'asc']],
           stateSave: true,
           serverSide: true,
           ajax: {
               url: "/user/agent/ajax",
               type: "POST",
           },
           columns: [
               
               {"data": "op", "orderable": false},
               {"data": "id"},
               {"data": "email"},
               {"data": "transfer"},
               {"data": "class"},
               {"data": "class_expire"},
               {"data": "ssrlink"},
			   {"data": "detect_ban"},
			   
            ],
			"columnDefs": [
                {
                    targets: ['_all'],
                    className: 'mdl-data-table__cell--non-numeric'
                }
            ],

            <?php $_smarty_tpl->_subTemplateRender('file:table/lang_chinese.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
        });

       <?php $_smarty_tpl->_subTemplateRender('file:table/js_3.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>  
    $("#delete_input").click(function () {
		$.ajax({
			type: "DELETE",
			url: "/user/agentdelete",
			dataType: "json",
			data: {
				deleteid: deleteid
			},
			success: function (data) {
				if (data.ret) {
                  $("#delete_modal").modal('hide');
					swal({
                       title: "删除结果",
                       text: data.msg, 
                       type:"success"
                       });
					   <?php $_smarty_tpl->_subTemplateRender('file:table/js_delete.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
                 // window.setTimeout("location.href=window.location.href", 2000);
				} else {
                    $("#delete_modal").modal('hide');
					swal('Oops...',data.msg,'error');
				}
			},
			error: function (jqXHR) {
				$("#result").modal();
                $("#msg").html("发生错误了: " + jqXHR.status);
			}
		});
	});
   })
<?php echo '</script'; ?>
>
<!--删除用户-->
<!--添加用户操作-->
<?php echo '<script'; ?>
>
		//检查输入字符合法性逻辑
    function checkByteLength(str,minlen,maxlen) {
    if (str == null) return false;
    //为空返回false
    var l = str.length;
    var blen = 0;
    for(i=0; i<l; i++) {
      //循环取得检验值的长度
      if ((str.charCodeAt(i) & 0xff00) != 0) {
        blen ++;
      }
      blen ++;
    }
    if (blen > maxlen || blen < minlen) {
      //判断长度是否合法
      return false;
    }
    return true;
  }

		//检查输入字符合法性逻辑
  function validateUsername(value){
    var patn = /^[a-zA-Z]+[a-zA-Z0-9]+$/; 
    if(!checkByteLength(value,4,16)) return false;

    var pattern = /^[A-Za-z0-9\u4e00-\u9fa5]+$/gi;
	if (pattern.test(value)) {
      return true; 
    }else{
      return false;
    }
    
  }
  function validateEmail(value){
    var pattern = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
	if (pattern.test(value)) {
      return true; 
    }else{
      return false;
    }
  }   

  //单个注册调用
$(document).ready(function() {
   function addUser() {
		
        var name = document.getElementById("userName").value;
	  	var email = document.getElementById("email").value;
		var shopId = document.getElementById("sptype").value;
        if ($$.getElementById('issend').checked) {
			var issend=1;
        } else {
			var issend=0;
		}
		/*用户昵称检测*/
		if(!validateUsername($("#userName").val())) {
  　　　　　swal('Oops...', "用户名不合法,仅支持4~16位字母数字或中文",'error');
            return false;
        }
		/*邮箱检测*/
		if(!validateEmail($("#email").val())) {
  　　　　　swal('Oops...', "邮箱不合法,请检查后输入",'error');
            return false;
        }
		/* 邮箱检测 */
         if($("#email").val()==null || $("#email").val()==''){
           swal('Oops...', "邮箱不能为空！",'error');
           return;
         }
         var email_arr = $("#email").val().split('@');
         var email_blacklist = ["qq.com","sina.com", "163.com","sina.cn", "gmail.com", "live.com", "163.com", "139.com", "outlook.com", "189.cn", "foxmail.com", "vip.qq.com", "hotmail.com", "126.com", "aliyun.com", "yeah.net", "sohu.com", "live.jp", "msn.com", "icloud.com"];
         if ($.inArray(email_arr[1], email_blacklist) == "-1") {
           swal('Oops...', "暂不支持此邮箱，请更换如QQ、谷歌、新浪、网易等常见邮箱。",'error');
           return false;
         } 
     		/*套餐检测*/
		if (shopId == "" || shopId == 0){
			swal('Oops...', "请选择要开通的套餐！",'error');
            return;
        }
         $.ajax({
            type: "POST",
            url: "/user/addUser",
            dataType: "json",
            data: {
                "userName": name,
              	"email": email,
				"shopId": shopId,
                 issend
            },
            success: function (data) {
             		var html='';
                if (data.ret == "0"){
                  	swal('Oops...',data.msg,'error');
                }else {
                  swal({
                        title: "添加用户成功",
                        text: "赶快复制下方的新用户信息吧~!", 
                        type:"success"
                        });
                	for(var i=0;i<data.length;i++) {
                        var ls = data[i];     
                        html += "<td>" + ls.id + "</td><td>" + ls.email + "</td><td>" + ls.ssrlink + "</td><td>" + ls.class + "</td><td>" + ls.class_expire + "</td>";
                    }
				$("#adduserResult").html(html);
                }   
              
            },
            error: function (jqXHR) {
                $("#result").modal();
                $("#msg").html("发生错误了: " + jqXHR.status);
              
            }
        });
    }

    $("#adds_input").click(function () {
        addUser();
    });
});
<?php echo '</script'; ?>
><?php }
}
