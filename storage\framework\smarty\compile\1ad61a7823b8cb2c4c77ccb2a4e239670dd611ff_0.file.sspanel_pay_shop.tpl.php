<?php
/* Smarty version 3.1.33, created on 2022-02-03 22:59:03
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/sspanel_pay_shop.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_61fbedb7372d96_09012744',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '1ad61a7823b8cb2c4c77ccb2a4e239670dd611ff' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/sspanel_pay_shop.tpl',
      1 => 1631761887,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_61fbedb7372d96_09012744 (Smarty_Internal_Template $_smarty_tpl) {
if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "f2fpay") {?>
<!--支付宝面对面-->
<?php echo '<script'; ?>
>	
	
	function pay_online() {
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_a/purchase",
                dataType: "json",
                data: {
                    amount: $("#amount").val()
                },
                success: function (data) {
                    if (data.ret) {
                        console.log(data);
                        pid = data.pid;
                        $("#ali_qrarea").html('<div class="text-center"><p>请使用支付宝APP扫描二维码支付</p><a id="qrcode" style="padding-top:10px;display:inline-block"></a><p>手机点击二维码可唤起支付宝</p></div>');
                        $("#readytopay").modal('hide');
						$("#qr_code").modal();
                        new QRCode("qrcode", {
                            render: "canvas",
                            width: 200,
                            height: 200,
                            text: encodeURI(data.qrcode)
                        });
                        $('#qrcode').attr('href',data.qrcode);
                        setTimeout(f, 1000);
                    } else {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                    }
                },
                error: function (jqXHR) {
                    console.log(jqXHR);
                    $("#readytopay").modal('hide');
					$("#qr_code").modal('hide');
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            })
        });
	}
	function f(){
        $.ajax({
            type: "POST",
            url: "/payment_a/status",
            dataType: "json",
            data: {
                pid:pid
            },
            success: function (data) {
                if (data.result) {
                    console.log(data);
                   $("#qr_code").modal('hide');
                  //  $("#result").modal();
                   // $("#msg").html("充值成功！");
					$("#result").modal('hide');
					pay_code();
                  return;
                   // window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                }else{
                tid = setTimeout(f, 1000); //循环调用触发setTimeout
                }
            },
            error: function (jqXHR) {
                console.log(jqXHR);
            }
        });
     
    }
<?php echo '</script'; ?>
>
<?php }
if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "mycode") {?>
<!--mycode-->
<?php echo '<script'; ?>
>	
	function pay_online() {
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_a/purchase",
                dataType: "json",
                data: {
                    price: $("#amount").val(),
                    type: "alipay",
                    method: "shop"
                },
                   
                success: function (data) {
                    if (data.ret) {
                        console.log(data);
                        jump_link = data.url;
                        pid = data.pid;
                      $('#readytopay').modal('hide');
				      $("#payresult").modal();
				      setTimeout(f, 1000);
                      window.setTimeout(open(jump_link), 1500);
                    } else {
                        $('#readytopay').modal('hide');
						$("#payresult").modal('hide');
				    	$("#result").modal();
				    	$("#msg").html(data.msg);
                    }
                },
                error: function (jqXHR) {
                    console.log(jqXHR);
                    $("#readytopay").modal('hide');
					$("#qr_code").modal('hide');
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            })
        });
	}
	function f(){
        $.ajax({
            type: "POST",
            url: "/payment_a/status",
            dataType: "json",
            data: {
                pid:pid
            },
            success: function (data) {
                if (data.result) {
                    $("#qr_code").modal('hide');
					$("#result").modal('hide');
					pay_code();
                    return true;
                   //window.setTimeout("location.href=window.location.href", 1500);
                }else{
                    tid = setTimeout(f, 2000); //循环调用触发setTimeout
                }
            },
            error: function (jqXHR) {
                console.log(jqXHR);
            }
        });
    }
<?php echo '</script'; ?>
>
<?php }
if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "mycode") {?>
<!--mycode-->
<?php echo '<script'; ?>
>	
	function wx_pay() {
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_b/purchase",
                dataType: "json",
                data: {
                    price: $("#amount").val(),
                    type: "wxpay",
                    method: "shop"
                },
                success: function (data) {
                    if (data.ret) {
                        console.log(data);
                        pid = data.pid;
                        $("#ali_qrarea").html('<div class="text-center"><p>请使用APP扫描二维码支付'+data.price+'</p><a id="qrcode" style="padding-top:10px;display:inline-block"></a><p>手机截图保存相册扫码</p></div>');
                        $("#readytopay").modal('hide');
						$("#qr_code").modal();
                        new QRCode("qrcode", {
                            render: "canvas",
                            width: 200,
                            height: 200,
                            text: encodeURI(data.qrcode)
                        });
                        $('#qrcode').attr('href',data.qrcode);
                        setTimeout(f, 1000);
                    } else {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                    }
                },
                error: function (jqXHR) {
                    console.log(jqXHR);
                    $("#readytopay").modal('hide');
					$("#qr_code").modal('hide');
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            })
        });
	}
	function f(){
        $.ajax({
            type: "POST",
            url: "/payment_b/status",
            dataType: "json",
            data: {
                pid:pid
            },
            success: function (data) {
                if (data.result) {
                    $("#qr_code").modal('hide');
					$("#result").modal('hide');
					pay_code();
                    return true;
                   //window.setTimeout("location.href=window.location.href", 1500);
                }else{
                    tid = setTimeout(f, 2000); //循环调用触发setTimeout
                }
            },
            error: function (jqXHR) {
                console.log(jqXHR);
            }
        });
    }
<?php echo '</script'; ?>
>
<?php }
if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "umikm") {?>
<!--优米支付(小微商户)-->
<?php echo '<script'; ?>
>	
    
	function wx_pay(){
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_b/purchase",
                dataType: "json",
                data: {
                    price: $("#amount").val()
                },
                success: function (data) {
                    if (data.ret) {
                        console.log(data);
                        pid = data.pid;
                        $("#ali_qrarea").html('<div class="text-center"><p>请使用微信APP扫描二维码支付</p><a id="qrcode" style="padding-top:10px;display:inline-block"></a><p>手机截图保存相册扫码</p></div>');
                        $("#readytopay").modal('hide');
						$("#qr_code").modal();
                        new QRCode("qrcode", {
                            render: "canvas",
                            width: 200,
                            height: 200,
                            text: encodeURI(data.qrcode)
                        });
                        $('#qrcode').attr('href',data.qrcode);
                        setTimeout(f, 1000);
                    } else {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                    }
                },
                error: function (jqXHR) {
                    console.log(jqXHR);
                    $("#readytopay").modal('hide');
					$("#qr_code").modal('hide');
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            })
        });
    }
	function f(){
		$.ajax({
			type: "POST",
			url: "/payment_b/status",
			dataType: "json",
			data: {
				pid:pid
			},
			success: function (data) {
				if (data.result) {
					console.log(data);
					$("#qr_code").modal('hide');
					//$("#result").modal();
                   // $("#msg").html("充值成功！");
					$("#result").modal('hide');
					pay_code();
                    return;
					//window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				}else{
                tid = setTimeout(f, 1000); //循环调用触发setTimeout
                }
			},
			error: function (jqXHR) {
				console.log(jqXHR);
			}
		});
	}

<?php echo '</script'; ?>
>	
<?php }?> 

<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "payjs") {?>
<!--PAYJS(小微商户)-->
<?php echo '<script'; ?>
>	
    
	
	function wx_pay(){
	    var price = parseFloat($("#amount").val());
        if (isNaN(price)) {
            $("#readytopay").modal('hide');
            $("#result").modal();
			$("#msg").html("非法的金额！");
            return;
        }
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_b/purchase",
                dataType: "json",
                data: {
                    price
                },
                success: function (data) {
				    console.log(data);
                    var jump_link = data.url;
				    if(data.code==0){
				    	pid = data.pid;
				    	$('#readytopay').modal('hide');
				    	$("#payresult").modal();
				    	setTimeout(f, 1000);
                        window.setTimeout(open(jump_link), <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				    }else{ 
				    	$('#readytopay').modal('hide');
						$("#payresult").modal('hide');
				    	$("#result").modal();
				    	$("#msg").html(data.errmsg);
				    }
                    
                },
                error: function (jqXHR) {
                    console.log(jqXHR);
                    $("#readytopay").modal('hide');
					$("#payresult").modal('hide');
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            })
        });
    }
	function f(){
		$.ajax({
			type: "POST",
			url: "/payment_b/status",
			dataType: "json",
			data: {
				pid:pid
			},
			success: function (data) {
				if (data.result) {
					console.log(data);
					$("#payresult").modal('hide');
					//$("#result").modal();
                   // $("#msg").html("充值成功！");
					$("#result").modal('hide');
					pay_code();
                    return;
					//window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				}else{
                tid = setTimeout(f, 1000); //循环调用触发setTimeout
                }
			},
			error: function (jqXHR) {
				console.log(jqXHR);
			}
		});
	}

<?php echo '</script'; ?>
>
<?php }?>

<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "codepay") {?>
<!--码支付(双通道)-->
<?php echo '<script'; ?>
>	
    
	function pay_online(){
	    var type = 1; //1支付宝 3微信支付
        var price = parseFloat($("#amount").val());
        //console.log("将要使用 " + type + " 充值" + price + "元");
        if (isNaN(price)) {
            $("#readytopay").modal('hide');
            $("#result").modal();
			$("#msg").html("非法的金额！");
            return;
        }
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_a/purchase",
                dataType: "json",
                data: {
                    price,
					type
                },
                success: function (data) {
				    console.log(data);
                    var jump_link = data.url;
				    if(data.code==0){
				    	pid = data.pid;
				    	$('#readytopay').modal('hide');
						$("#ali_qrarea").html('<div class="text-center"><p>请使用支付宝APP扫描二维码支付</p>'+jump_link+'<p>请在5分钟内完成支付</p></div>');
						$("#qr_code").modal();
				    	setTimeout(f, 1000);
                        //window.setTimeout(open(jump_link), <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				    }else{ 
				    	$('#readytopay').modal('hide');
						//$("#payresult").modal('hide');
				    	$("#result").modal();
				    	$("#msg").html(data.errmsg);
				    }
                    
                },
                error: function (jqXHR) {
                    console.log(jqXHR);
                    $("#readytopay").modal('hide');
					$("#qr_code").modal('hide');
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            })
        });
    }
	function f(){
		$.ajax({
			type: "POST",
			url: "/payment_a/status",
			dataType: "json",
			data: {
				pid:pid
			},
			success: function (data) {
				if (data.result) {
					console.log(data);
					$("#qr_code").modal('hide');
					//$("#result").modal();
                   // $("#msg").html("充值成功！");
					$("#result").modal('hide');
					pay_code();
                    return;
					//window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				}else{
                tid = setTimeout(f, 1000); //循环调用触发setTimeout
                }
			},
			error: function (jqXHR) {
				console.log(jqXHR);
			}
		});
	
	}

<?php echo '</script'; ?>
>
<?php }
if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "codepay") {?>
<!--码支付(双通道)-->
<?php echo '<script'; ?>
>	
    
	function wx_pay(){
	    var type = 3; //1支付宝 3微信支付
        var price = parseFloat($("#amount").val());
        //console.log("将要使用 " + type + " 充值" + price + "元");
        if (isNaN(price)) {
            $("#readytopay").modal('hide');
            $("#result").modal();
			$("#msg").html("非法的金额！");
            return;
        }
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_b/purchase",
                dataType: "json",
                data: {
                    price,
					type
                },
                success: function (data) {
				    console.log(data);
                    var jump_link = data.url;
				    if(data.code==0){
				    	pid = data.pid;
				    	$('#readytopay').modal('hide');
						$("#ali_qrarea").html('<div class="text-center"><p>请使用微信APP扫描二维码支付</p>'+jump_link+'<p>手机截图保存相册扫码</p></div>');
						$("#qr_code").modal();
				    	setTimeout(f, 1000);
                       // window.setTimeout(open(jump_link), <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				    }else{ 
				    	$('#readytopay').modal('hide');
						//$("#payresult").modal('hide');
				    	$("#result").modal();
				    	$("#msg").html(data.errmsg);
				    }
                    
                },
                error: function (jqXHR) {
                    console.log(jqXHR);
                    $("#readytopay").modal('hide');
					$("#qr_code").modal('hide');
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            })
        });
    }
	function f(){
		$.ajax({
			type: "POST",
			url: "/payment_b/status",
			dataType: "json",
			data: {
				pid:pid
			},
			success: function (data) {
				if (data.result) {
					console.log(data);
					$("#qr_code").modal('hide');
					//$("#result").modal();
                   // $("#msg").html("充值成功！");
					$("#result").modal('hide');
                    pay_code();
					return ;
					//window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				}else{
                tid = setTimeout(f, 1000); //循环调用触发setTimeout
                }
			},
			error: function (jqXHR) {
				console.log(jqXHR);
			}
		});
	}

<?php echo '</script'; ?>
>
<?php }?>

<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "flyfoxpay") {?>
<!--跃空聚合支付(双通道)-->
<?php echo '<script'; ?>
>	
    
	function pay_online(){
	    var type = 'alipay'; //1支付宝 3微信支付
        var price = parseFloat($("#amount").val());
        //console.log("将要使用 " + type + " 充值" + price + "元");
        if (price < 3) {
            $("#result").modal();
            $("#msg").html("输入金额最低3元,请检查");
            return;
        }
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_a/purchase",
                dataType: "json",
                data: {
                    price,
					type
                },
                success: function (data) {
				    console.log(data);
                    var jump_link = data.url;
				    if(data.code==0){
				    	pid = data.pid;
				    	$("#ali_qrarea").html('<div class="text-center"><p>请使用支付宝信APP扫描二维码支付</p><a id="qrcode" style="padding-top:10px;display:inline-block"></a><p>手机截图保存相册扫码</p></div>');
                        $("#readytopay").modal('hide');
						$("#qr_code").modal();
                        new QRCode("qrcode", {
                            render: "canvas",
                            width: 200,
                            height: 200,
                            text: encodeURI(data.url)
                        });
                        $('#qrcode').attr('href',data.url);
                        setTimeout(f, 1000);
				    }else{ 
				    	$('#readytopay').modal('hide');
						$("#payresult").modal('hide');
				    	$("#result").modal();
				    	$("#msg").html(data.errmsg);
				    }
                    
                },
                error: function (jqXHR) {
                    console.log(jqXHR);
                    $("#readytopay").modal('hide');
					$("#payresult").modal('hide');
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            })
        });
    }
	function f(){
		$.ajax({
			type: "POST",
			url: "/payment_a/status",
			dataType: "json",
			data: {
				pid:pid
			},
			success: function (data) {
				if (data.result) {
					console.log(data);
					$("#payresult").modal('hide');
					//$("#result").modal();
                   // $("#msg").html("充值成功！");
					$("#result").modal('hide');
					pay_code();
                  return;
					//window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				}else{
                tid = setTimeout(f, 1000); //循环调用触发setTimeout
                }
			},
			error: function (jqXHR) {
				console.log(jqXHR);
			}
		});
		
	}

<?php echo '</script'; ?>
>
<?php }?>

<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "flyfoxpay") {?>
<!--跃空聚合支付(双通道)-->
<?php echo '<script'; ?>
>	
    
	function wx_pay(){
	    var type = 'wxpay'; //1支付宝 3微信支付
        var price = parseFloat($("#amount").val());
        //console.log("将要使用 " + type + " 充值" + price + "元");
        if (price < 3) {
            $("#result").modal();
            $("#msg").html("输入金额最低3元,请检查");
            return;
        }
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_b/purchase",
                dataType: "json",
                data: {
                    price,
					type
                },
                success: function (data) {
				    console.log(data);
                    var jump_link = data.url;
				    if(data.code==0){
				    	pid = data.pid;
				    	$("#ali_qrarea").html('<div class="text-center"><p>请使用微信APP扫描二维码支付</p><a id="qrcode" style="padding-top:10px;display:inline-block"></a><p>手机截图保存相册扫码</p></div>');
                        $("#readytopay").modal('hide');
						$("#qr_code").modal();
                        new QRCode("qrcode", {
                            render: "canvas",
                            width: 200,
                            height: 200,
                            text: encodeURI(data.url)
                        });
                        $('#qrcode').attr('href',data.url);
                        setTimeout(f, 1000);
				    }else{ 
				    	$('#readytopay').modal('hide');
						$("#payresult").modal('hide');
				    	$("#result").modal();
				    	$("#msg").html(data.errmsg);
				    }
                    
                },
                error: function (jqXHR) {
                    console.log(jqXHR);
                    $("#readytopay").modal('hide');
					$("#payresult").modal('hide');
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            })
        });
    }
	function f(){
		$.ajax({
			type: "POST",
			url: "/payment_b/status",
			dataType: "json",
			data: {
				pid:pid
			},
			success: function (data) {
				if (data.result) {
					console.log(data);
					$("#payresult").modal('hide');
					//$("#result").modal();
                   // $("#msg").html("充值成功！");
					$("#result").modal('hide');
					pay_code();
                  return;
					//window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				}else{
                tid = setTimeout(f, 1000); //循环调用触发setTimeout
                }
			},
			error: function (jqXHR) {
				console.log(jqXHR);
			}
		});
	
	}

<?php echo '</script'; ?>
>
<?php }?>

<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "tomatopay") {?>
<!--番茄云支付(双通道)-->
<?php echo '<script'; ?>
>	
    
	function pay_online(){
	    var type = 'alipay'; //1支付宝 3微信支付
        var price = parseFloat($("#amount").val());
        //console.log("将要使用 " + type + " 充值" + price + "元");
        if (price < 1) {
            $("#result").modal();
            $("#msg").html("输入金额最低1元,请检查");
            return;
        }
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_a/purchase",
                dataType: "json",
                data: {
                    price,
					type
                },
                success: function (data) {
				    console.log(data);
                    var jump_link = data.url;
				    if(data.code==0){
				    	pid = data.pid;
				    	$("#ali_qrarea").html('<div class="text-center"><p>请使用支付宝APP扫描二维码支付</p><a id="qrcode" style="padding-top:10px;display:inline-block"></a><p>手机访问请截图到相册扫码</p></div>');
                        $("#readytopay").modal('hide');
						$("#qr_code").modal();
                        new QRCode("qrcode", {
                            render: "canvas",
                            width: 200,
                            height: 200,
                            text: encodeURI(data.url)
                        });
                        $('#qrcode').attr('href',data.url);
                        setTimeout(f, 1000);
				    }else{ 
				    	$('#readytopay').modal('hide');
						$("#payresult").modal('hide');
				    	$("#result").modal();
				    	$("#msg").html(data.errmsg);
				    }
                    
                },
                error: function (jqXHR) {
                    console.log(jqXHR);
                    $("#readytopay").modal('hide');
					$("#payresult").modal('hide');
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            })
        });
    }
	function f(){
		$.ajax({
			type: "POST",
			url: "/payment_a/status",
			dataType: "json",
			data: {
				pid:pid
			},
			success: function (data) {
				if (data.result) {
					console.log(data);
					$("#payresult").modal('hide');
					//$("#result").modal();
                   // $("#msg").html("充值成功！");
					$("#result").modal('hide');
					pay_code();
                  return;
					//window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				}else{
                tid = setTimeout(f, 1000); //循环调用触发setTimeout
                }
			},
			error: function (jqXHR) {
				console.log(jqXHR);
			}
		});
	
	}
<?php echo '</script'; ?>
>
<?php }
if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "tomatopay") {?>
<!--番茄云支付(双通道)-->
<?php echo '<script'; ?>
>	
    
	function wx_pay(){
	    var type = 'wxpay'; //1支付宝 3微信支付
        var price = parseFloat($("#amount").val());
        //console.log("将要使用 " + type + " 充值" + price + "元");
        if (price < 1) {
            $("#result").modal();
            $("#msg").html("输入金额最低1元,请检查");
            return;
        }
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_b/purchase",
                dataType: "json",
                data: {
                    price,
					type
                },
                success: function (data) {
				    console.log(data);
                    var jump_link = data.url;
				    if(data.code==0){
				    	pid = data.pid;
				    	$("#ali_qrarea").html('<div class="text-center"><p>请使用微信APP扫描二维码支付</p><a id="qrcode" style="padding-top:10px;display:inline-block"></a><p>手机访问请用另一部手机扫码</p></div>');
                        $("#readytopay").modal('hide');
						$("#qr_code").modal();
                        new QRCode("qrcode", {
                            render: "canvas",
                            width: 200,
                            height: 200,
                            text: encodeURI(data.url)
                        });
                        $('#qrcode').attr('href',data.url);
                        setTimeout(f, 1000);
				    }else{ 
				    	$('#readytopay').modal('hide');
						$("#payresult").modal('hide');
				    	$("#result").modal();
				    	$("#msg").html(data.errmsg);
				    }
                    
                },
                error: function (jqXHR) {
                    console.log(jqXHR);
                    $("#readytopay").modal('hide');
					$("#payresult").modal('hide');
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            });
        });
    }
	function f(){
		$.ajax({
			type: "POST",
			url: "/payment_b/status",
			dataType: "json",
			data: {
				pid:pid
			},
			success: function (data) {
				if (data.result) {
					console.log(data);
					$("#payresult").modal('hide');
					//$("#result").modal();
                   // $("#msg").html("充值成功！");
					$("#result").modal('hide');
					pay_code();
                  return;
					//window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				}else{
                tid = setTimeout(f, 1000); //循环调用触发setTimeout
                }
			},
			error: function (jqXHR) {
				console.log(jqXHR);
			}
		});
	
	}
<?php echo '</script'; ?>
>
<?php }
if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "idtpay") {
echo '<script'; ?>
>
//idt支付宝

	function pay_online(){
	    var type = 'alipay'; //1支付宝 3微信支付
        var price = parseFloat($("#amount").val());
        //console.log("将要使用 " + type + " 充值" + price + "元");
        if (price < 1) {
            $("#result").modal();
            $("#msg").html("输入金额最低1元,请检查");
            return;
        }
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_a/purchase",
                dataType: "json",
                data: {
                    price,
					type
                },
            success: function (data) {
                if (data.ret) {
                    pid = data.pid;
                    $("#ali_qrarea").html('<div class="text-center"><p>请使用支付宝APP扫描二维码支付</p><a id="qrcode" style="padding-top:10px;display:inline-block"></a><p>手机截图相册到支付宝扫码</p></div>');
                    $("#readytopay").modal('hide');
					$("#qr_code").modal();
                    new QRCode("qrcode", {
                        render: "canvas",
                        width: 200,
                        height: 200,
                        text: encodeURI(data.qrcode)
                    });
                    $('#qrcode').attr('href',data.qrcode);
                    setTimeout(f, 1000);
                }else{ 
				    $('#readytopay').modal('hide');
					$("#payresult").modal('hide');
				    $("#result").modal();
				    $("#msg").html(data.msg);
				}
                    
            },
            error: function (jqXHR) {
                console.log(jqXHR);
                $("#readytopay").modal('hide');
				$("#payresult").modal('hide');
                $("#result").modal();
                $("#msg").html("发生错误了: " + jqXHR.status);
            }
			});
		});
	}
	function f(){
		$.ajax({
			type: "POST",
			url: "/payment_a/status",
			dataType: "json",
			data: {
				pid:pid
			},
			success: function (data) {
				if (data.result) {
					console.log(data);
					$("#payresult").modal('hide');
					//$("#result").modal();
                   // $("#msg").html("充值成功！");
					$("#result").modal('hide');
					pay_code();
                  return;
					//window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				}else{
                tid = setTimeout(f, 1000); //循环调用触发setTimeout
                }
			},
			error: function (jqXHR) {
				console.log(jqXHR);
			}
		});
	
	}
//idt支付宝end
<?php echo '</script'; ?>
>
<?php }
if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "idtpay") {
echo '<script'; ?>
>
//idt微信

	function wx_pay(){
	    var type = 'wxpay';
        var price = parseFloat($("#amount").val());
        if (price < 1) {
            $("#result").modal();
            $("#msg").html("输入金额最低1元,请检查");
            return;
        }
		$("#readytopay").modal();
        $("#readytopay").on('shown.bs.modal', function () {
            $.ajax({
                type: "POST",
                url: "/user/payment_b/purchase",
                dataType: "json",
                data: {
                    price,
					type
                },
            success: function (data) {
                if (data.ret) {
                    pid = data.pid;
                    $("#ali_qrarea").html('<div class="text-center"><p>请使用微信APP扫描二维码支付</p><a id="qrcode" style="padding-top:10px;display:inline-block"></a><p>手机截图相册到微信扫码</p></div>');
                    $("#readytopay").modal('hide');
					$("#qr_code").modal();
                    new QRCode("qrcode", {
                        render: "canvas",
                        width: 200,
                        height: 200,
                        text: encodeURI(data.qrcode)
                    });
                    $('#qrcode').attr('href',data.qrcode);
                    setTimeout(f, 1000);
                }else{ 
				    $('#readytopay').modal('hide');
					$("#payresult").modal('hide');
				    $("#result").modal();
				    $("#msg").html(data.msg);
				}
                    
            },
            error: function (jqXHR) {
                console.log(jqXHR);
                $("#readytopay").modal('hide');
				$("#payresult").modal('hide');
                $("#result").modal();
                $("#msg").html("发生错误了: " + jqXHR.status);
            }
			});
		});
    }
    function f(){
		$.ajax({
			type: "POST",
			url: "/payment_b/status",
			dataType: "json",
			data: {
				pid:pid
			},
			success: function (data) {
				if (data.result) {
					console.log(data);
					$("#payresult").modal('hide');
					//$("#result").modal();
                   // $("#msg").html("充值成功！");
					$("#result").modal('hide');
					pay_code();
                  return;
					//window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				}else{
                tid = setTimeout(f, 1000); //循环调用触发setTimeout
                }
			},
			error: function (jqXHR) {
				console.log(jqXHR);
			}
		});
	
	}
//idt微信end
<?php echo '</script'; ?>
>
<?php }
echo '<script'; ?>
>
function pay_code() {
	if(document.getElementById('autorenew').checked){
		var autorenew=1;
	}
	else{
		var autorenew=0;
	}

	if(document.getElementById('disableothers').checked){
		var disableothers=1;
	}
	else{
		var disableothers=0;
	}
		
	$.ajax({
		type: "POST",
		url: "buy",
		dataType: "json",
		data: {
			coupon: $("#coupon").val(),
			shop: shop,
			autorenew: autorenew,
			disableothers:disableothers
		},
		success: function (data) {
			if (data.ret) {
				$("#result").modal();
				$("#msg").html(data.msg);
				window.setTimeout("location.href='/user'", 2000);
			} else {
				$("#result").modal();
				$("#msg").html(data.msg);
			}
		},
		error: function (jqXHR) {
			$("#result").modal();
            $("#msg").html("发生错误了: " + jqXHR.status);
		}
	});
}
<?php echo '</script'; ?>
>
<?php echo '<script'; ?>
>
   $("#rehrefdown").click(function () {
      window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
   });
   $("#rehrefup").click(function () {
      window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
   })
<?php echo '</script'; ?>
><?php }
}
