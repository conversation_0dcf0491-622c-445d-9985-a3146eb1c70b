<?php
/* Smarty version 3.1.33, created on 2022-07-17 17:56:20
  from '/www/wwwroot/www.shadowingy.com/resources/views/czssr/table/js_1.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62d3dcc424ea51_98689575',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'd6ebb311337bb00553fb8c3129beb94fbf3431f4' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.com/resources/views/czssr/table/js_1.tpl',
      1 => 1548128136,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_62d3dcc424ea51_98689575 (Smarty_Internal_Template $_smarty_tpl) {
?>function modify_table_visible(id, key) {
	if(document.getElementById(id).checked)
	{
		table_1.columns( '.' + key ).visible( true );
		localStorage.setItem(window.location.href + '-haschecked-' + id, true);
	}
	else
	{
		table_1.columns( '.' + key ).visible( false );
		localStorage.setItem(window.location.href + '-haschecked-' + id, false);
	}
}
<?php }
}
