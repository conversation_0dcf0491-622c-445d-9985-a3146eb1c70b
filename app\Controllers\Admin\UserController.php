<?php

namespace App\Controllers\Admin;

use App\Models\User;
use App\Models\Level;
use App\Models\NodeGroup;
use App\Models\Ip;
use App\Models\RadiusBan;
use App\Models\Relay;
use App\Models\DetectBanLog;
use App\Utils;
use App\Utils\Hash;
use App\Utils\Radius;
use App\Utils\QQWry;
use App\Utils\Tools;
use App\Models\Shop;
use App\Utils\GA;
use App\Utils\URL;
use App\Models\Link;
use App\Utils\Check;

use App\Services\Mail;
use App\Services\Config;
use App\Services\Auth;
use App\Controllers\AdminController;

class UserController extends AdminController
{
    public function index($request, $response, $args)
    {
        $table_config['total_column'] = array("op" => "操作", "id" => "ID", "user_name" => "用户名",
                            "remark" => "备注", "email" => "邮箱", "money" => "金钱", "is_admin" => "管理员",
							"is_agent" => "代理商", "ssrlink" => "ssr链接",
                            "node_group" => "群组", "expire_in" => "账户过期时间",
                            "class" => "等级", "class_expire" => "等级过期时间",
                            "passwd" => "连接密码","port" => "连接端口", "method" => "加密方式",
                            "protocol" => "连接协议", "obfs" => "连接混淆方式",
                            "online_ip_count" => "在线IP数", "last_ss_time" => "上次使用时间",
                            "used_traffic" => "已用流量/GB", "enable_traffic" => "总流量/GB",
                            "last_checkin_time" => "上次签到时间", "today_traffic" => "今日流量/GB",
                            "enable" => "是否启用", "detect_ban" => "审计封禁", "reg_date" => "注册时间","reg_addr" => "注册地",
                            "reg_ip" => "注册IP", "auto_reset_day" => "自动重置流量日",
                            "auto_reset_bandwidth" => "自动重置流量/GB", "ref_money"=>"邀请余额",
							"invite_num"=>"邀请剩余", "payback_code" => "邀请折扣%", "ref_by" => "邀请人ID", "agent_id" => "代理人",
							"ref_by_user_name" => "邀请人用户名");
        $table_config['default_show_column'] = array("op", "id", "user_name", "remark", "email");
        $table_config['ajax_url'] = 'user/ajax';
     	$Shops = Shop::where("status", 1)->orderBy("id", "asc")->get();
	    $Levelist = Level::select('id','level','name')->where('status',1)->get();
        return $this->view()
          ->assign('table_config', $table_config)
          ->assign('shop_name', $Shops)
          ->assign('Levelist', $Levelist)
          ->display('admin/user/index.tpl');
    }

    public function search($request, $response, $args)
    {
        $pageNum = 1;
        $text=$args["text"];
        if (isset($request->getQueryParams()["page"])) {
            $pageNum = $request->getQueryParams()["page"];
        }

        $users = User::where("email", "LIKE", "%".$text."%")->orWhere("user_name", "LIKE", "%".$text."%")->orWhere(/*"im_value", */"LIKE", "%".$text."%")->orWhere("port", "LIKE", "%".$text."%")->orWhere("remark", "LIKE", "%".$text."%")->paginate(20, ['*'], 'page', $pageNum);
        $users->setPath('/admin/user/search/'.$text);

        //Ip::where("datetime","<",time()-90)->get()->delete();
        $total = Ip::where("datetime", ">=", time()-90)->orderBy('userid', 'desc')->get();


        $userip=array();
        $useripcount=array();
        $regloc=array();

        $iplocation = new QQWry();
        foreach ($users as $user) {
            $useripcount[$user->id]=0;
            $userip[$user->id]=array();

            $location=$iplocation->getlocation($user->reg_ip);
            $regloc[$user->id]=iconv('gbk', 'utf-8//IGNORE', $location['country'].$location['area']);
        }



        foreach ($total as $single) {
            if (isset($useripcount[$single->userid])) {
                if (!isset($userip[$single->userid][$single->ip])) {
                    $useripcount[$single->userid]=$useripcount[$single->userid]+1;
                    $location=$iplocation->getlocation($single->ip);
                    $userip[$single->userid][$single->ip]=iconv('gbk', 'utf-8//IGNORE', $location['country'].$location['area']);
                }
            }
        }


        return $this->view()->assign('users', $users)->assign("regloc", $regloc)->assign("useripcount", $useripcount)->assign("userip", $userip)->display('admin/user/index.tpl');
    }

    public function sort($request, $response, $args)
    {
        $pageNum = 1;
        $text=$args["text"];
        $asc=$args["asc"];
        if (isset($request->getQueryParams()["page"])) {
            $pageNum = $request->getQueryParams()["page"];
        }

        $users->setPath('/admin/user/sort/'.$text."/".$asc);

        //Ip::where("datetime","<",time()-90)->get()->delete();
        $total = Ip::where("datetime", ">=", time()-90)->orderBy('userid', 'desc')->get();


        $userip=array();
        $useripcount=array();
        $regloc=array();

        $iplocation = new QQWry();
        foreach ($users as $user) {
            $useripcount[$user->id]=0;
            $userip[$user->id]=array();

            $location=$iplocation->getlocation($user->reg_ip);
            $regloc[$user->id]=iconv('gbk', 'utf-8//IGNORE', $location['country'].$location['area']);
        }

        foreach ($total as $single) {
            if (isset($useripcount[$single->userid])) {
                if (!isset($userip[$single->userid][$single->ip])) {
                    $useripcount[$single->userid]=$useripcount[$single->userid]+1;
                    $location=$iplocation->getlocation($single->ip);
                    $userip[$single->userid][$single->ip]=iconv('gbk', 'utf-8//IGNORE', $location['country'].$location['area']);
                }
            }
        }


        return $this->view()->assign('users', $users)->assign("regloc", $regloc)->assign("useripcount", $useripcount)->assign("userip", $userip)->display('admin/user/index.tpl');
    }

    public static function GenerateRandomLink()
    {
        $i =0;
        for ($i = 0; $i < 10; $i++) {
            $token = Tools::genRandomChar(16);
            $Elink = User::where("ssrlink", "=", $token)->first();
            if ($Elink == null) {
                return $token;
            }
        }

        return "couldn't alloc token";
    }

    public static function GenerateSSRSubCode($adduserid, $without_mu)
    {
        $Elink = Link::where("type", "=", 11)->where("userid", "=", $adduserid)->where("geo", $without_mu)->first();
        if ($Elink != null) {
            return $Elink->token;
        }
        $NLink = new Link();
        $NLink->type = 11;
        $NLink->address = "";
        $NLink->port = 0;
        $NLink->ios = 0;
        $NLink->geo = $without_mu;
        $NLink->method = "";
        $NLink->userid = $adduserid;
        $NLink->token = UserController::GenerateRandomLink();
        $NLink->save();

        return $NLink->token;
    }
   //单个用户创建(管理员)
  	public function addUser($request, $response, $args)
    {
        $userName = $request->getParam('userName');
      	$email = $request->getParam('email');
        $shopId = $request->getParam('shopId');
      	$agentid = $request->getParam('agentid');
      
        if (!Check::isEmailLegal($email)) {
            $rs['ret'] = 0;
            $rs['msg'] = "邮箱无效";
            return $response->getBody()->write(json_encode($rs));
        }
        $DbUser = User::where('email', '=', $email)->first();
        if ($DbUser != null) {
            $rs['ret'] = 0;
            $rs['msg'] = "此邮箱已经注册";
            return $response->getBody()->write(json_encode($rs));
        }
      
        $user = new User();
        $user->user_name = $userName;
        $user->email = $email;
        $user->pass = Hash::passwordHash($email);
        $user->passwd = Tools::genRandomChar(6);
        $user->t = 0;
        $user->u = 0;
        $user->d = 0;
      	$user->plan = 'A';
      	$user->transfer_enable = Tools::toGB(Config::get('defaultTraffic'));
        $user->port = Tools::getAvPort();
      	$user->switch = '1';
      	$user->enable = '1';
      	$user->type = '1';
      	$user->last_get_gift_time = '0';
      	$user->last_check_in_time = '0';
      	$user->last_rest_pass_time = '0';
      	$user->reg_date = date("Y-m-d H:i:s");
      	$user->invite_num = Config::get('inviteNum');
		$user->payback_code = Config::get('code_payback');
      	$user->money = '0';
      	$user->ref_by = '0';
      	$user->expire_time = '0';
        $user->method = Config::get('reg_method');
      	$user->is_email_verify = '0';
      	$user->reg_ip = $_SERVER["REMOTE_ADDR"];
      	$user->node_speedlimit = Config::get('user_speedlimit');
      	$user->node_connector = Config::get('user_conn');
      	$user->is_admin = '0';
        $user->im_type = '1';
        $user->im_value = null;
      	$user->last_day_t = '0';
      	$user->sendDailyMail = '0';
      	$user->class = Config::get('user_class_default');
      	$user->class_expire = date("Y-m-d H:i:s", time() + Config::get('user_class_expire_default') * 3600);
        $user->expire_in = date("Y-m-d H:i:s", time() + Config::get('user_expire_in_default') * 86400);
      	$user->theme = Config::get('theme');
      
        $ga = new GA();
        $secret = $ga->createSecret();
        $user->ga_token = $secret;
        $user->ga_enable = 0;
      
      	$user->pac = null;
      	$user->remark = null;
      
        $ramdom_group=array(Config::get('ramdom_group'));
        $groups=array_rand($ramdom_group,1);
      
        $user->node_group=$ramdom_group[$groups];
      	
      	$user->auto_reset_day = Config::get('reg_auto_reset_day');
      	$user->auto_reset_bandwidth = Config::get('reg_auto_reset_bandwidth');
        $user->protocol = Config::get('reg_protocol');
        $user->protocol_param = Config::get('reg_protocol_param');
        $user->obfs = Config::get('reg_obfs');
        $user->obfs_param = Config::get('reg_obfs_param');
        $user->forbidden_ip = Config::get('reg_forbidden_ip');
        $user->forbidden_port = Config::get('reg_forbidden_port');
        $user->disconnect_ip = null;
      	$user->is_hide = 0;
      	$user->is_multi_user = 0;
      	$user->telegram_id = null;
        
        $user->is_agent = 0;
        $user->agent_id = $agentid;
        //用户注册成功之后才执行添加套餐的操作
        if ($user->save()) {
            $ssr_sub_token = UserController::GenerateSSRSubCode($user->id,0);
            User::where('id', $user->id)->update(['ssrlink' => $ssr_sub_token]);
            $shopId = $request->getParam('shopId');
            $shop = Shop::where("id", $shopId)->where("status", 1)->first();
            $shop->buy($user);
            $rs['ret'] = 1;
            $rs['msg'] = "添加成功";
            return $response->getBody()->write(json_encode($rs));
           }
    }

    public function addclass($request, $response, $args)
    {
     	$user = User::find($id);
        $vip = $request->getParam('vip');
        $change_class = $request->getParam('change_class');

        $class_h = $request->getParam('class_h');
        $users = User::where('class', $vip)->where('enable', 1)->get();
          
          if ($class_h <= 0) {
             $res['ret'] = 0;
             $res['msg'] = "时长数值有误,请检查";
             return $response->getBody()->write(json_encode($res));  
            }
      
        foreach($users as $user){
		  if ($change_class == 1) {
            if ($user->class != 0) {
            	$user->class_expire=date("Y-m-d H:i:s", strtotime($user->class_expire)+$class_h*3600);
            } else {
            	$user->class_expire=date("Y-m-d H:i:s", time()+$class_h*3600);
            }
            $user->save();
          }else{
            if (time()>strtotime($user->expire_in)) {
            	$user->expire_in=date("Y-m-d H:i:s", time()+$class_h*3600);
            } else {
            	$user->expire_in=date("Y-m-d H:i:s", strtotime($user->expire_in)+$class_h*3600);
            }//如果当前时间>账户到期时间(已过期)，那么现在时间加上套餐的时间；否则在账户原有基础时间上相加   
            $user->save();
          }
          
        }
        $res['ret'] = 1;
        $res['msg'] = $vip." VIP等级时长批量增加完毕, 请查阅.";
        return $response->getBody()->write(json_encode($res)); 
      
    }
    public function addtraffic($request, $response, $args)
    {
     	$user = User::find($id);
        $vip = $request->getParam('vip');
        $user_traffic = $request->getParam('user_traffic');
        $users = User::where('class', $vip)->where('enable', 1)->get();
          
          if ($user_traffic <= 0) {
             $res['ret'] = 0;
             $res['msg'] = "流量数值有误,请检查";
             return $response->getBody()->write(json_encode($res));  
            }
      
        foreach($users as $user){
            $user->transfer_enable += $user_traffic * 1073741824;
            $user->save();
        }
        $res['ret'] = 1;
        $res['msg'] = $vip." VIP等级流量批量增加完毕, 请查阅.";
        return $response->getBody()->write(json_encode($res)); 
      
    }
    public function addmoney($request, $response, $args)
    {
     	$user = User::find($id);
        $vip = $request->getParam('vip');
        $user_money = $request->getParam('user_money');
        $users = User::where('class', $vip)->where('enable', 1)->get();
          
          if ($user_money <= 0) {
             $res['ret'] = 0;
             $res['msg'] = "金额数值有误,请检查";
             return $response->getBody()->write(json_encode($res));  
            }
      
        foreach($users as $user){
            $user->money += $user_money;
            $user->save();
        }
        $res['ret'] = 1;
        $res['msg'] = $vip." VIP等级余额批量增加完毕, 请查阅.";
        return $response->getBody()->write(json_encode($res)); 
      
    }
    public function sendemail($request, $response, $args)
    {
        $change_expire = $request->getParam('change_expire');//1=账号等级有效期 0=账号有效期
        $overdue = $request->getParam('overdue');//1=7天到期  0=已经到期
        // $beginSend = (int)($request->getParam('page') - 1) * Config::get('sendPageLimit');
		$this->sendMail($overdue, $change_expire);
		
        $rs['ret'] = 1;
		$rs['msg'] = "邮件发送成功(静默发送).";
        return $response->getBody()->write(json_encode($rs));
      
    }
	private function sendMail($overdue, $change_expire)
    {
		ignore_user_abort(true); // 后台运行，不受前端断开连接影响
		set_time_limit(7200); // 脚本最多运行2个小时
		
		$tim=7*86400+time();
        $time=date("Y-m-d H:i:s",time());//现在
        $timea=date("Y-m-d H:i:s",$tim);//7天
		
		if ($overdue==1) {
          if ($change_expire==1){
              $userCount=User::where("class_expire",">=",$time)->where("class_expire","<=",$timea)->count();
          }elseif($change_expire==0){
              $userCount=User::where("expire_in",">=",$time)->where("expire_in","<=",$timea)->count();
          }
        }else{
          if ($change_expire==1){
              $userCount=User::where("class_expire","<",$time)->count();
            }elseif($change_expire==0){
              $userCount=User::where("expire_in","<",$time)->count();
            }
        }
		
		if($userCount > 0){
			for($i = 0; $i < $userCount; $i+=Config::get('sendPageLimit')){
				if ($overdue==1) {
					if ($change_expire==1){
						$users=User::where("class_expire",">=",$time)->where("class_expire","<=",$timea)->skip($i)->limit(Config::get('sendPageLimit'))->get();
						$content = "您好,您的账户等级有效期即将到期, 请及时续费，请登录网站联系客服赠送7天10G免费体验，QQ群:748432512";
					}elseif($change_expire==0){
						$users=User::where("expire_in",">=",$time)->where("expire_in","<=",$timea)->skip($i)->limit(Config::get('sendPageLimit'))->get();
						$content = "您好,您的账户有效期即将到期, 逾期将被停止使用, 请注意使用情况.续费请登录网站联系客服赠送7天10G免费体验，QQ群:748432512";
					}
				}else{
					if ($change_expire==1){
						$users=User::where("class_expire","<",$time)->skip($i)->limit(Config::get('sendPageLimit'))->get();
						$content = "您好,您的账户等级有效期已经过期, 如需继续使用请登录网站联系客服赠送7天10G免费体验，QQ群:748432512";
					}elseif($change_expire==0){
						$users=User::where("expire_in","<",$time)->skip($i)->limit(Config::get('sendPageLimit'))->get();
						$content = "您好,您的账户有效期已经到期, 如需继续使用请及时续费.请登录网站联系客服赠送7天10G免费体验，QQ群:748432512";
					}
				}
			foreach($users as $user){
					$subject = Config::get('appName')."-通知";
					$to = $user->email;
					
					if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
						continue;
					}
					$text = $content;
					try {
						Mail::send($to , $subject, 'news/warn.tpl', [
								"user" => $user,"text" => $text
						], [
						]);  
						// sleep(1);
					} catch (\Exception $e) {
						continue;
					}
				}
			}
		}
    }
    public function edit($request, $response, $args)
    {
        $id = $args['id'];
        $user = User::find($id);
        if ($user == null) {
        }
        $groupList = NodeGroup::select('id','name','level')->orderBy('id')->get(); //获取节点群组名称的列表
        $levelList = Level::select('id','name','level')->orderBy('id')->get(); //获取等级名称的列表
        return $this->view()->assign(array('levelList' => $levelList, 'edit_user' => $user, 'groupList' => $groupList))->display('admin/user/edit.tpl');
    }

    public function update($request, $response, $args)
    {
        $id = $args['id'];
        $user = User::find($id);

        $email1=$user->email;

        $user->email = $request->getParam('email');

        $email2=$request->getParam('email');

        $passwd=$request->getParam('passwd');

        Radius::ChangeUserName($email1, $email2, $passwd);


        if ($request->getParam('pass') != '') {
            $user->pass = Hash::passwordHash($request->getParam('pass'));
            $user->clean_link();
        }

        $user->auto_reset_day =  $request->getParam('auto_reset_day');
        $user->auto_reset_bandwidth = $request->getParam('auto_reset_bandwidth');
        $origin_port = $user->port;
        $user->port =  $request->getParam('port');

        $relay_rules = Relay::where('user_id', $user->id)->where('port', $origin_port)->get();
        foreach ($relay_rules as $rule) {
            $rule->port = $user->port;
            $rule->save();
        }

        $user->passwd = $request->getParam('passwd');
        $user->protocol = $request->getParam('protocol');
        $user->protocol_param = $request->getParam('protocol_param');
        $user->obfs = $request->getParam('obfs');
        $user->obfs_param = $request->getParam('obfs_param');
        $user->is_multi_user = $request->getParam('is_multi_user');
        $user->transfer_enable = Tools::toGB($request->getParam('transfer_enable'));
        $user->invite_num = $request->getParam('invite_num');
		$user->payback_code = $request->getParam('payback_code');
        $user->method = $request->getParam('method');
        $user->node_speedlimit = $request->getParam('node_speedlimit');
        $user->node_connector = $request->getParam('node_connector');
        $user->enable = $request->getParam('enable');
        $user->is_admin = $request->getParam('is_admin');
      	$user->is_agent = $request->getParam('is_agent');
        $user->creta = $request->getParam('creta');
        $user->ga_enable = $request->getParam('ga_enable');
        $user->node_group = $request->getParam('group');
        $user->ref_by = $request->getParam('ref_by');
        $user->remark = $request->getParam('remark');
        $user->money = $request->getParam('money');
        $user->class = $request->getParam('class');
        $user->class_expire = $request->getParam('class_expire');
        $user->expire_in = $request->getParam('expire_in');

        $user->forbidden_ip = str_replace(PHP_EOL, ",", $request->getParam('forbidden_ip'));
        $user->forbidden_port = str_replace(PHP_EOL, ",", $request->getParam('forbidden_port'));
        $detect_ban = (int)$request->getParam('detect_ban');
        // 手动解封
        if ($user->detect_ban == 1 && $detect_ban == 0) {
            $user->detect_ban = 0;
        }
        // 手动封禁
        if ($user->detect_ban == 0 && $detect_ban == 1) {
            if ((int)$request->getParam('ban_time') == 0) {
                $rs['ret'] = 0;
                $rs['msg'] = "修改失败，封禁时长不能为 0";
                return $response->getBody()->write(json_encode($rs));
            }
            $user->detect_ban = 1;
            $end_time = date('Y-m-d H:i:s');
            $user->last_detect_ban_time = $end_time;
            $DetectBanLog = new DetectBanLog();
            $DetectBanLog->user_name = $user->user_name;
            $DetectBanLog->user_id = $user->id;
            $DetectBanLog->email = $user->email;
            $DetectBanLog->detect_number = '0';
            $DetectBanLog->ban_time = (int)$request->getParam('ban_time');
            $DetectBanLog->start_time = strtotime('1989-06-04 00:05:00');
            $DetectBanLog->end_time = strtotime($end_time);
            $DetectBanLog->all_detect_number = $user->all_detect_number;
            $DetectBanLog->save();
        }
        if (!$user->save()) {
            $rs['ret'] = 0;
            $rs['msg'] = "修改失败";
            return $response->getBody()->write(json_encode($rs));
        }
        $rs['ret'] = 1;
        $rs['msg'] = "修改成功";
        return $response->getBody()->write(json_encode($rs));
    }

    public function delete($request, $response, $args)
    {
        $id = $request->getParam('id');
        $user = User::find($id);

        $email1=$user->email;

        if (!$user->kill_user($user->id,$user->email)) {
            $rs['ret'] = 0;
            $rs['msg'] = "删除失败";
            return $response->getBody()->write(json_encode($rs));
        }
        $rs['ret'] = 1;
        $rs['msg'] = "删除成功";
        return $response->getBody()->write(json_encode($rs));
    }
    
    public function changetouser($request, $response, $args)
    {
        $userid = $request->getParam('userid');
        $adminid = $request->getParam('adminid');
        $user = User::find($userid);
        $admin = User::find($adminid);
        $expire_in = time()+60*60;
      
        if (!$admin->is_admin || !$user || !Auth::getUser()->isLogin) {
            $rs['ret'] = 0;
            $rs['msg'] = "非法请求";
            return $response->getBody()->write(json_encode($rs));
        }
        
        Utils\Cookie::set([
            "uid" => $user->id,
            "email" => $user->email,
            "key" => Hash::cookieHash($user->pass),
            "ip" => md5($_SERVER["REMOTE_ADDR"].Config::get('key').$user.$expire_in),
            "expire_in" =>  $expire_in,
            "old_uid" => Utils\Cookie::get('uid'),
            "old_email" => Utils\Cookie::get('email'),
            "old_key" => Utils\Cookie::get('key'),
            "old_ip" => Utils\Cookie::get('ip'),
            "old_expire_in" => Utils\Cookie::get('expire_in'),
            "old_local" =>  $request->getParam('local')
        ],  $expire_in);
        $rs['ret'] = 1;
        $rs['msg'] = "切换成功";
        return $response->getBody()->write(json_encode($rs));
    }

	public function ajax($request, $response, $args)
	{		
       //得到排序的方式
        $order = $request->getParam('order')[0]['dir'];
        //得到排序字段的下标
        $order_column = $request->getParam('order')[0]['column'];
        //根据排序字段的下标得到排序字段
        $order_field = $request->getParam('columns')[$order_column]['data'];
        $limit_start = $request->getParam('start');
        $limit_length = $request->getParam('length');
        $search = $request->getParam('search')['value'];

        if ($order_field == 'used_traffic') {
            $order_field = 'u + d';
        } elseif ($order_field == 'enable_traffic') {
            $order_field = 'transfer_enable';
        } elseif ($order_field == 'today_traffic') {
            $order_field = 'u +d - last_day_t';
        }

        $users = array();
        $count_filtered = 0;

        if ($search) {
            $users = User::where(
                static function ($query) use ($search) {
                    $query->where('id', 'LIKE binary', "%$search%")
                        ->orwhere('user_name', 'LIKE binary', "%$search%")
                        ->orwhere('email', 'LIKE binary', "%$search%")
                        ->orwhere('passwd', 'LIKE binary', "%$search%")
                        ->orwhere('port', 'LIKE binary', "%$search%")
                        ->orwhere('reg_date', 'LIKE binary', "%$search%")
                        ->orwhere('invite_num', 'LIKE binary', "%$search%")
                        ->orwhere('money', 'LIKE binary', "%$search%")
                        ->orwhere('ssrlink', 'LIKE binary', "%$search%")
                        ->orwhere('ref_by', 'LIKE binary', "%$search%")
                        ->orwhere('agent_id', 'LIKE binary', "%$search%")
                        ->orwhere('method', 'LIKE binary', "%$search%")
                        ->orwhere('reg_addr', 'LIKE binary', "%$search%")
                        ->orwhere('reg_ip', 'LIKE binary', "%$search%")
                        ->orwhere('node_speedlimit', 'LIKE binary', "%$search%")
                        ->orwhere('im_value', 'LIKE binary', "%$search%")
                        ->orwhere('class', 'LIKE binary', "%$search%")
                        ->orwhere('class_expire', 'LIKE binary', "%$search%")
                        ->orwhere('expire_in', 'LIKE binary', "%$search%")
                        ->orwhere('remark', 'LIKE binary', "%$search%")
                        ->orwhere('node_group', 'LIKE binary', "%$search%")
                        ->orwhere('auto_reset_day', 'LIKE binary', "%$search%")
                        ->orwhere('auto_reset_bandwidth', 'LIKE binary', "%$search%")
                        ->orwhere('protocol', 'LIKE binary', "%$search%")
                        ->orwhere('protocol_param', 'LIKE binary', "%$search%")
                        ->orwhere('obfs', 'LIKE binary', "%$search%")
                        ->orwhere('obfs_param', 'LIKE binary', "%$search%");
                }
            )
                ->orderByRaw($order_field . ' ' . $order)
                ->skip($limit_start)->limit($limit_length)
                ->get();
            $count_filtered = User::where(
                static function ($query) use ($search) {
                    $query->where('id', 'LIKE binary', "%$search%")
                        ->orwhere('user_name', 'LIKE binary', "%$search%")
                        ->orwhere('email', 'LIKE binary', "%$search%")
                        ->orwhere('passwd', 'LIKE binary', "%$search%")
                        ->orwhere('port', 'LIKE binary', "%$search%")
                        ->orwhere('reg_date', 'LIKE binary', "%$search%")
                        ->orwhere('invite_num', 'LIKE binary', "%$search%")
                        ->orwhere('money', 'LIKE binary', "%$search%")
                        ->orwhere('ssrlink', 'LIKE binary', "%$search%")
                        ->orwhere('ref_by', 'LIKE binary', "%$search%")
                        ->orwhere('agent_id', 'LIKE binary', "%$search%")
                        ->orwhere('method', 'LIKE binary', "%$search%")
                        ->orwhere('reg_addr', 'LIKE binary', "%$search%")
                        ->orwhere('reg_ip', 'LIKE binary', "%$search%")
                        ->orwhere('node_speedlimit', 'LIKE binary', "%$search%")
                        ->orwhere('im_value', 'LIKE binary', "%$search%")
                        ->orwhere('class', 'LIKE binary', "%$search%")
                        ->orwhere('class_expire', 'LIKE binary', "%$search%")
                        ->orwhere('expire_in', 'LIKE binary', "%$search%")
                        ->orwhere('remark', 'LIKE binary', "%$search%")
                        ->orwhere('node_group', 'LIKE binary', "%$search%")
                        ->orwhere('auto_reset_day', 'LIKE binary', "%$search%")
                        ->orwhere('auto_reset_bandwidth', 'LIKE binary', "%$search%")
                        ->orwhere('protocol', 'LIKE binary', "%$search%")
                        ->orwhere('protocol_param', 'LIKE binary', "%$search%")
                        ->orwhere('obfs', 'LIKE binary', "%$search%")
                        ->orwhere('obfs_param', 'LIKE binary', "%$search%");
                }
            )->count();
        } else {
            $users = User::orderByRaw($order_field . ' ' . $order)
                ->skip($limit_start)->limit($limit_length)
                ->get();
            $count_filtered = User::count();
        }
        $data = array();
		foreach ($users as $user) {
			$tempdata=array();
			//model里是casts所以没法直接 $tempdata=(array)$user
			$tempdata['op']='<a class="btn btn-primary btn-sm" href="/admin/user/'.$user->id.'/edit">编辑</a>
                    <a class="btn btn-secondary btn-sm" id="delete" href="javascript:void(0);" onClick="delete_modal_show(\''.$user->id.'\')">删除</a>
                    <a class="btn btn-primary btn-sm" id="changetouser" href="javascript:void(0);" onClick="changetouser_modal_show(\''.$user->id.'\')">切换为该用户</a>';;
			$tempdata['id']=$user->id;
			$tempdata['user_name']=$user->user_name;
			$tempdata['remark']=$user->remark;
			$tempdata['email']=$user->email;
			$tempdata['money']=$user->money;
            $tempdata['is_admin']=$user->is_admin;
            $tempdata['is_agent']=$user->is_agent;
            $tempdata['ssrlink']=$user->ssrlink;
            $group = NodeGroup::where('level',$user->node_group)->first();//获取群组名称的列表
			$tempdata['node_group']=$group->name;
			$tempdata['expire_in']=$user->userexpirexp($user->id);
			$level = Level::where('level', $user->class)->orderBy('id')->first(); //获取等级名称的列表    
			$tempdata['class']=$level->name;
			$tempdata['class_expire']=$user->userclassexp($user->id);
			$tempdata['passwd']=$user->passwd;
			$tempdata['port']=$user->port;
			$tempdata['method']=$user->method;
			$tempdata['protocol']=$user->protocol;
			$tempdata['obfs']=$user->obfs;
			$tempdata['online_ip_count']=$user->online_ip_count();
			$tempdata['last_ss_time']=$user->lastSsTime();
			$tempdata['used_traffic']=round(Tools::flowToGB($user->u + $user->d),2);
			$tempdata['enable_traffic']=round(Tools::flowToGB($user->transfer_enable),2);
			$tempdata['last_checkin_time']=$user->lastCheckInTime();
			$tempdata['today_traffic']=round(Tools::flowToGB($user->u + $user->d-$user->last_day_t),2);
			$tempdata['enable']=$user->enable == 1 ? "可用" : "禁用";
			$tempdata['detect_ban']=$user->detect_ban == 1 ? "已禁" : "未禁";
			$tempdata['reg_date']=$user->reg_date;
            if(!isset($user->reg_addr)){
               $localtion=$user->getlocation($user->reg_ip);
               User::where('id',$user->id)->update(['reg_addr' => $localtion]);
            }
            $tempdata['reg_addr']=$user->reg_addr;
			$tempdata['reg_ip']=$user->reg_ip;
			$tempdata['auto_reset_day']=$user->auto_reset_day;
			$tempdata['auto_reset_bandwidth']=$user->auto_reset_bandwidth;
            $tempdata['ref_money']=$user->ref_money;
            $tempdata['invite_num'] = $user->invite_num;
			$tempdata['payback_code'] = $user->payback_code;
            $tempdata['ref_by']= $user->ref_by;
            $tempdata['agent_id']= $user->agent_id;
			if ($user->ref_by == 0) {
				$tempdata['ref_by_user_name'] = "系统邀请";
			}
			else {
				$ref_user = User::find($user->ref_by);
				if ($ref_user == null) {
					$tempdata['ref_by_user_name'] = "邀请人已经被删除";
				}
				else {
					$tempdata['ref_by_user_name'] = $ref_user->user_name;
				}
			}
         
			array_push($data,$tempdata);
		}         

        $info = [
           'draw'=> $request->getParam('draw'), // ajax请求次数，作为标识符
           'recordsTotal'=>User::count(),
           'recordsFiltered'=>$count_filtered,
           'data'=>$data,
        ];
        return json_encode($info,true);
	}
}
