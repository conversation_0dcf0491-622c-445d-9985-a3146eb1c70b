{include file='user/main.tpl'}
<style>
   .doudong:hover { transform: translateY(-2px);}
</style>
     <!-- Header -->
	<div class="header pb-6 d-flex align-items-center" style="min-height: 500px; background-image: url(/theme/czssr/main/picture/profile-cover.jpg); background-size: cover; background-position: center top;">
      <!-- Mask -->
      <span class="mask bg-gradient-default opacity-8"></span>
      <!-- Header container -->
      <div class="container-fluid d-flex align-items-center">
        <div class="row">
          <div class="col-lg-7 col-md-10">
            <h1 class="display-2 text-white">Hello {$user->user_name}</h1>
            <p class="text-white mt-0 mb-5">This is your profile page. You can see the progress you've made with your work and manage your projects or assigned tasks</p>
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Profile</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">我的账号</li>
                </ol>
              </nav>
            </div>
			
          </div>
        </div>
      </div>
    </div>
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <div class="row">
	    <div class="col-xl-4 order-xl-2">
          <div class="card card-profile">
            <img src="/theme/czssr/main/picture/img-1-1000x600.jpg" alt="Image placeholder" class="card-img-top">
            <div class="row justify-content-center">
              <div class="col-lg-3 order-lg-2">
                <div class="card-profile-image">
                  <a href="#">
                    <img src="/theme/czssr/main/picture/team-4.jpg" class="rounded-circle">
                  </a>
                </div>
              </div>
            </div>
			<div class="card-header text-center border-0 pt-8 pt-md-4 pb-0 pb-md-4">
				<div class="d-flex justify-content-between">
					<a href="/user/node" class="btn btn-sm btn-info mr-4">查看节点</a>
					<a href="/user/shop" class="btn btn-sm btn-default float-right">前往商店</a>
				</div>
			</div>
            <div class="card-body pt-0">
              <div class="row">
                <div class="col">
				
                  <div class="card-profile-stats d-flex justify-content-center">
                    <div>
                      <span class="heading">{$user->money}</span>
                      <span class="description">Money</span>
                    </div>
                    <div>
                      <span class="heading">{$user->unusedTraffic()}</span>
                      <span class="description">Traffic</span>
                    </div>
                    <div>
                      <span class="heading">Lv.{$level->level}</span>
                      <span class="description">Class</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="text-center">
                <h5 class="h3">{$user->user_name}</h5>
				{if $config['enable_kill'] == "true"}
				<a href="kill" class="btn btn-sm btn-danger">删除账号</a>
				{/if}
              </div>
            </div>
          </div>
        </div>
		
        <div class="col-xl-8 order-xl-1">
          <div class="row">
            <div class="col-lg-6 doudong">
              <div class="card bg-gradient-danger border-0">
                <!-- Card body --><a href="javascript:void(0);" data-toggle="modal" data-target="#pwd-up">
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <span class="h2 font-weight-bold mb-0 text-white">修改登陆密码</span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-white text-dark rounded-circle shadow">
                        <i class="ni ni-active-40"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-white mr-2"><i class="ni ni-lock-circle-open icon-ver"></i>&nbsp;定期修改为高强度密码以保护您的账号.</span>
                  </p>
                </div>
				</a>
              </div>
            </div>
            <div class="col-lg-6 doudong">
              <div class="card bg-gradient-Secondary border-0">
                <!-- Card body --><a href="javascript:void(0);" data-toggle="modal" data-target="#telegram">
                <div class="card-body">
                  <div class="row">
                    <div class="col">
					{if $user->telegram_id == null}
                      <span class="h2 font-weight-bold mb-0">绑定TG账号</span>
					{else}
					  <span class="h4 font-weight-bold mb-0">当前绑定TG账号:&nbsp;{$user->telegram_id}</span>
					{/if}
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-white text-dark rounded-circle shadow">
                        <i class="ni ni-send"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="mr-2"><i class="ni ni-send icon-ver"></i>&nbsp;绑定后可使用 Telegram 快速登录网站.</span>
                  </p>
                </div>
				</a>
              </div>
            </div>
          </div>
		  
		  <div class="row">
            <div class="col-lg-6 doudong">
              <div class="card bg-gradient-Info border-0">
                <!-- Card body --><a href="javascript:void(0);" data-toggle="modal" data-target="#wechat-up">
				{if $user->im_value == null}
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <span class="h2 font-weight-bold mb-0 text-white">添加联系方式</span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-white text-dark rounded-circle shadow">
                        <i class="ni ni-chat-round"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-white mr-2"><i class="ni ni-single-copy-04 icon-ver"></i>&nbsp;添加你联系方式以便客服联系你.</span>
                  </p>
                </div>
				{else}
				<div class="card-body">
                  <div class="row">
                    <div class="col">
                      <span class="h2 font-weight-bold mb-0 text-white">修改联系方式</span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-white text-dark rounded-circle shadow">
                        <i class="ni ni-active-40"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-white mr-2"><i class="ni ni-active-40 icon-ver"></i>&nbsp;当前的联系方式是&nbsp;:&nbsp;{$user->im_value}</span>
                  </p>
                </div>
				{/if}
				</a>
              </div>
            </div>
            <div class="col-lg-6 doudong">
              <div class="card bg-gradient-Success border-0">
                <!-- Card body --><a href="javascript:void(0);" data-toggle="modal" data-target="#theme-up">
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <span class="h2 font-weight-bold mb-0 text-white">主题修改</span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-white text-dark rounded-circle shadow">
                        <i class="ni ni-palette"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-white mr-2"><i class="ni ni-palette icon-ver"></i>&nbsp;你当前使用的主题&nbsp;:&nbsp;{$user->theme}</span>
                  </p>
                </div>
				</a>
              </div>
            </div>
          </div>
		  
		  <div class="row">
            <div class="col-lg-6 doudong">
              <div class="card bg-gradient-Primary border-0">
                <!-- Card body --><a href="javascript:void(0);" data-toggle="modal" data-target="#ajax-email">
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <span class="h2 font-weight-bold mb-0 text-white">每日邮件提醒设置</span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-white text-dark rounded-circle shadow">
                        <i class="ni ni-email-83"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-white mr-2"><i class="ni ni-email-83 icon-ver"></i>&nbsp;当前的设置&nbsp;:&nbsp;{if $user->sendDailyMail==1}发送{else}不发送{/if}</span>
                  </p>
                </div>
				</a>
              </div>
            </div>
            <div class="col-lg-6 doudong">
              <div class="card bg-gradient-Default border-0">
                <!-- Card body --><a href="javascript:void(0);" data-toggle="modal" data-target="#unblock-ip">
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <span class="h2 font-weight-bold mb-0 text-white">IP 解封</span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-white text-dark rounded-circle shadow">
                        <i class="ni ni-button-power"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-white mr-2"><i class="ni ni-button-power icon-ver"></i>&nbsp;当前的状态&nbsp;:&nbsp;{$Block}</span>
                  </p>
                </div>
				</a>
              </div>
            </div>
          </div>
        </div>

	  </div>
	  
	  
	  <div class="row">
		<div class="col">
		  <div class="card">
            <!-- Card header -->
            <div class="card-header border-0">
              <h3 class="mb-0">最近五分钟使用IP</h3>
            </div>
            <!-- Light table -->
            <div class="table-responsive">
              <table class="table align-items-center table-flush">
                <thead class="thead-light">
                  <tr>
                    <th scope="col" class="sort">IP地址</th>
                    <th scope="col" class="sort">归属地</th>
                  </tr>
                </thead>
                <tbody class="list">
				{foreach $userip as $single=>$location}
                  <tr>
                    <th scope="row">
                      <div class="media align-items-center">
                        <div class="media-body">
                          <span class="name mb-0 text-sm">{$single}</span>
                        </div>
                      </div>
                    </th>
                    <td>
                      <span class="badge badge-dot mr-4">
                        <i class="bg-warning"></i>
                        <span class="status">{$location}</span>
                      </span>
                    </td>
				  </tr>
				{/foreach}
                </tbody>
              </table>
            </div>
          </div><!--card-->
		  
		  <div class="card">
            <!-- Card header -->
            <div class="card-header border-0">
              <h3 class="mb-0">最近十次登录IP</h3>
            </div>
            <!-- Light table -->
            <div class="table-responsive">
              <table class="table align-items-center table-flush">
                <thead class="thead-light">
                  <tr>
                    <th scope="col" class="sort">IP地址</th>
                    <th scope="col" class="sort">归属地</th>
                    <th scope="col" class="sort">登陆时间</th>
                  </tr>
                </thead>
                <tbody class="list">
				{foreach $userloginip as $single=>$location}
                  <tr>
                    <td scope="row">
                      <span class="badge badge-dot mr-4">
                        <i class="bg-warning"></i>
                        <span class="status">{$single}</span>
                      </span>
                    </td>
                    <td>
                      <span class="badge badge-dot">
                        <i class="bg-warning"></i>
                        <span class="status">{$location["location"]}</span>
                      </span>
                    </td>
                    <td>
                      <span class="badge badge-dot">
                        <i class="bg-warning"></i>
                        <span class="status">{$location["logintime"]}</span>
                      </span>
                    </td>
				  </tr>
				{/foreach}
                </tbody>
              </table>
            </div>
          </div><!--card-->
		  
		</div>
      </div><!--row-->
	  <!-- pass  Modal -->
		<div class="modal fade" id="pwd-up" tabindex="-1" role="dialog" aria-labelledby="pwd-upModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="pwd-upModalLabel">修改密码</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
			
				<div class="form-group">
					<label class="form-control-label">当前密码:</label>
					<input id="oldpwd" class="form-control form-control-sm" type="password" placeholder="Old passwd">
				</div>
				<div class="form-group">
					<label class="form-control-label">新密码:</label>
					<input id="pwd" class="form-control form-control-sm" type="password" placeholder="New passwd">
				</div>
				<div class="form-group">
					<label class="form-control-label">重复新密码</label>
					<input id="repwd" class="form-control form-control-sm" type="password" placeholder="RE New passwd">
				</div>
				 
		      </div>
			  <div class="modal-footer">
                    <button id="pwd-update" type="button" class="btn btn-primary">确认提交</button>
               </div>
		    </div>
		  </div>
		</div>
		
		<!-- telegram  Modal -->
		<div class="modal fade" id="telegram" tabindex="-1" role="dialog" aria-labelledby="telegramModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="telegramModalLabel">Telegram</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
			{if $config['enable_telegram'] == 'true'}
		      <div class="modal-body">
				<!-- Card body -->
				{if $user->telegram_id == null}
				<p class="description">请添加机器人账号 <a href="https://t.me/{$telegram_bot}" target="_blank">@{$telegram_bot}</a>，截图或拍照这张二维码发给它。</p>
				<div id="telegram-qr" class="text-center"></div>
				{else}
					<p class="description">当前绑定Telegram账户:&nbsp;<a href="https://t.me/{$user->im_value}">@{$user->im_value}</a></p>
					<p class="description">①&nbsp;如未显示,请在左边修改联系方式为Telegram,用户名填你Telegram设置的个人用户名。</p>
					<p class="description mb-3">②&nbsp;Telefram设置用户名：Settings -> Edit profile -> Add Username</p>
					<a href="/user/telegram_reset" class="btn btn-neutral">解绑TG账号</a>
				{/if} 
		      </div>
			{else}
			<div class="modal-body">
			  <div class="card-body">
				<p class="description">当前本站未开启 Telegram 功能.</p>
			  </div>
			</div>
			{/if}
			<div class="modal-footer">
                 <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
		    </div>
		  </div>
		</div>
		<!-- wechat-update Modal -->
		<div class="modal fade" id="wechat-up" tabindex="-1" role="dialog" aria-labelledby="wechat-upModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="wechat-upModalLabel">联系方式修改</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description mt-3">当前联系方式&nbsp;:&nbsp;</p>
					<select id="imtype" class="form-control form-control-sm">
						<option value="1" {if $user->im_type == 1}selected{/if}>微信</option>
						<option value="2" {if $user->im_type == 2}selected{/if}>QQ</option>
						<option value="3" {if $user->im_type == 3}selected{/if}>Facebook</option>
						<option value="4" {if $user->im_type == 4}selected{/if}>Telegram</option>
					</select>
					<div class="form-group mt-3">
						<label class="form-control-label">联系账号&nbsp;:&nbsp;</label>
						<input id="wechat" class="form-control form-control-sm" type="text" value="{$user->im_value}">
					</div>
				</div>	 
		      </div>
			  <div class="modal-footer">
                    <button id="wechat-update" type="button" class="btn btn-primary">确认提交</button>
               </div>
		    </div>
		  </div>
		</div>
		
		<!-- theme Modal -->
		<div class="modal fade" id="theme-up" tabindex="-1" role="dialog" aria-labelledby="theme-upModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="theme-upModalLabel">主题修改</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description">当前使用主题&nbsp;:&nbsp;</p>
					<select id="theme" class="form-control form-control-sm">
					{foreach $themes as $theme}
						<option value="{$theme}" {if $user->theme == $theme}selected{/if}>{$theme}</option>
					{/foreach}
					</select>
				</div>	 
		      </div>
			  <div class="modal-footer">
                    <button id="theme-update" type="button" class="btn btn-primary">确认提交</button>
               </div>
		    </div>
		  </div>
		</div>
		
		<!-- everyday email Modal -->
		<div class="modal fade" id="ajax-email" tabindex="-1" role="dialog" aria-labelledby="ajax-emailModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="ajax-emailModalLabel">每日邮件提醒设置</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description">当前设置&nbsp;:&nbsp;</p>
					<select id="mail" class="form-control form-control-sm" value="{$user->sendDailyMail}">
						<option value="1" {if $user->sendDailyMail == 1}selected{/if}>发送</option>
						<option value="0" {if $user->sendDailyMail == 0}selected{/if}>不发送</option>
					</select>
				</div>	 
		      </div>
			  <div class="modal-footer">
                    <button id="mail-update" type="button" class="btn btn-primary">确认提交</button>
               </div>
		    </div>
		  </div>
		</div>
		
		<!-- unblock IP Modal -->
		<div class="modal fade" id="unblock-ip" tabindex="-1" role="dialog" aria-labelledby="unblock-ipModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="unblock-ipModalLabel">解封IP</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<p class="description">当前状态&nbsp;:&nbsp;<span id="ajax-block">{$Block}</span></p> 
		      </div>
			  <div class="modal-footer">
                    <button id="unblock" type="button" class="btn btn-primary">提交解封</button>
               </div>
		    </div>
		  </div>
		</div>
	  {include file='dialog.tpl'}
	  {include file='user/footer.tpl'}
	  <script src="/theme/czssr/main/js/qrcode.min.js"></script>
	
	
	
	<!--Telegram-->
	{if $config['enable_telegram'] == 'true'}
	<script>
	
    $(document).ready(function () {
	    
		function telegram_qrcode() {
		    var telegram_qrcode = 'mod://bind/{$bind_token}';
			let qrcode2 = new QRCode(document.getElementById("telegram-qr"));
			qrcode2.clear();
			qrcode2.makeCode(telegram_qrcode);
		}
		
		if ($$.getElementById("telegram-qr")) {
			telegram_qrcode();
		}
	})
	</script>
	{/if}
