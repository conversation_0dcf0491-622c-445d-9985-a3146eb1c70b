<?php
/* Smarty version 3.1.33, created on 2022-02-06 09:06:12
  from '/www/wwwroot/www.shadowingy.cf/resources/conf/quantumult.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_61ff1f0473e894_04806240',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '7c3fe513a3b657ab43cff887ed879b147d926df3' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/conf/quantumult.tpl',
      1 => 1575156596,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:rule/Apple.conf' => 1,
    'file:rule/PROXY.conf' => 1,
    'file:rule/DIRECT.conf' => 1,
  ),
),false)) {
function content_61ff1f0473e894_04806240 (Smarty_Internal_Template $_smarty_tpl) {
if ($_smarty_tpl->tpl_vars['quantumult']->value == 3) {?>
[SERVER]
<?php echo $_smarty_tpl->tpl_vars['proxys']->value['ssr'];
echo $_smarty_tpl->tpl_vars['proxys']->value['v2ray'];?>


[POLICY]
<?php echo $_smarty_tpl->tpl_vars['groups']->value['proxy_group'];?>

<?php echo $_smarty_tpl->tpl_vars['groups']->value['domestic_group'];?>

<?php echo $_smarty_tpl->tpl_vars['groups']->value['others_group'];?>

<?php echo $_smarty_tpl->tpl_vars['groups']->value['apple_group'];?>

<?php echo $_smarty_tpl->tpl_vars['groups']->value['auto_group'];?>

<?php echo $_smarty_tpl->tpl_vars['groups']->value['direct_group'];?>


[Rule]
<?php $_smarty_tpl->_subTemplateRender('file:rule/Apple.conf', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
$_smarty_tpl->_subTemplateRender('file:rule/PROXY.conf', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
$_smarty_tpl->_subTemplateRender('file:rule/DIRECT.conf', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

GEOIP,CN,🍂 Domestic
FINAL,☁️ Others

<?php } elseif ($_smarty_tpl->tpl_vars['quantumult']->value == 2) {?>
[SERVER]

[SOURCE]
<?php echo $_smarty_tpl->tpl_vars['appName']->value;?>
_v2, server ,<?php echo $_smarty_tpl->tpl_vars['subUrl']->value;?>
?quantumult=1, false, true, false
<?php echo $_smarty_tpl->tpl_vars['appName']->value;?>
_ssr, server ,<?php echo $_smarty_tpl->tpl_vars['subUrl']->value;?>
?sub=1, false, true, false
Hackl0us Rules, filter, https://raw.githubusercontent.com/Hackl0us/Surge-Rule-Snippets/master/LAZY_RULES/Quantumult.conf, true

<?php }?>

[DNS]
system, ************, *********, ***********, *******

[STATE]
STATE,AUTO
<?php }
}
