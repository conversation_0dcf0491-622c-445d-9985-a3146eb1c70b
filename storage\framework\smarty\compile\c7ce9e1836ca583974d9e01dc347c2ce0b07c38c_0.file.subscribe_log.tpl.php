<?php
/* Smarty version 3.1.33, created on 2022-07-17 21:20:46
  from '/www/wwwroot/www.shadowingy.com/resources/views/czssr/user/subscribe_log.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62d40cae67c132_23542543',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'c7ce9e1836ca583974d9e01dc347c2ce0b07c38c' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.com/resources/views/czssr/user/subscribe_log.tpl',
      1 => 1573185984,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:user/main.tpl' => 1,
    'file:user/footer.tpl' => 1,
  ),
),false)) {
function content_62d40cae67c132_23542543 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:user/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Subscribe Logs</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
				  <li class="breadcrumb-item"><a href="/user/subscribe_log">订阅记录</a></li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/ticket/create" class="btn btn-sm btn-neutral">工单</a>
              <a href="/user/shop" class="btn btn-sm btn-neutral">前往商店</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">订阅记录查看</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<blockquote class="blockquote mb-0">
							<p class="description">您可在此查询您账户最近 <?php echo $_smarty_tpl->tpl_vars['config']->value['subscribeLog_keep_days'];?>
 天的订阅记录, 确保您的账户没有被盗用.</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
			
            <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">记录表</h3>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive">
				<?php echo $_smarty_tpl->tpl_vars['logs']->value->render();?>

					<table class="table align-items-center table-flush">
					<thead class="thead-light">
						<tr>
							<th>ID</th>
                            <th>订阅类型</th>
                            <th>IP</th>
                            <th>归属地</th>
                            <th>时间</th>
                            <th>User-Agent</th>
						</tr>
					</thead>
					<tbody class="list">
				    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['logs']->value, 'log');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['log']->value) {
?>
					
					<tr>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>#<?php echo $_smarty_tpl->tpl_vars['log']->value->id;?>
</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i><?php echo $_smarty_tpl->tpl_vars['log']->value->subscribe_type;?>
</span>
						</td>
						<td>
							<span class="badge"><?php echo $_smarty_tpl->tpl_vars['log']->value->request_ip;?>
</span>
						</td>
						<?php $_smarty_tpl->_assignInScope('location', $_smarty_tpl->tpl_vars['iplocation']->value->getlocation($_smarty_tpl->tpl_vars['log']->value->request_ip));?>
						<td>
							<span class="badge"><?php echo iconv("gbk","utf-8//IGNORE",$_smarty_tpl->tpl_vars['location']->value['country']);?>
 <?php echo iconv("gbk","utf-8//IGNORE",$_smarty_tpl->tpl_vars['location']->value['area']);?>
</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i><?php echo $_smarty_tpl->tpl_vars['log']->value->request_time;?>
</span>
						</td>
						<td>
							<span class="badge"><?php echo $_smarty_tpl->tpl_vars['log']->value->request_user_agent;?>
</span>
						</td>
					</tr>
					 
					<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
					</tbody>
					</table>
				
				</div>
              </div>
            </div><!--card-->
		
        </div>
      </div><!--row-->
	  

	  <?php $_smarty_tpl->_subTemplateRender('file:user/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php }
}
