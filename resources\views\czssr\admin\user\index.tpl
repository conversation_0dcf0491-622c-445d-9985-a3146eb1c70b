{include file='admin/main.tpl'}

    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">User Center</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/user">用户列表</a></li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/node" class="btn btn-sm btn-neutral">节点</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">用户列表</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>显示表项: {include file='table/checkbox.tpl'}</p>
							
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
			
            <div class="card">
              <!-- Card body -->
			  <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive py-4">
				  {include file='table/table.tpl'}
					
				</div>
              </div>
            </div><!--card-->
			
			<div class="card">
				<div class="card-header bg-transparent">
					<h4 class="mb-0">添加用户(邮箱=密码)</h4>
				</div>
				<div class="card-body">
					<div class="form-group">
						<label class="form-control-label">用户昵称:</label>
						<input id="userName" class="form-control form-control-sm" type="text" placeholder="请填写用户昵称...">
					</div>
					<div class="form-group">
						<label class="form-control-label">填写邮箱:</label>
						<input id="email" class="form-control form-control-sm" type="text" placeholder="请输入用户邮箱...">
					</div>
					<label class="form-control-label">选择套餐:</label>
					<select id="sptype" class="form-control form-control-sm">
						<option value="0"><a href="javascript:void(0)" onclick="return false;">选择您要开通的套餐</a></option>
						{foreach $shop_name as $key => $value}
							<option value="{$value["id"]}">{$value["name"]}--{$value["price"]}</option>
						{/foreach}
					</select>
				</div>
				<div class="modal-footer">
                    <button id="adminadds_input" type="button" class="btn btn-primary">确认提交</button>
				</div>
			</div>
			
			<div class="card">
				<div class="card-header bg-transparent">
					<h4 class="mb-0">批量到期邮件通知</h4>
				</div>
				<div class="card-body">
					<div class="form-group">
						<div class="custom-control custom-radio custom-control-inline">
							<input type="radio" id="exprice_email" name="type_email" class="custom-control-input">
							<label class="custom-control-label" for="exprice_email">账号有效期</label>
						</div>
						<div class="custom-control custom-radio custom-control-inline">
							<input type="radio" id="class_email" name="type_email" class="custom-control-input" checked>
							<label class="custom-control-label" for="class_email">等级有效期</label>
						</div>
					</div> 
					<div class="form-group">
						<div class="custom-control custom-radio custom-control-inline">
							<input type="radio" id="overdue_in" name="overdue" class="custom-control-input">
							<label class="custom-control-label" for="overdue_in">已经过期</label>
						</div>
						<div class="custom-control custom-radio custom-control-inline">
							<input type="radio" id="overdue_no" name="overdue" class="custom-control-input" checked>
							<label class="custom-control-label" for="overdue_no">7天内过期</label>
						</div>
					</div> 
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>发送邮件轮训过程请等待页面刷新.</p>
				</div>
				<div class="modal-footer">
                    <button id="send_email" type="button" class="btn btn-primary">确认提交</button>
				</div>
			</div>
          
          <div class="card">
            <div class="card-header bg-transparent">
               <h4 class="mb-0">补单功能(慎用)</h4>
            </div>
			<div class="card-body">
				<div class="nav-wrapper">
					<ul class="nav nav-pills nav-fill flex-column flex-md-row" id="tabs-icons-text" role="tablist">
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0 active" id="tabs-icons-text-1-tab" data-toggle="tab" href="#tabs-icons-text-1" role="tab" aria-controls="tabs-icons-text-1" aria-selected="true"> 补单时长</a>
						</li>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-2-tab" data-toggle="tab" href="#tabs-icons-text-2" role="tab" aria-controls="tabs-icons-text-2" aria-selected="false"> 补单流量</a>
						</li>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-3-tab" data-toggle="tab" href="#tabs-icons-text-3" role="tab" aria-controls="tabs-icons-text-3" aria-selected="false"> 补单余额</a>
						</li>
					</ul>
				</div>
	
				<div class="tab-content" id="myTabContent">
					<div class="tab-pane fade show active" id="tabs-icons-text-1" role="tabpanel" aria-labelledby="tabs-icons-text-1-tab">
						<p>
							<i class="ni ni-air-baloon"></i> 批量增加时长:
						</p>
						<div class="form-group">
							<label class="form-control-label" for="vip">选择补单哪个VIP等级&nbsp;:&nbsp;</label>
							<select class="form-control form-control-sm maxwidth-edit" id="vip" name="vip">
								{foreach $Levelist as $level}
								<option value="{$level->level}">{$level->name}</option>
								{/foreach}	
							</select>
						</div>
						<div class="form-group">
							<div class="custom-control custom-radio custom-control-inline">
								<input type="radio" id="exprice_in" name="exprice_in" class="custom-control-input">
								<label class="custom-control-label" for="exprice_in">增加账号有效期</label>
							</div>
							<div class="custom-control custom-radio custom-control-inline">
								<input type="radio" id="class_exprice" name="exprice_in" class="custom-control-input" checked>
								<label class="custom-control-label" for="class_exprice">增加等级有效期</label>
							</div>
						</div> 
						<div class="form-group">
							<label class="form-control-label">填写时长(H):</label>
							<input id="class_h" class="form-control form-control-sm" type="number" placeholder="请输入整数(小时)...">
						</div>
						<div class="modal-footer">
							<button id="addclass_h" type="button" class="btn btn-primary">确认提交</button>
						</div>
					</div>
				
					<div class="tab-pane fade" id="tabs-icons-text-2" role="tabpanel" aria-labelledby="tabs-icons-text-2-tab">
						<p>
							<i class="ni ni-air-baloon"></i> 批量增加流量:
						</p>
						<div class="form-group">
							<label class="form-control-label" for="vip_traffic">选择补单哪个VIP等级&nbsp;:&nbsp;</label>
							<select class="form-control form-control-sm maxwidth-edit" id="vip_traffic" name="vip_traffic">
								{foreach $Levelist as $level}
								<option value="{$level->level}">{$level->name}</option>
								{/foreach}	
							</select>
						</div>
						
						<div class="form-group">
							<label class="form-control-label">填写流量(GB):</label>
							<input id="user_traffic" class="form-control form-control-sm" type="number" placeholder="请输入整数(GB)...">
						</div>
						<div class="modal-footer">
							<button id="addTraffic" type="button" class="btn btn-primary">确认提交</button>
						</div>
					</div>
					<div class="tab-pane fade" id="tabs-icons-text-3" role="tabpanel" aria-labelledby="tabs-icons-text-3-tab">
					    <p>
							<i class="ni ni-air-baloon"></i> 批量增加余额:
						</p>
						<div class="form-group">
							<label class="form-control-label" for="vip_money">选择补单哪个VIP等级&nbsp;:&nbsp;</label>
							<select class="form-control form-control-sm maxwidth-edit" id="vip_money" name="vip_money">
								{foreach $Levelist as $level}
								<option value="{$level->level}">{$level->name}</option>
								{/foreach}	
							</select>
						</div>
						
						<div class="form-group">
							<label class="form-control-label">填写金额(元):</label>
							<input id="user_money" class="form-control form-control-sm" type="number" placeholder="请输入整数(元)...">
						</div>
						<div class="modal-footer">
							<button id="addMoney" type="button" class="btn btn-primary">确认提交</button>
						</div>
					</div>
				</div>
			</div>
		  </div><!--card-->
 
		
        </div>
      </div><!--row-->
		<!--删除modal-->
		<div class="modal fade" id="delete_modal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="deleteModalLabel" class="text-danger">确认删除吗?</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>请问你确认要删除吗?</p>
				</div>	 
		      </div>
			    <div class="modal-footer">
                    <button id="delete_input" type="button" class="btn btn-primary">确认提交</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
		    </div>
		  </div>
		</div>
		<!--change user-->
		<div class="modal fade" id="changetouser_modal" tabindex="-1" role="dialog" aria-labelledby="ChangetouserModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
					<div class="modal-header">
                        <h4 id="ChangetouserModalLabel" class="text-success">确认切换吗?</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
                    </div>
                    <div class="modal-body">
						<!-- Card body -->
						<div class="card-body">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>请问你确认要切换吗?</p>
						</div>	 
					</div>
                    <div class="modal-footer">
						<button id="changetouser_input" type="button" class="btn btn-primary">确认提交</button>
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
					</div>
                </div>
            </div>
        </div>
      <!--send email-->
		<div class="modal fade" id="sendemail_modal" tabindex="-1" role="dialog" aria-labelledby="sendemailModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
					<div class="modal-header">
                        <h4 id="sendemailModalLabel">系统通知</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
                    </div>
                    <div class="modal-body">
						<!-- Card body -->
						<div class="card-body">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>正在发送邮件, 请勿关闭页面....</p>
						</div>	 
					</div>
                    <div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
					</div>
                </div>
            </div>
        </div>
      {include file='dialog.tpl'}
	  {include file='admin/footer.tpl'} 
<!-- <script src="//cdn.jsdelivr.net/npm/datatables.net@1.10.19"></script> -->
<script src="/theme/czssr/main/js/datatables.net@1.10.19"></script>
<script src="/theme/czssr/main/js/dataTables.material.min.js"></script>

<script>

    function delete_modal_show(id) {
        deleteid = id;
        $("#delete_modal").modal();
    }

    function changetouser_modal_show(id) {
        changetouserid = id;
        $("#changetouser_modal").modal();
    }
{include file='table/js_1.tpl'}

    window.addEventListener('load', () => {
        table_1 = $('#table_1').DataTable({
            order: [[1, 'asc']],
            stateSave: true,
            serverSide: true,
            ajax: {
                url: "/admin/user/ajax",
                type: "POST",
            },
            columns: [
                {literal}
                {"data": "op", "orderable": false},
                {"data": "id"},
                {"data": "user_name"},
                {"data": "remark"},
                {"data": "email"},
                {"data": "money"},
               // {"data": "im_type"},
               // {"data": "im_value"},
			    {"data": "is_admin"},
                {"data": "is_agent"},
				{"data": "ssrlink"},
                {"data": "node_group"},
                {"data": "expire_in"},
                {"data": "class"},
                {"data": "class_expire"},
                {"data": "passwd"},
                {"data": "port"},
                {"data": "method"},
                {"data": "protocol"},
                {"data": "obfs"},
                {"data": "online_ip_count", "orderable": false},
                {"data": "last_ss_time", "orderable": false},
                {"data": "used_traffic"},
                {"data": "enable_traffic"},
                {"data": "last_checkin_time", "orderable": false},
                {"data": "today_traffic"},
                {"data": "enable"},
				{"data": "detect_ban"},
                {"data": "reg_date"},
                {"data": "reg_addr"},
                {"data": "reg_ip"},
                {"data": "auto_reset_day"},
                {"data": "auto_reset_bandwidth"},
                {"data": "ref_money"},
                {"data": "invite_num"},
				{"data": "payback_code"},
                {"data": "ref_by"},
                {"data": "agent_id"},
                {"data": "ref_by_user_name", "orderable": false},
                {/literal}
            ],
            "columnDefs": [
                {
                    targets: ['_all'],
                    className: 'mdl-data-table__cell--non-numeric'
                }
            ],

            {include file='table/lang_chinese.tpl'}
        });

       {include file='table/js_3.tpl'}
      
		function delete_id() {
            $.ajax({
                type: "DELETE",
                url: "/admin/user",
                dataType: "json",
                data: {
                    id: deleteid
                },
                success: data => {
                    if (data.ret) {
                        $("#delete_modal").modal("hide");
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        {include file='table/js_delete.tpl'}
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $('#msg').html("发生错误："+jqXHR.status);
                }
            });
        }
	    $("#delete_input").on("click", delete_id);
		
		function changetouser_id() {
            $.ajax({
                type: "POST",
                url: "/admin/user/changetouser",
                dataType: "json",
                data: {
                    userid: changetouserid,
                    adminid: {$user->id},
                    local: '/admin/user'
                },
                success: data => {
                    if (data.ret) {
                        $("#changetouser_modal").modal("hide");
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href='/user'", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $('#msg').html("发生错误："+jqXHR.status);
                }
            });
        }
		$("#changetouser_input").on("click", changetouser_id);
		
    })
</script>
<script>
		//单个注册调用 检查输入字符合法性逻辑
    function checkByteLength(str,minlen,maxlen) {
    if (str == null) return false;
    //为空返回false
    var l = str.length;
    var blen = 0;
    for(i=0; i<l; i++) {
      //循环取得检验值的长度
      if ((str.charCodeAt(i) & 0xff00) != 0) {
        blen ++;
      }
      blen ++;
    }
    if (blen > maxlen || blen < minlen) {
      //判断长度是否合法
      return false;
    }
    return true;
  }

		//检查输入字符合法性逻辑
  function validateUsername(value){
    var patn = /^[a-zA-Z]+[a-zA-Z0-9]+$/; 
    if(!checkByteLength(value,4,16)) return false;

    var pattern = /^[A-Za-z0-9\u4e00-\u9fa5]+$/gi;
	if (pattern.test(value)) {
      return true; 
    }else{
      return false;
    }
    
  }
  //单个注册调用
$(document).ready(function() {
   function adminaddUser() {
		
        var name = document.getElementById("userName").value;
	  	var email = document.getElementById("email").value;
		var shopId = document.getElementById("sptype").value;
		
		/*用户昵称检测*/
		if(!validateUsername($("#userName").val())) {
  　　　　　swal('Oops...', "用户名不合法,仅支持4~16位字母数字或中文",'error');
            return false;
        }
         //   !Check::isEmailLegal($email)
		/* 邮箱检测 */
         if($("#email").val()==null || $("#email").val()==''){
           swal('Oops...', "邮箱不能为空！",'error');
           return;
         }
         var email_arr = $("#email").val().split('@');
         var email_blacklist = ["qq.com","sina.com", "163.com","sina.cn", "gmail.com", "live.com", "163.com", "139.com", "outlook.com", "189.cn", "foxmail.com", "vip.qq.com", "hotmail.com", "126.com", "aliyun.com", "yeah.net", "sohu.com", "live.jp", "msn.com", "icloud.com"];
         if ($.inArray(email_arr[1], email_blacklist) == "-1") {
           swal('Oops...', "暂不支持此邮箱，请更换如QQ、谷歌、新浪、网易等常见邮箱。",'error');
           return false;
         } 
     		/*套餐检测*/
		if (shopId == "" || shopId == 0){
			swal('Oops...', "请选择要开通的套餐！",'error');
            return;
        }
         $.ajax({
            type: "POST",
            url: "/admin/user/adds",
            dataType: "json",
            data: {
                "userName": name,
              	"email": email,
				"shopId": shopId,
                "agentid": {$user->id}
            },
            success: function (data) {
                if (data.ret == "0"){
                  	swal('Oops...',data.msg,'error');
                }else {
                  swal({
                        title: "添加用户成功",
                        text: "赶快查看吧~!", 
                        type:"success"
                        });
                	window.setTimeout("location.href=window.location.href", 2000);
                    }
                },
             
            error: function (jqXHR) {
                $("#result").modal();
                $("#msg").html("发生错误了: " + jqXHR.status);
              
            }
        });
    }

    $("#adminadds_input").click(function () {
        adminaddUser();
    });
})
</script>