{include file='admin/main.tpl'}


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Nodes</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
                  <li class="breadcrumb-item"><a href="/admin/node">节点列表</a></li>
				  <li class="breadcrumb-item active">新建节点</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/user" class="btn btn-sm btn-neutral">用户</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card" id="main_form">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">新建节点</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">节点名称:</label>
							<input id="name" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">节点地址(入口地址,端口偏移写这里 ;port=单端口号#偏移的目标端口号):</label>
							<input id="server" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">出口解析地址(V2须带上参数例如 xxx.com;8989;2;tls;ws;path=/v2ray|host=xxx.com)</label>
							<input id="server_out" class="form-control form-control-sm" type="text">
							<p class="description badge-dot"><i class="bg-warning"></i>如果填写为域名, "节点IP"会自动设置为解析的IP</p>
						</div>
						<div class="form-group">
							<label class="form-control-label">节点IP(出口地址为域名的情况下可留空,不要把端口偏移写这里):</label>
							<input id="node_ip" class="form-control form-control-sm" type="text">
						</div>
						
						<div class="form-group" hidden="hidden">
							<label class="form-control-label">加密方式(隐藏):</label>
							<input id="method" class="form-control form-control-sm" type="text" value="aes-256-cfb">
						</div>
						<div class="form-group">
							<label class="form-control-label">流量比例:</label>
							<input id="rate" class="form-control form-control-sm" type="text" value="1">
						</div>
						<div class="form-group" hidden="hidden">
							<label class="custom-toggle icon-ver" for="custom_method">
								<input id="custom_method" type="checkbox" checked>
								<span class="custom-toggle-slider rounded-circle" data-label-off="No" data-label-on="Yes"></span>
							</label>&nbsp;&nbsp;自定义加密(隐藏)
						</div>
						<div class="form-group" hidden="hidden">
							<label class="custom-toggle icon-ver" for="custom_rss">
								<input id="custom_rss" type="checkbox" checked>
								<span class="custom-toggle-slider rounded-circle" data-label-off="No" data-label-on="Yes"></span>
							</label>&nbsp;&nbsp;自定义协议&混淆(隐藏)
						</div>
						<div class="form-group">
						<label class="form-control-label" for="mu_only">单端口多用户启用:</label>
						<select id="mu_only" class="form-control form-control-sm" name="mu_only">
                            <option value="0">单端口多用户与普通端口并存</option>
                            <option value="-1">只启用普通端口</option>
                            <option value="1">只启用单端口多用户</option>
                        </select>
						</div>
					</div>
				</div>
			
			<div class="card bg-gradient-Secondary">
				<div class="card-body">
					<div class="form-group">
						<label class="custom-toggle icon-ver" for="type">
							<input id="type" type="checkbox" checked>
							<span class="custom-toggle-slider rounded-circle" data-label-off="No" data-label-on="Yes"></span>
						</label>&nbsp;&nbsp;是否显示
					</div>
                  		
					<div class="form-group">
						<label class="form-control-label">节点图标(例如中国填写:CN 美国填写:US):</label>
						<input id="status" class="form-control form-control-sm" type="text" value="可用">
					</div>
					<div class="form-group">
					<label class="form-control-label" for="sort">节点类型:</label>
					<select id="sort" class="form-control form-control-sm" name="sort">
                        <option value="0">Shadowsocks</option>
                         <option value="1">VPN/Radius基础</option>
                         <option value="2">SSH</option>
                         <option value="5">Anyconnect</option>
                         <option value="9">Shadowsocks 单端口多用户</option>
                         <option value="10">Shadowsocks 中转</option>
                         <option value="11">V2Ray</option>
                         <option value="12">V2Ray 中转</option>
                         <option value="13">Shadowsocks V2Ray-Plugin</option>
                    </select>
					</div>
					<div class="form-group">
						<label class="form-control-label">节点描述:</label>
						<input id="info" class="form-control form-control-sm" type="text" value="无描述">
					</div>
					<div class="form-group">
					<label class="form-control-label" for="class">节点等级:</label>
					<select id="class" class="form-control form-control-sm" name="class">
                        {foreach $levelList as $level}
							<option value="{$level->level}">{$level->name}</option>
						{/foreach}	
                    </select>
					<p class="description badge-dot"><i class="bg-warning"></i>不分级请选默认等级</p>
					</div>
					<div class="form-group">
					<label class="form-control-label" for="group">节点群组:</label>
					<select id="group" class="form-control form-control-sm" name="group">
                        {foreach $groupList as $group}
							<option value="{$group->level}">{$group->name}</option>
						{/foreach}	
                    </select>
					<p class="description badge-dot"><i class="bg-warning"></i>不分级请选默认组</p>
					</div>
				</div>
			</div>
            <div class="card bg-gradient-Secondary">
				<div class="card-body">
					<div class="form-group">
						<label class="form-control-label">节点流量上限(GB,不设上限请填0):</label>
						<input id="node_bandwidth_limit" class="form-control form-control-sm" type="text" value="0">
					</div>
					<div class="form-group">
						<label class="form-control-label">节点流量上限清空日(不清空填0):</label>
						<input id="bandwidthlimit_resetday" class="form-control form-control-sm" type="text" value="0">
					</div>
					<div class="form-group">
						<label class="form-control-label">节点限速(Mbps,不限速请填0,对于每个用户端口生效):</label>
						<input id="node_speedlimit" class="form-control form-control-sm" type="text" value="0">
					</div>
				</div>
			</div><!--card-->

			<div class="modal-footer">
                <button id="submit" type="button" class="btn btn-primary">确认提交</button>
			</div>
		  </div><!--card-->
        </div>
      </div><!--row-->
		{include file='dialog.tpl'}
       
	  {include file='admin/footer.tpl'}


<script>

    window.addEventListener('load', () => {
		function submit() {
            if ($$.getElementById('custom_method').checked) {
                var custom_method = 1;
            } else {
                var custom_method = 0;
            }

            if ($$.getElementById('type').checked) {
                var type = 1;
            } else {
                var type = 0;
            }

            if ($$.getElementById('custom_rss').checked) {
                var custom_rss = 1;
            } else {
                var custom_rss = 0;
            }

            $.ajax({
			    type: "POST",
                url: "/admin/node",
                dataType: "json",
                data: {
                    name: $$getValue('name'),
                    server: $$getValue('server'),
					server_out: $$getValue('server_out'),
                    node_ip: $$getValue('node_ip'),
                    method: $$getValue('method'),
                    custom_method,
                    rate: $$getValue('rate'),
                    info: $$getValue('info'),
                    type,
                    group: $$getValue('group'),
                    status: $$getValue('status'),
                    sort: $$getValue('sort'),
                    node_speedlimit: $$getValue('node_speedlimit'),
                    class: $$getValue('class'),
                    node_bandwidth_limit: $$getValue('node_bandwidth_limit'),
                    bandwidthlimit_resetday: $$getValue('bandwidthlimit_resetday'),
                    custom_rss,
                    mu_only: $$getValue('mu_only')
                },
                success: (data) => {
                    if (data.ret) {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", {$config['jump_delay']});

                    } else {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                    }
                },

                error: (jqXHR) => {
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            });
		}
		$("#submit").on("click", submit);
    })
</script>

