<?php
require_once('WxqqJump/WxqqJump.php');
//关闭订阅域名访问
//  $url = $_SERVER['HTTP_HOST'];
//  $suburl = "http://www.shadowingy.xyz/";  //你的订阅域名
//  $jump_link = explode('/',$suburl);
//  $links = $_SERVER["REQUEST_URI"];
//  $link = explode('/',$links);
//  if ($link[1] != "link" && $jump_link[2] == $url) {
//   header('Location: http://www.shadowingy.xyz/404.html');
//   exit();
//}

//  PUBLIC_PATH
define('PUBLIC_PATH', __DIR__);

// Bootstrap
require PUBLIC_PATH.'/../bootstrap.php';


// Init slim routes
require BASE_PATH.'/config/routes.php';

