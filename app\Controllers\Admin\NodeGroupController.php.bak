<?php
namespace App\Controllers\Admin;

use App\Models\Level;
use App\Models\Node;
use App\Models\NodeGroup;
use App\Controllers\AdminController;
use Ozdemir\Datatables\Datatables;
use App\Utils\DatatablesHelper;

class NodeGroupController extends AdminController
{	
    public function index($request, $response, $args)
    {
        $table_config['total_column'] = array("op" => "操作", "id" => "ID",
                              "name" => "分组名称", "level" => "分组级别", "date" => "时间");
        $table_config['default_show_column'] = array("op", "id",
                                                    "name", "level", "date");
													
        $table_config['ajax_url'] = 'node_group/ajax';
        return $this->view()->assign('table_config', $table_config)->display('admin/node_group/index.tpl');
		
    }

    public function create($request, $response, $args)
    {
        $levelList = Level::select('id','name','level')->orderBy('id')->get(); //获取等级名称的列表
		return $this->view()->assign('levelList', $levelList)->display('admin/node_group/create.tpl');
    }
	
    public function add($request, $response, $args)
    {	
        $ngg = new NodeGroup();
		$ngg->name = $request->getParam('name');
        $ngg->level = $request->getParam('level');
		$ngg->date =  date("Y-m-d H:i:s");
		
            if (!$ngg->save()) {
                $rs['ret'] = 0;
                $rs['msg'] = "添加失败";
                return $response->getBody()->write(json_encode($rs));
            }
				$rs['ret'] = 1;
				$rs['msg'] = "添加成功";
			
			return $response->getBody()->write(json_encode($rs));
	} 
    public function edit($request, $response, $args)
    {
        $id = $args['id'];
        $ngg = NodeGroup::find($id);
        $levelList = Level::select('id','name','level')->orderBy('id')->get(); //获取等级名称的列表
        return $this->view()->assign(array('levelList' => $levelList, 'ngg' => $ngg, 'level' => $level))->display('admin/node_group/edit.tpl');
    }

    public function update($request, $response, $args)
    {
        $id = $args['id'];
        $ngg = NodeGroup::find($id);
        $ngg->name =  $request->getParam('name');
        $ngg->level =  $request->getParam('level');
		$ngg->date =  date("Y-m-d H:i:s");
		
		Node::where('group_id',$id)->update(['node_class' => $request->getParam('level')]);
		
        if (!$ngg->save()) {
            $rs['ret'] = 0;
            $rs['msg'] = "修改失败";
            return $response->getBody()->write(json_encode($rs));
        }
			$rs['ret'] = 1;
			$rs['msg'] = "修改成功";
        return $response->getBody()->write(json_encode($rs));
    }

	
    public function delete($request, $response, $args)
    {
        $id = $request->getParam('id');
        $ngg = NodeGroup::find($id);
      
        Node::where('group_id',$id)->update(['node_class' => 0]);
        if (!$ngg->delete()) {
            $rs['ret'] = 0;
            $rs['msg'] = "删除失败";
            return $response->getBody()->write(json_encode($rs));
        }
        $rs['ret'] = 1;
        $rs['msg'] = "删除成功";
        return $response->getBody()->write(json_encode($rs));
    }

    public function ajax($request, $response, $args)
    {
        $datatables = new Datatables(new DatatablesHelper());
        $datatables->query('Select id as id,date,name,level from ss_group');


		$datatables->edit('op', function ($data) {
            return '<a class="btn btn-brand" '.($data['id'] == 1 ? 'disabled' : 'href="/admin/node_group/'.$data['id'].'/edit"').'>编辑</a>
                    <a class="btn btn-brand-accent" '.($data['id'] == 1 ? 'disabled' : 'id="delete" value="'.$data['id'].'" href="javascript:void(0);" onClick="delete_modal_show(\''.$data['id'].'\')"').'>删除</a>';
        });
        $datatables->edit('DT_RowId', function ($data) {
            return 'row_1_'.$data['id'];
        });


        $body = $response->getBody();
        $body->write($datatables->generate());
    }
}
