## 应用概述
## [点击在线观看iphone手机视频教程](https://cdn.jsdelivr.net/gh/lxiaoya1003/tuchuang@master/shadowingy-ios.m4v)

<video width="300" height="150" controls="controls">
<source src="https://cdn.jsdelivr.net/gh/lxiaoya1003/tuchuang@master/shadowingy-ios.m4v" type="video/mp4" />
</video>

Kitsunebi 是在 iOS 平台上的客户端软件，支持 Shadowsocks 以及 VMess 协议。

目前 Kitsunebi 已经被 Apple 根据政府要求从中国大陆区的 App Store 移除，之前在中国大陆商店购买此软件的用户将不能获得更新或重新下载。

!> 这是一个付费软件，你需要购买才能使用。

## 应用下载

以下是各平台该应用的下载地址。

- Apple iOS：[App Store](https://itunes.apple.com/us/app/kitsunebi-proxy-utility/id1446584073?ls=1&mt=8)
- ...

## 获取订阅

此处将显示您的订阅链接，请注意为登录状态：

[cinwell website](/sublink?type=kitsunebi ':include :type=markdown')

!> 这个 **订阅链接** 非常重要，你应当把它当做密码一样妥善保管。

## 配置 Kitsunebi

打开 Kitsunebi，点击底部导航栏的「服务器」进入服务器页面。

![1](https://i.loli.net/2019/01/13/5c3a6dccd346d.png ':size=400')

点击右上角的加号，从弹出菜单中选择第四个「订阅」。

在「备注」中输入本站名称，随后在「URL」中粘贴上方 **[获取订阅](#获取订阅)** 中您需要使用的订阅类型，打开自动更新的开关，随后点击右上角储存。

![2](https://i.loli.net/2019/01/13/5c3a6e474ae9f.jpeg ':size=400')

点击本站订阅名称旁的感叹号进入订阅页面，随后点击「从 URL 更新」，此时会自动更新获取服务器。

![3](https://i.loli.net/2019/01/13/5c3a70a5d946a.jpeg ':size=400')

将上方「操作模式」更改为 **Rule**。

![4](https://i.loli.net/2019/01/13/5c3a71587252e.jpg ':size=200')

## 开始使用

在下方订阅组中选择您需要的节点，随后点击底部导航栏的「状态」进入状态页面，打开开关即可。

如提示添加 VPN 配置，请点击 Allow 并验证您的 密码、Touch ID、Face ID 。
