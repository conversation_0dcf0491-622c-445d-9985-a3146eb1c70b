<?php
/* Smarty version 3.1.33, created on 2022-07-17 17:52:23
  from '/www/wwwroot/www.shadowingy.com/resources/views/czssr/auth/login.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62d3dbd7a15e34_02334004',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '1b8a56ec8a30704d33bcc7ccc6768444ac0e50dd' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.com/resources/views/czssr/auth/login.tpl',
      1 => 1648042982,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.tpl' => 1,
    'file:footer.tpl' => 1,
  ),
),false)) {
function content_62d3dbd7a15e34_02334004 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:header.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<body>
<!-- 以下代码变黑白色 -->
<!-- <style type="text/css">
html {
filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
-webkit-filter: grayscale(100%);}
</style> -->
<!-- 以上代码变黑白色 -->
  <header class="header-global">
    <nav id="navbar-main" class="navbar navbar-main navbar-expand-lg navbar-transparent navbar-light headroom">
      <div class="container">
        <a class="navbar-brand mr-lg-5" href="/">
          <img src="/theme/czssr/main/picture/white.png" alt="brand">
          <span class="engname1"> <?php echo $_smarty_tpl->tpl_vars['config']->value['engname'];?>
</span>
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar_global" aria-controls="navbar_global" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button><!--菜单button-->
        <div class="navbar-collapse collapse" id="navbar_global">
          <div class="navbar-collapse-header">
            <div class="row">
              <div class="col-6 collapse-brand">
                <a href="/">
                  <img src="/theme/czssr/main/picture/blue.png" alt="brand">
                  <span class="engname3"> <?php echo $_smarty_tpl->tpl_vars['config']->value['engname'];?>
</span>
                </a>
              </div>
              <div class="col-6 collapse-close"><!--关闭button-->
                <button type="button" class="navbar-toggler" data-toggle="collapse" data-target="#navbar_global" aria-controls="navbar_global" aria-expanded="false" aria-label="Toggle navigation">
                  <span></span>
                  <span></span>
                </button>
              </div>
            </div>
          </div>
          <ul class="navbar-nav navbar-nav-hover align-items-lg-center">
				<li class="nav-item ">
				<?php if ($_smarty_tpl->tpl_vars['user']->value->isLogin) {?>
				<a href="/user" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-sign-in"></i>
					<span class="nav-link-inner--text">My Account</span>
				</a>
				<?php } else { ?>
				<a href="/auth/login" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-sign-in"></i>
					<span class="nav-link-inner--text">Login</span>
				</a>
				<?php }?>
				</li>
				<li class="nav-item ">
				<?php if ($_smarty_tpl->tpl_vars['config']->value['register_mode'] != 'close') {?>
				<a href="/auth/register" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-user-plus"></i>
					<span class="nav-link-inner--text">Sign Up</span>
				</a>
				<?php } else { ?>
				<a href="javascript:void(0);" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-user-plus"></i>
					<span class="nav-link-inner--text">Stop register</span>
				</a>
				<?php }?>
				</li>
				<li class="nav-item ">
				<a href="/password/reset" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-repeat"></i>
					<span class="nav-link-inner--text">Reset Password</span>
				</a>
				</li>
				<li class="nav-item ">
				<a href="/doc" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-question-circle"></i>
					<span class="nav-link-inner--text">Support</span>
				</a>
				</li>
           </ul>
            <ul class="navbar-nav align-items-lg-center ml-lg-auto">
				<li class="nav-item">
				<!--<a class="nav-link nav-link-icon" href="#" target="_blank" data-toggle="tooltip" title="Star us on Telegram">
					<i class="fa fa-telegram"></i>
					<span class="nav-link-inner--text d-lg-none">Telegram</span>
				</a>-->
				
				</li>
            </ul>
        </div>
      </div>
    </nav>
  </header>
  <main>
    <section class="section section-shaped section-lg" style="min-height: calc(100vh);">
      <div class="shape shape-style-1 bg-gradient-default">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
      <div class="container pt-lg-md">
        <div class="row justify-content-center">
          <div class="col-lg-5">
            <div class="card bg-secondary shadow border-0">
              <div class="card-header bg-white">
                <div class="text-muted text-center mb-3"><small>Sign in with</small></div>
                <div class="btn-wrapper text-center">
                  <?php if ($_smarty_tpl->tpl_vars['config']->value['enable_tg'] == 'true') {?>
                  <a href="#" class="btn btn-neutral btn-icon" id="calltgauth" data-toggle="modal" data-target="#modal-default">
                    <span class="btn-inner--icon">
                      <img alt="image" src="/theme/czssr/main/picture/telegram.svg">
                    </span>
                    <span class="">Telegram</span>
                  </a>
                  <?php }?>
                  <?php if ($_smarty_tpl->tpl_vars['config']->value['enable_qq'] == 'true') {?>
				
                  <a href="<?php echo $_smarty_tpl->tpl_vars['login_url']->value;?>
" class="btn btn-neutral btn-icon" id="qqlogin">
                    <span class="btn-inner--icon">
                      <img alt="image" src="/theme/czssr/main/picture/qq.svg">
                    </span>
                    <span class="">Tencent</span>
                  </a>
				
                  <?php }?>
                </div>
				 <div class="modal fade" id="modal-default" tabindex="-1" role="dialog" aria-labelledby="modal-default" aria-hidden="true">
                    <div class="modal-dialog modal- modal-dialog-centered modal-" role="document">
                      <div class="modal-content">
                        <div class="modal-header">
                          <h6 class="modal-title" id="modal-title-default">Sign in with Telegram.</h6>
                          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true" style="font-size:1em">×</span>
                          </button>
                        </div>
                        <div class="modal-body">
                          <div class="py-3 text-center">
                            <i class="fa fa-telegram fa-4x"></i>
                            <h5 class="heading mt-4">一键登陆</h5>
							<p id="telegram-alert">正在载入 Telegram，如果长时间未显示请刷新页面或检查代理</p>
							<div class="text-center" id="telegram-login-box"></div>
							<p>或者添加机器人账号 <a href="https://t.me/<?php echo $_smarty_tpl->tpl_vars['telegram_bot']->value;?>
" target="_blank">@<?php echo $_smarty_tpl->tpl_vars['telegram_bot']->value;?>
</a>，发送下面的数字给它。</p>
							<div class="text-center">
								<h2><code id="code_number"><?php echo $_smarty_tpl->tpl_vars['login_number']->value;?>
</code></h2>
							</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
               </div>
              <div class="card-body px-lg-5 py-lg-5">
                <form action="javascript:void(0);" method="POST">
                  <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                      <div class="input-group-prepend">
                        <span class="input-group-text"><i class="ni ni-email-83"></i></span>
                      </div>
                      <input class="form-control" placeholder="Email" type="email" id="email">
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="input-group input-group-alternative">
                      <div class="input-group-prepend">
                        <span class="input-group-text"><i class="ni ni-lock-circle-open"></i></span>
                      </div>
                      <input class="form-control" placeholder="Password" type="password" id="passwd">
                    </div>
                  </div>
				  <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
				  <div class="form-group">
                    <div class="input-group-alternative">
                        <div id="embed-captcha"></div>
                    </div>
                  </div>
                  <?php }?>
                  <?php if ($_smarty_tpl->tpl_vars['recaptcha_sitekey']->value != null) {?>
                  <div class="form-group">
                    <div class="input-group-alternative">
                        <div align="center" class="g-recaptcha" data-sitekey="<?php echo $_smarty_tpl->tpl_vars['recaptcha_sitekey']->value;?>
"></div>
                    </div>
                  </div>
                  <?php }?>
                 <?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
                  <div class="form-group mb-3">
                    <div class="input-group-alternative">
                        <div align="center" class="g-recaptcha" data-sitekey="<?php echo $_smarty_tpl->tpl_vars['recaptcha_sitekey']->value;?>
"></div>
						<div id="CaptchaDX"></div>
                    </div>
                  </div>
                  <?php }?>
                  <div class="custom-control custom-control-alternative custom-checkbox">
                    <input class="custom-control-input" id="remember_me" type="checkbox">
                    <label class="custom-control-label" for="remember_me">
                      <span>Remember me</span>
                    </label>
                  </div>
                  <div class="text-center">
                    <button type="button" class="btn btn-primary my-4" id="login">Sign in</button>
                  </div>
                </form>
				
              </div>
            </div>

          </div>
        </div>
      </div>
    </section>
  </main>
  <!-- Footer -->
<?php $_smarty_tpl->_subTemplateRender('file:footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>



<?php if ($_smarty_tpl->tpl_vars['config']->value['enable_tg'] == 'true') {?>
    <?php echo '<script'; ?>
>
        $(document).ready(function () {
            $("#calltgauth").click(
                    function () {
                        f();
                    }
            );

            function f() {
                $.ajax({
                    type: "POST",
                    url: "qrcode_check",
                    dataType: "json",
                    data: {
                        token: "<?php echo $_smarty_tpl->tpl_vars['login_token']->value;?>
",
                        number: "<?php echo $_smarty_tpl->tpl_vars['login_number']->value;?>
"
                    },
                    success: (data) => {
                        if (data.ret > 0) {
                            clearTimeout(tid);

                            $.ajax({
                                type: "POST",
                                url: "/auth/qrcode_login",
                                dataType: "json",
                                data: {
                                    token: "<?php echo $_smarty_tpl->tpl_vars['login_token']->value;?>
",
                                    number: "<?php echo $_smarty_tpl->tpl_vars['login_number']->value;?>
"
                                },
                                success: (data) => {
                                    if (data.ret) {
                                        swal({
											title: "<span style='color: #9954bb; font-size:2rem; font-weight: 500'>"+data.user+"</span>",
											text: "Welcome, 欢迎进入<?php echo $_smarty_tpl->tpl_vars['config']->value["appName"];?>
~!",

										});
                                        window.setTimeout("location.href='/user'", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                                    }
                                },
                                error: (jqXHR) => {
                                    swal('Oops...',"发生错误"+jqXHR.status,'error');
                                }
                            });

                        } else {
                            if (data.ret === -1) {
                                $('#code_number').replaceWith('<code id="code_number">此数字已经过期，请刷新页面后重试。</code>');
                            }
                        }
                    },
                    error: (jqXHR) => {
                        if (jqXHR.status !== 200 && jqXHR.status !== 0) {
                            swal('Oops...',"发生错误"+jqXHR.status,'error');
                        }
                    }
                });
                tid = setTimeout(f, 2500); //循环调用触发setTimeout
            }


        })
    <?php echo '</script'; ?>
>
    <?php echo '<script'; ?>
>
        $(document).ready(function () {
            var el = document.createElement('script');
            document.getElementById('telegram-login-box').append(el);
            el.onload = function () {
                $('#telegram-alert').remove()
            }
            el.src = 'https://telegram.org/js/telegram-widget.js?4';
            el.setAttribute('data-size', 'large')
            el.setAttribute('data-telegram-login', '<?php echo $_smarty_tpl->tpl_vars['telegram_bot']->value;?>
')
            el.setAttribute('data-auth-url', '<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
/auth/telegram_oauth')
            el.setAttribute('data-request-access', 'write')
        });
    <?php echo '</script'; ?>
>
<?php }
if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
  <?php if (isset($_smarty_tpl->tpl_vars['geetest_html']->value)) {?>
    <?php echo '<script'; ?>
 src="//static.geetest.com/static/tools/gt.js"><?php echo '</script'; ?>
>
    <?php echo '<script'; ?>
>
        var handlerEmbed = function (captchaObj) {
            // 将验证码加到id为captcha的元素里

            captchaObj.onSuccess(function () {
                validate = captchaObj.getValidate();
            });

            captchaObj.appendTo("#embed-captcha");

            captcha = captchaObj;
            // 更多接口参考：http://www.geetest.com/install/sections/idx-client-sdk.html
        };

        initGeetest({
            gt: "<?php echo $_smarty_tpl->tpl_vars['geetest_html']->value->gt;?>
",
            challenge: "<?php echo $_smarty_tpl->tpl_vars['geetest_html']->value->challenge;?>
",
            product: "embed", // 产品形式，包括：float，embed，popup。注意只对PC版验证码有效
            offline: <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value->success) {?>0<?php } else { ?>1<?php }?> // 表示用户后台检测极验服务器是否宕机，与SDK配合，用户一般不需要关注
        }, handlerEmbed);
    <?php echo '</script'; ?>
>
  <?php }
}
if ($_smarty_tpl->tpl_vars['recaptcha_sitekey']->value != null) {?>
    <?php echo '<script'; ?>
 src="https://recaptcha.net/recaptcha/api.js" async defer><?php echo '</script'; ?>
>
<?php }
if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
	<?php echo '<script'; ?>
 src="https://cdn.dingxiang-inc.com/ctu-group/captcha-ui/index.js"><?php echo '</script'; ?>
>
<?php }
echo '<script'; ?>
>
$(document).ready(function(){
<?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
var appId = '<?php echo $_smarty_tpl->tpl_vars['config']->value["CaptchaDX_AppId"];?>
';
 var myCaptcha = _dx.Captcha(document.getElementById('CaptchaDX'), {
   appId: appId,
	type: 'basic', 
   style: 'oneclick',
   width: '310',
 });
var token = "";
myCaptcha.on('verifySuccess', function(security_code){
	  if (security_code != null || security_code != 'undefined' || security_code != ''){
	     token = security_code;  //security_code.split(':',1);
	  }
});
<?php }?>

    function login(){
		<?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
          	if (typeof validate == 'undefined') {
              	swal('Oops...',"请滑动验证码来完成验证",'warning');
              	return;
          	}
          	if (!validate) {
              	swal('Oops...',"请滑动验证码来完成验证",'warning');
              	return;
          	}
        <?php }?>
		<?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
        if (token == null || token == '' || token == 'undifined'){
           swal('Oops...',"请滑动验证码来完成验证",'warning');
		   return;
        }
        <?php }?>
		document.getElementById("login").disabled = true;

        $.ajax({
            type:"POST",
            url:"/auth/login",
            dataType:"json",
            data:{
                email: $("#email").val(),
                passwd: $("#passwd").val(),
			  <?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
			    token: token,
			  <?php }?>
			//	code: $("#code").val(),//取消两步验证
              <?php if ($_smarty_tpl->tpl_vars['recaptcha_sitekey']->value != null) {?>
                recaptcha: grecaptcha.getResponse(),
              <?php }?>
                remember_me: $("#remember_me:checked").val()<?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>,
                geetest_challenge: validate.geetest_challenge,
                geetest_validate: validate.geetest_validate,
                geetest_seccode: validate.geetest_seccode<?php }?>
            },
            success:function(data){
          		var user= data.user;
                var jump_link = data.href;
                  /*
                  swal({
                    title: "Merry Christmas~!",
                    html: "<span style='color: #9954bb; font-size:2rem; font-weight: 500'>"+user+"</span>",
                    imageUrl: "/images/shendan.png",
                  }).then(function(){
                    window.location.href = '/user';
                  });
                */
                if(data.ret == 1){
                    swal({
                      title: "<span style='color: #9954bb; font-size:2rem; font-weight: 500'>"+user+"</span>",
                      text: "Welcome, 欢迎进入<?php echo $_smarty_tpl->tpl_vars['config']->value["appName"];?>
~!",

                    });
                  window.setTimeout('location.href="'+jump_link+'"', <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                }else{
                	swal('Oops...',data.msg,'error');
					document.getElementById("login").disabled = false;
                    <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
                     captcha.reset();
                    <?php }?>
				    <?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
				     myCaptcha.reload();
				    <?php }?>
		        }
            },
            error:function(jqXHR){
				swal('Oops...',"发生错误"+jqXHR.status,'error');
				document.getElementById("login").disabled = false;
                <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
                 captcha.reset();
                <?php }?>
				<?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
		         myCaptcha.reload();
			    <?php }?>
		    }
        });
    }
    $("html").keydown(function(event){
        if(event.keyCode==13){
            login();
        }
    });
    $("#login").click(function(){
        login();
    });

})
<?php echo '</script'; ?>
>

  <?php }
}
