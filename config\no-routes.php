<?php

use Slim\App;
use Slim\Container;
use App\Middleware\Auth;
use App\Middleware\Guest;
use App\Middleware\Home;
use App\Middleware\Admin;
use App\Middleware\Api;
use App\Middleware\Mu;
use App\Middleware\Mod_Mu;
use Zeuxisoo\Whoops\Provider\Slim\WhoopsMiddleware;

       
/***
 * The slim documents: http://www.slimframework.com/docs/objects/router.html
 */

// config
$debug = false;
if (defined("DEBUG")) {
    $debug = true;
}
/***
 * $configuration = [
 * 'settings' => [
 * 'displayErrorDetails' => $debug,
 * ]
 * ];
 * $c = new Container($configuration);
 ***/

// Make a Slim App
// $app = new App($c);

$configuration = [
    'settings' => [
        'debug' => $debug,
        'whoops.editor' => 'sublime',
        'displayErrorDetails' => $debug
    ]
];

$container = new Container($configuration);


$container['notFoundHandler'] = function ($c) {
    return function ($request, $response) use ($c) {
        return $response->withAddedHeader('Location', '/404');
    };
};

$container['notAllowedHandler'] = function ($c) {
    return function ($request, $response, $methods) use ($c) {
        return $response->withAddedHeader('Location', '/405');
    };
};

if ($debug == false) {
    $container['errorHandler'] = function ($c) {
        return function ($request, $response, $exception) use ($c) {
            return $response->withAddedHeader('Location', '/500');
        };
    };
}

$app = new App($container);
$app->add(new WhoopsMiddleware);



// Home
$app->get('/', 'App\Controllers\HomeController:index')->add(new Home());
$app->get('/index', 'App\Controllers\HomeController:index_two')->add(new Home());
$app->get('/404', 'App\Controllers\HomeController:page404');
$app->get('/405', 'App\Controllers\HomeController:page405');
$app->get('/500', 'App\Controllers\HomeController:page500');

$app->get('/staff', 'App\Controllers\HomeController:staff');
$app->post('/telegram_callback', 'App\Controllers\HomeController:telegram');
$app->get('/yft/notify', 'App\Services\Gateway\YftPay:notify');//yft uses GET
$app->post('/tomato_back/{type}', 'App\Services\PaymentA:notify'); //番茄支付的回调
$app->get('/tomato_back/{type}', 'App\Services\PaymentB:notify'); //番茄支付的通知

$app->post('/flyfoxpay_back/{type}', 'App\Services\PaymentA:notify'); //跃空聚合支付回调
$app->get('/flyfoxpay_back/{type}', 'App\Services\PaymentB:notify');  //跃空聚合支付通知

$app->get('/pay/wx_umikm', 'App\Services\PaymentB:umikmHTML'); //微信小薇支付对接优米家
$app->get('/pay/ali_idtpay', 'App\Services\PaymentA:idtpayHTML'); //idtpay支付宝通道
$app->get('/pay/wx_idtpay', 'App\Services\PaymentB:idtpayHTML'); //idtpay微信通道
$app->post('/idtpay_back/{type}', 'App\Services\PaymentA:notify'); //idtpay支付回调
$app->get('/idtpay_back/{type}', 'App\Services\PaymentB:notify');  //idtpay支付通知

// User Center
$app->group('/user', function () {
    $this->get('', 'App\Controllers\UserController:index');
    $this->get('/', 'App\Controllers\UserController:index');
    $this->post('/get_node_class', 'App\Controllers\UserController:get_node_class');
	$this->get('/agent', 'App\Controllers\UserController:agent');
  	$this->post('/agentbuy', 'App\Controllers\UserController:agentbuy');
    $this->delete('/agentdelete', 'App\Controllers\UserController:agentdelete');
  	$this->post('/addUser', 'App\Controllers\UserController:addUser');
    $this->post('/agent/ajax', 'App\Controllers\UserController:agentajax');
  
    $this->post('/checkin', 'App\Controllers\UserController:doCheckin');
    $this->get('/node', 'App\Controllers\UserController:node');
    $this->get('/tutorial', 'App\Controllers\UserController:tutorial');
  //  $this->get('/announcement', 'App\Controllers\UserController:announcement');
    $this->get('/donate', 'App\Controllers\UserController:donate');
    $this->get('/lookingglass', 'App\Controllers\UserController:lookingglass');
    $this->get('/node/{id}', 'App\Controllers\UserController:nodeInfo');
    $this->get('/node/{id}/ajax', 'App\Controllers\UserController:nodeAjax');
    $this->get('/profile', 'App\Controllers\UserController:profile');
    $this->get('/invite', 'App\Controllers\UserController:invite');
    $this->post('/invite/ref_acc', 'App\Controllers\UserController:invite_ref_acc');
    $this->post('/invite/ref_money', 'App\Controllers\UserController:invite_ref_money');
    $this->post('/invite/tx_money', 'App\Controllers\UserController:invite_tx_money');
    $this->post('/invite/change_class', 'App\Controllers\UserController:invite_ref_class');
  
    $this->get('/detect', 'App\Controllers\UserController:detect_index');
    $this->get('/detect/log', 'App\Controllers\UserController:detect_log');
    $this->get('/subscribe_log', 'App\Controllers\UserController:subscribe_log');
    $this->get('/essay', 'App\Controllers\UserController:essay');
    $this->get('/disable', 'App\Controllers\UserController:disable');
    // getUserAllURL
    $this->get('/getUserAllURL', 'App\Controllers\UserController:getUserAllURL');
  
    $this->get('/shop', 'App\Controllers\UserController:shop');
    $this->post('/coupon_check', 'App\Controllers\UserController:CouponCheck');
    $this->post('/buy', 'App\Controllers\UserController:buy');
    $this->post('/buy_traffic', 'App\Controllers\UserController:buy_traffic');
	$this->post('/shop_refund', 'App\Controllers\UserController:shop_refund');
	
	//pay_success
	$this->get('/pay_success', 'App\Controllers\UserController:pay_success');
    // Relay Mange
    $this->get('/relay', 'App\Controllers\RelayController:index');
    $this->get('/relay/create', 'App\Controllers\RelayController:create');
    $this->post('/relay', 'App\Controllers\RelayController:add');
    $this->get('/relay/{id}/edit', 'App\Controllers\RelayController:edit');
    $this->put('/relay/{id}', 'App\Controllers\RelayController:update');
    $this->delete('/relay', 'App\Controllers\RelayController:delete');

    $this->get('/ticket', 'App\Controllers\UserController:ticket');
    $this->get('/ticket/create', 'App\Controllers\UserController:ticket_create');
    $this->post('/ticket', 'App\Controllers\UserController:ticket_add');
    $this->get('/ticket/{id}/view', 'App\Controllers\UserController:ticket_view');
    $this->put('/ticket/{id}', 'App\Controllers\UserController:ticket_update');
    $this->post('/upload/invite', 'App\Controllers\UserController:up_images');
  	$this->post('/upload/ticket', 'App\Controllers\UserController:up_images_t');
  
    $this->post('/buy_invite', 'App\Controllers\UserController:buyInvite');
    $this->post('/custom_invite', 'App\Controllers\UserController:customInvite');
    $this->get('/edit', 'App\Controllers\UserController:edit');
    $this->post('/password', 'App\Controllers\UserController:updatePassword');
    $this->post('/wechat', 'App\Controllers\UserController:updateWechat');
    $this->post('/ssr', 'App\Controllers\UserController:updateSSR');
    $this->post('/theme', 'App\Controllers\UserController:updateTheme');
    $this->post('/mail', 'App\Controllers\UserController:updateMail');
    $this->post('/sspwd', 'App\Controllers\UserController:updateSsPwd');
    $this->post('/method', 'App\Controllers\UserController:updateMethod');
    $this->post('/hide', 'App\Controllers\UserController:updateHide');

    $this->get('/trafficlog', 'App\Controllers\UserController:trafficLog');
    $this->get('/kill', 'App\Controllers\UserController:kill');
    $this->post('/kill', 'App\Controllers\UserController:handleKill');
    $this->get('/logout', 'App\Controllers\UserController:logout');
    $this->get('/backtoadmin', 'App\Controllers\UserController:backtoadmin');
    $this->get('/code', 'App\Controllers\UserController:code');
    //易付通路由定义 start
    $this->post('/code/yft/pay', 'App\Services\Gateway\YftPay:yftPay');
    $this->get('/code/yft/pay/result', 'App\Services\Gateway\YftPay:notify');
    $this->post('/code/yft', 'App\Services\Gateway\YftPay:yft');
    $this->get('/yftOrder', 'App\Services\Gateway\YftPay:yftOrder');
    //易付通路由定义 end
  
    $this->get('/code_check', 'App\Controllers\UserController:code_check');
  
    $this->post('/code', 'App\Controllers\UserController:codepost');
    $this->post('/gacheck', 'App\Controllers\UserController:GaCheck');
    $this->post('/gaset', 'App\Controllers\UserController:GaSet');
    $this->get('/gareset', 'App\Controllers\UserController:GaReset');
    $this->get('/telegram_reset', 'App\Controllers\UserController:telegram_reset');
    $this->post('/resetport', 'App\Controllers\UserController:ResetPort');
    $this->post('/specifyport', 'App\Controllers\UserController:SpecifyPort');
    $this->post('/pacset', 'App\Controllers\UserController:PacSet');

    $this->post('/unblock', 'App\Controllers\UserController:Unblock');
  
   // $this->get('/bought', 'App\Controllers\UserController:bought');
    $this->delete('/bought', 'App\Controllers\UserController:deleteBoughtGet');

    $this->get('/url_reset', 'App\Controllers\UserController:resetURL');

    $this->get('/inviteurl_reset', 'App\Controllers\UserController:resetInviteURL');

    //Reconstructed Payment System  //双端支付
    $this->post('/payment/purchase', 'App\Services\Payment:purchase');
    $this->post('/payment_a/purchase', 'App\Services\PaymentA:purchase');
    $this->post('/payment_b/purchase', 'App\Services\PaymentB:purchase');
  
    $this->get('/payment/return', 'App\Services\Payment:returnHTML');
    $this->get('/payment_a/return', 'App\Services\PaymentA:returnHTML');
    $this->get('/payment_b/return', 'App\Services\PaymentB:returnHTML');
  
      // Crypto Payment - BTC, ETH, EOS, BCH, LTC etch  //数字货币通道
    $this->post('/payment/bitpay/purchase', 'App\Services\BitPayment:purchase');
    $this->get('/payment/bitpay/return', 'App\Services\BitPayment:returnHTML');
  
     // Paypal通道
    $this->post('/payment/paypal/purchase', 'App\Services\PaypalPayment:purchase');
    $this->get('/payment/paypal/return', 'App\Services\PaypalPayment:returnHTML');
})->add(new Auth());

//备用通道
$app->group('/payment', function () {
    $this->post('/notify', 'App\Services\Payment:notify'); 
    $this->post('/notify/{type}', 'App\Services\Payment:notify'); 
    $this->post('/status', 'App\Services\Payment:getStatus'); 
  
    $this->post('/bitpay/notify', 'App\Services\BitPayment:notify'); 
    $this->post('/bitpay/status', 'App\Services\BitPayment:getStatus');
  
    $this->post('/paypal/notify', 'App\Services\PaypalPayment:notify'); 
    $this->post('/paypal/status', 'App\Services\PaypalPayment:getStatus');
});

$app->group('/payment_a', function () {
    $this->post('/notify', 'App\Services\PaymentA:notify');
    $this->post('/notify/{type}', 'App\Services\PaymentA:notify');
    $this->post('/status', 'App\Services\PaymentA:getStatus');
});

$app->group('/payment_b', function () {
    $this->post('/notify', 'App\Services\PaymentB:notify');
    $this->post('/notify/{type}', 'App\Services\PaymentB:notify');
    $this->post('/status', 'App\Services\PaymentB:getStatus');
});

// Auth
$app->group('/auth', function () {
    $this->get('/login', 'App\Controllers\AuthController:login');
    $this->post('/qrcode_check', 'App\Controllers\AuthController:qrcode_check');
    $this->post('/login', 'App\Controllers\AuthController:loginHandle');
    $this->post('/qrcode_login', 'App\Controllers\AuthController:qrcode_loginHandle');
    $this->get('/register', 'App\Controllers\AuthController:register');
    $this->post('/register', 'App\Controllers\AuthController:registerHandle');
    $this->post('/send', 'App\Controllers\AuthController:sendVerify');
    $this->get('/logout', 'App\Controllers\AuthController:logout');
    $this->get('/telegram_oauth', 'App\Controllers\AuthController:telegram_oauth');
    $this->get('/login_getCaptcha', 'App\Controllers\AuthController:getCaptcha');
})->add(new Guest())->add(new Home());

// Password
$app->group('/password', function () {
    $this->get('/reset', 'App\Controllers\PasswordController:reset');
    $this->post('/reset', 'App\Controllers\PasswordController:handleReset');
    $this->get('/token/{token}', 'App\Controllers\PasswordController:token');
    $this->post('/token/{token}', 'App\Controllers\PasswordController:handleToken');
})->add(new Guest())->add(new Home());

// Admin
$app->group('/admin', function () {
    $this->get('', 'App\Controllers\AdminController:index');
    $this->get('/', 'App\Controllers\AdminController:index');
    $this->post('/traffic_used/ajax', 'App\Controllers\AdminController:TrafficUsedTop');
    $this->get('/trafficlog', 'App\Controllers\AdminController:trafficLog');
    $this->post('/trafficlog/ajax', 'App\Controllers\AdminController:ajax_trafficLog');
    // Node Mange
    $this->get('/node', 'App\Controllers\Admin\NodeController:index');
    $this->get('/node/create', 'App\Controllers\Admin\NodeController:create');
    $this->post('/node', 'App\Controllers\Admin\NodeController:add');
    $this->get('/node/{id}/edit', 'App\Controllers\Admin\NodeController:edit');
    $this->put('/node/{id}', 'App\Controllers\Admin\NodeController:update');
    $this->delete('/node', 'App\Controllers\Admin\NodeController:delete');
    $this->post('/node/ajax', 'App\Controllers\Admin\NodeController:ajax');
    $this->post('/node/check_nodes_gfw', 'App\Controllers\Admin\NodeController:check_nodes_gfw');

    $this->get('/ticket', 'App\Controllers\Admin\TicketController:index');
    $this->get('/ticket/{id}/view', 'App\Controllers\Admin\TicketController:show');
    $this->put('/ticket/{id}', 'App\Controllers\Admin\TicketController:update');
    $this->post('/ticket/ajax', 'App\Controllers\Admin\TicketController:ajax');

    // Relay Mange
    $this->get('/relay', 'App\Controllers\Admin\RelayController:index');
    $this->get('/relay/create', 'App\Controllers\Admin\RelayController:create');
    $this->post('/relay', 'App\Controllers\Admin\RelayController:add');
    $this->get('/relay/{id}/edit', 'App\Controllers\Admin\RelayController:edit');
    $this->put('/relay/{id}', 'App\Controllers\Admin\RelayController:update');
    $this->delete('/relay', 'App\Controllers\Admin\RelayController:delete');
    $this->post('/relay/ajax', 'App\Controllers\Admin\RelayController:ajax_relay');

    // Shop Mange
    $this->get('/shop', 'App\Controllers\Admin\ShopController:index');
    $this->post('/shop/ajax', 'App\Controllers\Admin\ShopController:ajax_shop');

    $this->get('/bought', 'App\Controllers\Admin\ShopController:bought');
    $this->delete('/bought', 'App\Controllers\Admin\ShopController:deleteBoughtGet');
    $this->post('/bought/ajax', 'App\Controllers\Admin\ShopController:ajax_bought');

    $this->get('/shop/create', 'App\Controllers\Admin\ShopController:create');
    $this->post('/shop', 'App\Controllers\Admin\ShopController:add');
    $this->get('/shop/{id}/edit', 'App\Controllers\Admin\ShopController:edit');
    $this->put('/shop/{id}', 'App\Controllers\Admin\ShopController:update');
    $this->delete('/shop', 'App\Controllers\Admin\ShopController:deleteGet');

    // Ann Mange
    $this->get('/announcement', 'App\Controllers\Admin\AnnController:index');
    $this->get('/announcement/create', 'App\Controllers\Admin\AnnController:create');
    $this->post('/announcement', 'App\Controllers\Admin\AnnController:add');
    $this->get('/announcement/{id}/edit', 'App\Controllers\Admin\AnnController:edit');
    $this->put('/announcement/{id}', 'App\Controllers\Admin\AnnController:update');
    $this->delete('/announcement', 'App\Controllers\Admin\AnnController:delete');
    $this->post('/announcement/ajax', 'App\Controllers\Admin\AnnController:ajax');
    $this->post('/upload/announcement', 'App\Controllers\Admin\AnnController:up_images_a');
  
    // Essay Mange
    $this->get('/essay', 'App\Controllers\Admin\EssayController:index');
    $this->get('/essay/create', 'App\Controllers\Admin\EssayController:create');
    $this->post('/essay', 'App\Controllers\Admin\EssayController:add');
    $this->get('/essay/{id}/edit', 'App\Controllers\Admin\EssayController:edit');
    $this->put('/essay/{id}', 'App\Controllers\Admin\EssayController:update');
    $this->delete('/essay', 'App\Controllers\Admin\EssayController:delete');
    $this->post('/essay/ajax', 'App\Controllers\Admin\EssayController:ajax');
    $this->post('/upload/essay', 'App\Controllers\Admin\EssayController:up_images_e');
  
	// nodegroup
	$this->get('/nodegroup', 'App\Controllers\Admin\NodeGroupController:index');
    $this->get('/nodegroup/create', 'App\Controllers\Admin\NodeGroupController:create');
    $this->post('/nodegroup', 'App\Controllers\Admin\NodeGroupController:add');
    $this->get('/nodegroup/{id}/edit', 'App\Controllers\Admin\NodeGroupController:edit');
    $this->put('/nodegroup/{id}', 'App\Controllers\Admin\NodeGroupController:update');
    $this->delete('/nodegroup', 'App\Controllers\Admin\NodeGroupController:delete');
    $this->post('/nodegroup/ajax', 'App\Controllers\Admin\NodeGroupController:ajax');
  
	// tixian
	$this->get('/tixian', 'App\Controllers\Admin\TixianController:index');
    $this->get('/tixian/{id}/edit', 'App\Controllers\Admin\TixianController:edit');
    $this->put('/tixian/{id}', 'App\Controllers\Admin\TixianController:update');
    $this->post('/tixian/ajax', 'App\Controllers\Admin\TixianController:ajax');
  
	// level 
	$this->get('/level', 'App\Controllers\Admin\LevelController:index');
    $this->get('/level/create', 'App\Controllers\Admin\LevelController:create');
    $this->post('/level', 'App\Controllers\Admin\LevelController:add');
    $this->get('/level/{id}/edit', 'App\Controllers\Admin\LevelController:edit');
    $this->put('/level/{id}', 'App\Controllers\Admin\LevelController:update');
    $this->delete('/level', 'App\Controllers\Admin\LevelController:delete');
    $this->post('/level/ajax', 'App\Controllers\Admin\LevelController:ajax');
  
    // Detect Mange
    $this->get('/detect', 'App\Controllers\Admin\DetectController:index');
    $this->get('/detect/create', 'App\Controllers\Admin\DetectController:create');
    $this->post('/detect', 'App\Controllers\Admin\DetectController:add');
    $this->get('/detect/{id}/edit', 'App\Controllers\Admin\DetectController:edit');
    $this->put('/detect/{id}', 'App\Controllers\Admin\DetectController:update');
    $this->delete('/detect', 'App\Controllers\Admin\DetectController:delete');
    $this->get('/detect/log', 'App\Controllers\Admin\DetectController:log');
    $this->post('/detect/ajax', 'App\Controllers\Admin\DetectController:ajax_rule');
    $this->post('/detect/log/ajax', 'App\Controllers\Admin\DetectController:ajax_log');
    
    // Detect Ban Mange  审计封禁
    $this->get('/detect/ban', 'App\Controllers\Admin\DetectBanLogController:index');
    $this->post('/detect/ban/ajax', 'App\Controllers\Admin\DetectBanLogController:ajax_log');
  
    $this->get('/auto', 'App\Controllers\Admin\AutoController:index');
    $this->get('/auto/create', 'App\Controllers\Admin\AutoController:create');
    $this->post('/auto', 'App\Controllers\Admin\AutoController:add');
    $this->delete('/auto', 'App\Controllers\Admin\AutoController:delete');
    $this->post('/auto/ajax', 'App\Controllers\Admin\AutoController:ajax');

    // IP Mange
    $this->get('/block', 'App\Controllers\Admin\IpController:block');
    $this->get('/unblock', 'App\Controllers\Admin\IpController:unblock');
    $this->post('/unblock', 'App\Controllers\Admin\IpController:doUnblock');
    $this->get('/login', 'App\Controllers\Admin\IpController:index');
    $this->get('/alive', 'App\Controllers\Admin\IpController:alive');
    $this->post('/block/ajax', 'App\Controllers\Admin\IpController:ajax_block');
    $this->post('/unblock/ajax', 'App\Controllers\Admin\IpController:ajax_unblock');
    $this->post('/login/ajax', 'App\Controllers\Admin\IpController:ajax_login');
    $this->post('/alive/ajax', 'App\Controllers\Admin\IpController:ajax_alive');

    // Code Mange
    $this->get('/code', 'App\Controllers\Admin\CodeController:index');
    $this->get('/code/create', 'App\Controllers\Admin\CodeController:create');
    $this->post('/code', 'App\Controllers\Admin\CodeController:add');
    $this->get('/donate/create', 'App\Controllers\Admin\CodeController:donate_create');
    $this->post('/donate', 'App\Controllers\Admin\CodeController:donate_add');
    $this->post('/code/ajax', 'App\Controllers\Admin\CodeController:ajax_code');
  
    // Subscribe Log Mange 订阅记录
    $this->get('/subscribe', 'App\Controllers\Admin\SubscribeLogController:index');
    $this->post('/subscribe/ajax', 'App\Controllers\Admin\SubscribeLogController:ajax_subscribe_log');
  
    // User Mange
    $this->get('/user', 'App\Controllers\Admin\UserController:index');
    $this->get('/user/{id}/edit', 'App\Controllers\Admin\UserController:edit');
    $this->put('/user/{id}', 'App\Controllers\Admin\UserController:update');
    $this->delete('/user', 'App\Controllers\Admin\UserController:delete');
    $this->post('/user/changetouser', 'App\Controllers\Admin\UserController:changetouser');
    $this->post('/user/ajax', 'App\Controllers\Admin\UserController:ajax');
    $this->post('/user/addclass', 'App\Controllers\Admin\UserController:addclass');
    $this->post('/user/addtraffic', 'App\Controllers\Admin\UserController:addtraffic');
    $this->post('/user/addmoney', 'App\Controllers\Admin\UserController:addmoney');
    $this->post('/user/sendemail', 'App\Controllers\Admin\UserController:sendemail');
  
    $this->get('/coupon', 'App\Controllers\AdminController:coupon');
    $this->post('/coupon', 'App\Controllers\AdminController:addCoupon');
    $this->post('/coupon/ajax', 'App\Controllers\AdminController:ajax_coupon');
    $this->delete('/coupon', 'App\Controllers\AdminController:deleteCoupon');

    $this->get('/profile', 'App\Controllers\AdminController:profile');
    $this->get('/invite', 'App\Controllers\AdminController:invite');
    $this->post('/invite', 'App\Controllers\AdminController:addInvite');
    $this->delete('/invitedelete', 'App\Controllers\AdminController:invitedelete');
    $this->get('/logout', 'App\Controllers\AdminController:logout');
    $this->post('/payback/ajax', 'App\Controllers\AdminController:ajax_payback');
    $this->get('/yftOrder', 'App\Services\Gateway\YftPay:yftOrderForAdmin');
  	$this->post('/user/adds', 'App\Controllers\Admin\UserController:addUser');
})->add(new Admin());

// API
$app->group('/api', function () {
    $this->get('/token/{token}', 'App\Controllers\ApiController:token');
    $this->post('/token', 'App\Controllers\ApiController:newToken');
    $this->get('/node', 'App\Controllers\ApiController:node')->add(new Api());
    $this->get('/user/{id}', 'App\Controllers\ApiController:userInfo')->add(new Api());
    $this->get('/sublink','App\Controllers\Client\ClientApiController:GetSubLink');
});

// mu
$app->group('/mu', function () {
    $this->get('/users', 'App\Controllers\Mu\UserController:index');
    $this->post('/users/{id}/traffic', 'App\Controllers\Mu\UserController:addTraffic');
    $this->post('/nodes/{id}/online_count', 'App\Controllers\Mu\NodeController:onlineUserLog');
    $this->post('/nodes/{id}/info', 'App\Controllers\Mu\NodeController:info');
})->add(new Mu())->add(new Home());

// mu
$app->group('/mod_mu', function () {
    $this->get('/nodes/{id}/info', 'App\Controllers\Mod_Mu\NodeController:get_info');
    $this->get('/users', 'App\Controllers\Mod_Mu\UserController:index');
    $this->post('/users/traffic', 'App\Controllers\Mod_Mu\UserController:addTraffic');
    $this->post('/users/aliveip', 'App\Controllers\Mod_Mu\UserController:addAliveIp');
    $this->post('/users/detectlog', 'App\Controllers\Mod_Mu\UserController:addDetectLog');
    $this->post('/nodes/{id}/info', 'App\Controllers\Mod_Mu\NodeController:info');

    $this->get('/nodes', 'App\Controllers\Mod_Mu\NodeController:get_all_info');
    $this->post('/nodes/config', 'App\Controllers\Mod_Mu\NodeController:getConfig');

    $this->get('/func/detect_rules', 'App\Controllers\Mod_Mu\FuncController:get_detect_logs');
    $this->get('/func/relay_rules', 'App\Controllers\Mod_Mu\FuncController:get_relay_rules');
    $this->post('/func/block_ip', 'App\Controllers\Mod_Mu\FuncController:addBlockIp');
    $this->get('/func/block_ip', 'App\Controllers\Mod_Mu\FuncController:get_blockip');
    $this->get('/func/unblock_ip', 'App\Controllers\Mod_Mu\FuncController:get_unblockip');
    $this->post('/func/speedtest', 'App\Controllers\Mod_Mu\FuncController:addSpeedtest');
    $this->get('/func/autoexec', 'App\Controllers\Mod_Mu\FuncController:get_autoexec');
    $this->post('/func/autoexec', 'App\Controllers\Mod_Mu\FuncController:addAutoexec');
    $this->get('/func/ping', 'App\Controllers\Mod_Mu\FuncController:ping');
    //============================================
})->add(new Mod_Mu())->add(new Home());

// res
$app->group('/res', function () {
    $this->get('/captcha/{id}', 'App\Controllers\ResController:captcha');
});
$app->group('/link', function () {
    $this->get('/{token}', 'App\Controllers\LinkController:GetContent');
});

//doc
$app->group('/doc', function () {
    $this->get('', 'App\Controllers\HomeController:getDocCenter');
    $this->get('/', 'App\Controllers\HomeController:getDocCenter');
});
$app->get('/sublink', 'App\Controllers\HomeController:getSubLink');
$app->get('/getsoft', 'App\Controllers\HomeController:getsoft');
//doc end

// Vue
$app->get('/logout', 'App\Controllers\VueController:vuelogout');
$app->get('/globalconfig', 'App\Controllers\VueController:getGlobalConfig');
$app->get('/getuserinfo', 'App\Controllers\VueController:getUserInfo');
$app->get('/getuserinviteinfo', 'App\Controllers\VueController:getUserInviteInfo');
$app->get('/getusershops', 'App\Controllers\VueController:getUserShops');
$app->get('/getcredit', 'App\Controllers\VueController:getCredit');
$app->get('/getallresourse', 'App\Controllers\VueController:getAllResourse');
$app->get('/getnewsubtoken', 'App\Controllers\VueController:getNewSubToken');
$app->get('/getnewinvotecode', 'App\Controllers\VueController:getNewInviteCode');
$app->get('/gettransfer', 'App\Controllers\VueController:getTransfer');
$app->get('/getCaptcha', 'App\Controllers\VueController:getCaptcha');
$app->post('/getChargeLog', 'App\Controllers\VueController:getChargeLog');
/***$app->get('/getnodelist', 'App\Controllers\VueController:getNodeList');白嫖漏洞（机场域名+/getnodelist）*/

// chenApp
$app->group("/appApi", function () {
    $this->post("/token", \App\Controllers\AppApiController::class . ":newToken");
    $this->get("/getSsrUser", \App\Controllers\AppApiController::class . ":ssr_index");
    $this->get("/getUser", \App\Controllers\AppApiController::class . ":index");
    $this->get('/checkin', \App\Controllers\AppApiController::class . ':doCheckIn');
    $this->get('/redirect', \App\Controllers\AppApiController::class . ':redirect');
});
// chenApp end
// Run Slim Routes for App
\App\V1\Routes::loadRoutes($app);
$app->run();
