<?php
/* Smarty version 3.1.33, created on 2022-07-14 23:52:56
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/index.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62d03bd8295b36_79232665',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'd54d847264293e28bdc5aadfa6c13122749b0030' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/index.tpl',
      1 => 1657813972,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:user/main.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:user/footer.tpl' => 1,
  ),
),false)) {
function content_62d03bd8295b36_79232665 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:user/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
$_smarty_tpl->_assignInScope('ssr_prefer', App\Utils\URL::SSRCanConnect($_smarty_tpl->tpl_vars['user']->value,0));
$_smarty_tpl->_assignInScope('pre_user', App\Utils\URL::cloneUser($_smarty_tpl->tpl_vars['user']->value));?>

    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Default</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user"><i class="fa fa-group" style="font-size:18px;color:cream"></i>用户中心</a></li>
                   <li class="breadcrumb-item"><a href="./"><i class="fa fa-home fa-lg" style="font-size:28px;color:cream"></i>首页</a></li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/code" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 充值</a>
              <a href="/user/shop" class="btn btn-sm btn-neutral">商店</a>
            </div>
          </div>
          <!-- Card stats -->
          <div class="row">
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">当前用户等级</h5>
                      <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['level']->value->name;?>
 <?php echo $_smarty_tpl->tpl_vars['group']->value->name;?>
<small> 用户</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-red text-white rounded-circle shadow">
                        <i class="ni ni-active-40"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/user/shop"><i class="ni ni-spaceship icon-ver"></i>&nbsp;提升用户等级</a></span>
                  </p>
                </div>
              </div>
            </div>
			<div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">账户余额</h5>
                      <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['user']->value->money;?>
 <small>火箭币</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-green text-white rounded-circle shadow">
                        <i class="ni ni-money-coins"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/user/code"><i class="ni ni-credit-card icon-ver"></i>&nbsp;充值</a></span>
                  </p>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">在线设备数</h5>
					  <?php if ($_smarty_tpl->tpl_vars['user']->value->node_connector != 0) {?>
                      <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['user']->value->online_ip_count();?>
/<?php echo $_smarty_tpl->tpl_vars['user']->value->node_connector;?>
 <small>台</small></span>
					  <?php } else { ?>
					  <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['user']->value->online_ip_count();?>
/不限制 <small>台</small></span>
					  <?php }?>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-orange text-white rounded-circle shadow">
                        <i class="ni ni-chart-pie-35 fa-spin"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/user/shop"><i class="ni ni-laptop icon-ver"></i>&nbsp;增加设备数量</a></span>
                  </p>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">宽带速度</h5>
					  <?php if ($_smarty_tpl->tpl_vars['user']->value->node_speedlimit != 0) {?>
                      <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['user']->value->node_speedlimit;?>
 <small>Mbps</small></span>
					  <?php } else { ?>
					  <span class="h2 font-weight-bold mb-0">无限制</span>
					  <?php }?>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                        <i class="ni ni-chart-bar-32"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/user/ticket/create"><i class="ni ni-spaceship icon-ver"></i>&nbsp;提升带宽速率</a></span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <div class="row">
        <div class="col-lg-7 col-md-7">
          <div class="card">
            <div class="card-header bg-transparent">
               <h3 class="mb-0"><i class="ni ni-bell-55 ni-lg icon-ver" style="font-size:26px;color:red"></i>&nbsp;公告栏
               <i class="fa fa-chrome fa-spin" style="font-size:18px;color:royalblue"></i>
				<i class="fa fa-snowflake-o fa-spin" style="font-size:18px;color:green"></i>
				<i class="fa fa-safari fa-spin" style="font-size:18px;color:violet"></i>
				<i class="fa fa-rotate-right fa-spin" style="font-size:18px;color:pink"></i>
				<i class="fa fa-circle-o-notch fa-spin" style="font-size:18px;color:green"></i>
				<i class="fa fa-refresh fa-spin" style="font-size:18px;color:green"></i>
				<i class="fa fa-futbol-o fa-spin" style="font-size:18px;color:cyan"></i>
				<i class="fa fa-empire fa-spin" style="font-size:18px;color:red"></i>
				<i class="fa fa-first-order fa-spin" style="font-size:18px;color:green"></i>
				<i class="fa fa-firefox fa-spin" style="font-size:18px;color:nature"></i>
				<i class="fa fa-spinner fa-pulse" style="font-size:18px;color:green"></i>
				<i class="fa fa-cog fa-spin" style="font-size:18px;color:blue"></i>
				<i class="fa fa-history fa-spin" style="font-size:18px;color:coral"></i>
				<i class="fa fa-clock-o fa-spin" style="font-size:22px;color:peru"></i>
				<i class="fa fa-diamond" style="font-size:18px;color:red"></i></h3>
            </div>
            <div class="card-body">
			    <ul class="list-group list-group-flush" data-toggle="">
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['anns']->value, 'ann');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['ann']->value) {
?>
				<li class="checklist-entry list-group-item flex-column px-4">
				  <div class="checklist-item checklist-item-danger">
				    <div class="checklist-info">
				        <p class="mb-0"><?php echo $_smarty_tpl->tpl_vars['ann']->value->content;?>
</p>
					</div>
				  </div>
				</li>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>

                <li class="checklist-entry list-group-item flex-column px-4">
                  <div class="checklist-item checklist-item-success">
                    <div class="checklist-info">
                      <h5 class="checklist-title mb-0">关注收藏我们不迷路</h5>
                        <p class="mb-0">
						<?php if ($_smarty_tpl->tpl_vars['config']->value['telegram_group_name'] == null) {?>
							<small>TG频道：&nbsp;<a href="#">######</a></small>
							<small>TG群组：&nbsp;<a href="#">######</a></small>
						<?php } else { ?>
							<!--small>TG频道：&nbsp;<a href="<?php echo $_smarty_tpl->tpl_vars['config']->value['telegram_channel_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['config']->value['telegram_channel_name'];?>
</a></small>-->
							<small>永久网站更新网址：https://shadowingy.tokin.ml&nbsp&nbsp;<!--<a href="https://shadowingy.tokin.ml"><?php echo $_smarty_tpl->tpl_vars['config']->value['telegram_channel_name'];?>
</a>--></small>
							<!--<small>TG群组：&nbsp;<a href="<?php echo $_smarty_tpl->tpl_vars['config']->value['telegram_group_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['config']->value['telegram_group_name'];?>
</a></small>-->
						<?php }?>
						</p>
                    </div>
                  </div>
                </li>
				<?php if ($_smarty_tpl->tpl_vars['config']->value["enable_admin_contact"] == 'true') {?>
                <li class="checklist-entry list-group-item flex-column px-4">
                  <div class="checklist-item checklist-item-warning">
                    <div class="checklist-info">
                      <h5 class="checklist-title mb-0">管理员联系方式</h5>
                    <?php if ($_smarty_tpl->tpl_vars['config']->value["admin_contact1"] != null) {?>
						<small><?php echo $_smarty_tpl->tpl_vars['config']->value["admin_contact1"];?>
</small>
					<?php }?>
					<?php if ($_smarty_tpl->tpl_vars['config']->value["admin_contact2"] != null) {?>
						<small><?php echo $_smarty_tpl->tpl_vars['config']->value["admin_contact2"];?>
</small>
					<?php }?>
					<?php if ($_smarty_tpl->tpl_vars['config']->value["admin_contact3"] != null) {?>
						<small><?php echo $_smarty_tpl->tpl_vars['config']->value["admin_contact3"];?>
</small>
					<?php }?>
                    </div>
                  </div>
                </li>
				<?php }?>
              </ul>
            </div>
          </div>
	    <div class="card">
            <div class="card-header bg-transparent">
               <h3 class="mb-0"><i class="ni ni-cloud-download-95 ni-lg icon-ver"></i>&nbsp;分级订阅</h3>
            </div>
			<div class="card-body">
			<h3><i class="ni ni-air-baloon"></i> <font color="#FF0000">购买后申请进群,群里有教程,有客服一对一指导售后.q群:748432512</font></h3>
			
      	<!--删除<p class="description">选择订阅的节点等级&nbsp;:&nbsp;</p>
				<select id="node_class" class="col-sm-6 form-control form-control-sm">
					<option value="-1">请选择节点等级</option>
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['levels']->value, 'level');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['level']->value) {
?>
					<option value="<?php echo $_smarty_tpl->tpl_vars['level']->value->level;?>
"><?php echo $_smarty_tpl->tpl_vars['level']->value->name;?>
</option>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>	
				</select>
				<button id="get_node_class" class="btn btn-primary btn-sm my-3">确认提交</button>
				<blockquote class="blockquote mb-0">
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>当只需要使用你所在等级的节点列表时, 可以使用此功能获取分级订阅.</p>
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>将返回 SSR 和 V2Ray 订阅链接,请注意区别放入相对应的客户端.</p>删除-->
				</blockquote>
			  <div id="sub_node_class" class="col-sm-6" style="display:none">
				<div class="input-group" style="padding:0">
					<input id="sub_node_ssr" type="text" class="form-control form-control-sm" name="input2"  value="" readonly disabled />
					<div class="input-group-append">
					  <a id="node_class_ssr_copy" class="copy-text btn btn-sm btn-primary text-white" data-clipboard-text="">复制 SSR</a>
				    </div>
				</div>
                <br>
				<div class="input-group" style="padding:0">
					<input id="sub_node_v2ray" type="text" class="form-control form-control-sm" name="input2" value="" readonly disabled />
					<div class="input-group-append">
					  <a id="node_class_v2ray_copy" class="copy-text btn btn-sm btn-primary text-white" data-clipboard-text="">复制 v2ray</a>
				    </div>
				</div>
			   </div>
			</div>
          </div>
		  
		<div class="card">
            <div class="card-header bg-transparent">
               <h3 class="mb-0 float-left"><i class="ni ni-spaceship ni-lg icon-ver fa-spin"></i>&nbsp;进阶教程- <font color="#FF0000">软件下载请点下边的下载按钮下载</font></h3>
			   <button type="button" class="reset-link btn btn-secondary btn-sm float-right"><i class="fa fa-repeat fa-lg icon-ver"></i>&nbsp;重置订阅</button>
            </div>
			<div class="card-body">
				<div class="nav-wrapper">
					<ul class="nav nav-pills nav-fill flex-column flex-md-row" id="tabs-icons-text" role="tablist">
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0 active" id="tabs-icons-text-1-tab" data-toggle="tab" href="#tabs-icons-text-1" role="tab" aria-controls="tabs-icons-text-1" aria-selected="true"><i class="ni ni-send mr-2"></i>通用</a>
						</li>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-2-tab" data-toggle="tab" href="#tabs-icons-text-2" role="tab" aria-controls="tabs-icons-text-2" aria-selected="false"><i class="fa fa-windows fa-lg mr-2"></i> Windows</a>
						</li>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-3-tab" data-toggle="tab" href="#tabs-icons-text-3" role="tab" aria-controls="tabs-icons-text-3" aria-selected="false"><i class="fa fa-apple fa-lg mr-2"></i> MacOS</a>
						</li>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-4-tab" data-toggle="tab" href="#tabs-icons-text-4" role="tab" aria-controls="tabs-icons-text-4" aria-selected="false"><i class="fa fa-mobile fa-lg mr-2"></i> iOS</a>
						</li>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-5-tab" data-toggle="tab" href="#tabs-icons-text-5" role="tab" aria-controls="tabs-icons-text-5" aria-selected="false"><i class="fa fa-android fa-lg mr-2"></i> Android</a>
						</li>
					</ul>
				</div>
	
				<div class="tab-content" id="myTabContent">
					<div class="tab-pane fade show active" id="tabs-icons-text-1" role="tabpanel" aria-labelledby="tabs-icons-text-1-tab">
						<p>
							<i class="ni ni-air-baloon"></i> <font color="#FF0000">购买后申请进群,群里有教程,有客服一对一指导售后.q群:748432512</font>
						</p>
						<!--<p>
						(1)&nbsp;[ SS ][暂不支持]:&nbsp;
                        </p>
                        <p>
						<button id="general_ss" class="copy-config btn btn-sm btn-outline-default" onclick=Copyconfig("/user/getUserAllURL?type=ss","#general_ss","")>全部 URL</button>
						</p>-->
						<p>
						(A)&nbsp;[QV2RAY][全站支持所有节点]:&nbsp;<a class="btn btn-success btn-sm" href="https://wwi.lanzoul.com/iEpA105ehyde" ><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;QV2RAY客户端</a>
						
						<a class="btn btn-outline-default" href="https://jq.qq.com/?_wv=1027&k=gcBA0GQQ" ><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;客户端进QQ群下载</a>
						
                        </p>
                        	<p>
						(B)&nbsp;[Clash_Windows][支持全站节点]:&nbsp;<a class="btn btn-success btn-sm" href="https://wwi.lanzoul.com/iEpA105ehyde" ><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;Clash客户端</a>
						<a class="btn btn-sm btn-outline-default" href="https://websub8.ssr89.xyz" target="_blank"><font color="#FF0000"><i class="fa fa-windows fa-lg mr-2"></i><i class="fa fa-apple fa-lg mr-2"></i><i class="fa fa-android fa-lg mr-2"></i>&nbsp;Clash订阅转换-A</font></a>
						
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['clashr'];?>
"><i class="fa fa-refresh fa-spin" style="font-size:18px;color:green"></i><i class="fa fa-windows"></i><i class="fa fa-apple fa-lg mr-2"></i><i class="fa fa-android fa-lg mr-2"></i>&nbsp;复制Clash订阅</button>
						
                        </p>
						<p>
						(1)&nbsp;[ SSR ][全站支持]:&nbsp;
						
						<a class="btn btn-sm btn-outline-default" href="https://tecknity.com/"><i class="fa fa-windows fa-lg mr-2"style="font-size:14px;color:orange"></i>&nbsp;Netfix奈非免费看</a>
						<a class="btn btn-sm btn-outline-default" href="https://qm.qq.com/cgi-bin/qm/qr?k=BOHVA7Vp4KbuwHscYeAw1LUoJPGCPK4x&jump_from=webapi"><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;Clash下载-所有客户端QQ群里下载</a>
						
                        </p>
                        <p>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value["ssr"];?>
&extend=0">订阅链接</button>
						<button id="general_ssr" class="copy-config btn btn-sm btn-outline-default" onclick=Copyconfig("/user/getUserAllURL?type=ssr","#general_ssr","")>全部 URL</button>
						<a class="btn btn-success btn-sm" href="https://wwi.lanzoul.com/iEpA105ehyde" ><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;下载客PC户端</a>
						<a class="btn btn-success btn-sm" href="https://wwi.lanzoul.com/iEpA105ehyde" ><i class="fa fa-apple fa-lg mr-2"></i>&nbsp;下载客户端</a>
						<a class="btn btn-success btn-sm" href="https://wwi.lanzoul.com/iEpA105ehyde" ><i class="fa fa-android fa-lg mr-2"></i>&nbsp;SSR+V2RAY客户端</a>
						<a class="btn btn-success btn-sm" href="https://wwi.lanzoul.com/iEpA105ehyde" ><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;SSTap客户端</a></a>
						</p>
						<p>
						(2)&nbsp;[ VMess ][全站支持]:&nbsp;
						<a class="btn btn-sm btn-outline-default"href="https://shadowingypro.xyz/sub-web.php" target="_blank"><font color="#FF0000"><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;Clash本地订阅转换-B</font></a>
						<a class="btn btn-sm btn-outline-default" href="./doc/#/iOS/iphone" target="_blank"><font color="#FF0000"><i class="fa fa-apple fa-lg mr-2"></i>&nbsp;苹果手机一键导入节点</font></a>
						
						
                        </p>
                        <p>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['v2ray'];?>
&extend=0">订阅链接</button>
						<button id="general_v2ray" class="copy-config btn btn-sm btn-outline-default" onclick=Copyconfig("/user/getUserAllURL?type=v2ray","#general_v2ray","")>全部 URL</button>
						<a class="btn btn-success btn-sm" href="https://wwi.lanzoul.com/iEpA105ehyde" ><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;V2RAY客户端</a>
						<a class="btn btn-success btn-sm" href="https://wwi.lanzoul.com/iEpA105ehyde" ><i class="fa fa-apple fa-lg mr-2"></i>&nbsp;V2RAY客户端</a>
						<a class="btn btn-success btn-sm" href="https://wwi.lanzoul.com/iEpA105ehyde" ><i class="fa fa-android fa-lg mr-2"></i>&nbsp;V2RAY+SSR客户端</a></a>
						<a class="btn btn-sm btn-outline-default" href="https://websub8.ssr89.xyz" target="_blank"><font color="#FF0000"><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;Clash订阅转换-C</font></a>
						
						</p>
						<p>
						(3)&nbsp;[ 苹果手机 ][全站支持]:&nbsp;
					    <a class="btn btn-sm btn-outline-default" href="./doc/#/iOS/iphone" target="_blank"><font color="#FF0000"><i class="fa fa-apple fa-lg mr-2"></i>&nbsp;苹果手机一键导入节点</font></a>
					    
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value["shadowrocket"];?>
">复制Shadowrocket订阅</button>
                        </p>
                        <p>
							<i class="ni ni-air-baloon"></i> <font color="#FF0000">购买后申请进群,群里有教程,有客服一对一指导售后.q群:748432512</font>
						</p>
					</div> 
					<div class="tab-pane fade" id="tabs-icons-text-2" role="tabpanel" aria-labelledby="tabs-icons-text-2-tab">
					<!--<p>
						(1)&nbsp;SSD-[ SS ]使用方法:&nbsp;
						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/Windows/ShadowsocksD" target="_blank">教程文档</a>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['ssd'];?>
">复制订阅</button>
						<button id="win_ssd" class="copy-config btn btn-sm btn-outline-default" onclick=Copyconfig("/user/getUserAllURL?type=ssd","#win_ssd","")>全部 URL</button>
						</p>-->
                                 
                        <video width="300" height="150" controls="controls">
                        <source src="https://xhjgw.by777.icu/jiaocheng/Clashnet128.m4v" type="video/mp4" />
                        </video>
						<p>
						(1)&nbsp;SSTap[ SSR ]使用方法:&nbsp;<font color="#FF0000"></font>
						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/Windows/SSTap" target="_blank">教程文档</a>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['ssr'];?>
&extend=0">复制订阅</button>
						<button id="win_ssr" class="copy-config btn btn-sm btn-outline-default" onclick=Copyconfig("/user/getUserAllURL?type=ssr","#win_ssr","")>全部 URL</button>
						<a class="btn btn-sm btn-outline-default" href="https://jq.qq.com/?_wv=1027&k=gcBA0GQQ" target="_blank"><font color="#FF0000"><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;下载PC客户端进QQ群下载</a></font>
						</p>
						
						<p>
						(2)&nbsp;V2rayN[ VMess ]使用方法:&nbsp;<a class="btn btn-primary btn-sm mb-3" href="https://tecknity.com/"><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;Netfix奈非免费看</a>
						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/Windows/V2rayN" target="_blank">教程文档</a>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['v2ray'];?>
&extend=0">复制订阅</button>
						<button id="win_ssr" class="copy-config btn btn-sm btn-outline-default" onclick=Copyconfig("/user/getUserAllURL?type=v2ray","#general_v2ray","")>全部 URL</button>
						<a class="btn btn-sm btn-outline-default" href="https://wwi.lanzoul.com/iEpA105ehyde" target="_blank"><font color="#FF0000"><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;下载V2RAY客户端-进QQ群下载</a></font>
					    
						
						</p>
						
					<!--<p>
						(3)&nbsp;Clash[ SS/VMess ]使用方法:&nbsp;
						
						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/Windows/Clash-for-Windows" target="_blank">教程文档</a>
						<a class="btn btn-sm btn-outline-default" href="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['clash'];?>
" target="_blank">复制订阅</a>
						<a type="button" class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['clashr'];?>
">复制订阅</a>
						<a class="btn btn-sm btn-outline-default" href="clash://install-config?url=<?php echo urlencode($_smarty_tpl->tpl_vars['subInfo']->value['clash']);?>
">Clash 一键导入</a>
						<button type="button" class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['clashr'];?>
">复制订阅</button>
						</p>-->
						
					    <p>
						(3)&nbsp;ClashR[ SS/SSR/VMess ]使用方法:&nbsp;
						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/Windows/Clash-for-Windows" target="_blank">教程文档</a> 
						<a class="btn btn-sm btn-outline-default" href="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['clashr'];?>
" target="_blank">配置下载</a>
						<a class="btn btn-sm btn-outline-default" href="clash://install-config?url=<?php echo urlencode($_smarty_tpl->tpl_vars['subInfo']->value['clashr']);?>
">ClashR 一键导入</a>
						<a class="btn btn-sm btn-outline-default" href="https://jq.qq.com/?_wv=1027&k=gcBA0GQQ" target="_blank"><font color="#FF0000"><i class="fa fa-windows fa-lg mr-2"></i>&nbsp;Clash下载-所有APP进QQ群下载</a></font>
						</p>
					</div>
					<div class="tab-pane fade" id="tabs-icons-text-3" role="tabpanel" aria-labelledby="tabs-icons-text-3-tab">
				     	<video width="300" height="150" controls="controls">
                        <source src="https://xhjgw.by777.icu/jiaocheng/MAC.mp4" type="video/mp4" />
                        </video>
					    <p>
						(1)&nbsp;Surge[ SS/VMess ]使用方法:&nbsp;
						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/macOS/ShadowsocksX-NG" target="_blank">教程文档</a>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['surge4'];?>
">4.x 托管</button>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['surge3'];?>
">3.x 托管</button>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['surge_node'];?>
">3.x 节点</button>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['surge2'];?>
">2.x 托管</button>
						<a class="btn btn-sm btn-outline-default" href="https://cdn.jsdelivr.net/gh/lxiaoya1003/tuchuang@master/shadowingy-mac.mp4" target="_blank"><font color="#FF0000"><i class="fa fa-apple fa-lg mr-2"></i>&nbsp;MAC苹果电脑版在线视频教程</a></font>
						</p>
							
						<p>
						(2)&nbsp;ShadowsocksX-NG[ SSR ]:&nbsp;<font color="#FF0000">苹果电脑请点击（2）里的教程稳当，按教程下载软件使用。</font>
						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/macOS/ShadowsocksX-NG" target="_blank">教程文档</a>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value["ssr"];?>
&extend=0">订阅链接</button>
						<button id="general_ssr" class="copy-config btn btn-sm btn-outline-default" onclick=Copyconfig("/user/getUserAllURL?type=ssr","#general_ssr","")>全部 URL</button>
						<a class="btn btn-sm btn-outline-default" href="https://jq.qq.com/?_wv=1027&k=gcBA0GQQ" target="_blank"><font color="#FF0000"><i class="fa fa-apple fa-lg mr-2"></i>&nbsp;下载MAC苹果电脑版客户端-进QQ群下载</a></font>
						</p>
						
					<!--<p>
						(2)&nbsp;ClashX - [ SS/VMess ]:&nbsp;
						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/macOS/ClashX" target="_blank">教程文档</a>
						<a class="btn btn-sm btn-outline-default" href="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['clash'];?>
" target="_blank">配置下载</a>
						<a class="btn btn-sm btn-outline-default" href="clash://install-config?url=<?php echo urlencode($_smarty_tpl->tpl_vars['subInfo']->value['clash']);?>
">ClashX 一键导入</a>
						</p>-->
						
						<p>
						(3)&nbsp;ClashXR[ SS/SSR/VMess ]:&nbsp;
						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/macOS/ClashX" target="_blank">教程文档</a>
						<button type="button" class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['clashr'];?>
">复制订阅</button>
						<a class="btn btn-sm btn-outline-default" href="clash://install-config?url=<?php echo urlencode($_smarty_tpl->tpl_vars['subInfo']->value['clashr']);?>
">ClashX 一键导入</a>
						<a class="btn btn-sm btn-outline-default" href="https://qm.qq.com/cgi-bin/qm/qr?k=BOHVA7Vp4KbuwHscYeAw1LUoJPGCPK4x&jump_from=webapi" target="_blank"><font color="#FF0000"><i class="fa fa-apple fa-lg mr-2"></i>&nbsp;下载MAC苹果电脑版V2RAY客户端</a></font>
						</p>
					</div>
					<div class="tab-pane fade" id="tabs-icons-text-4" role="tabpanel" aria-labelledby="tabs-icons-text-4-tab">
					<video width="300" height="150" controls="controls">
                    <source src="https://xhjgw.by777.icu/jiaocheng/IOS1.mp4" type="video/mp4" />
                    </video>
					<?php if ($_smarty_tpl->tpl_vars['display_ios_class']->value >= 0) {?>
						<?php if ($_smarty_tpl->tpl_vars['user']->value->class >= $_smarty_tpl->tpl_vars['display_ios_class']->value) {?>
						iOS账户(仅可用于登陆Apple ID):
					
						<div class="col-sm-6 input-group" style="padding:0">
							<input type="text" class="form-control form-control-sm" name="input1"  value="<?php echo $_smarty_tpl->tpl_vars['ios_account']->value;?>
" readonly disabled />
							<div class="input-group-append">
							   <a class="copy-text btn btn-sm btn-primary text-white" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['ios_account']->value;?>
">复制</a>
						    </div>
					    </div>
					 <p> </p>
						<div class="col-sm-6 input-group" style="padding:0">
							<input type="text" class="form-control form-control-sm" name="input1" value="<?php echo $_smarty_tpl->tpl_vars['ios_password']->value;?>
" readonly disabled />
							<div class="input-group-append">
							   <a class="copy-text btn btn-sm btn-primary text-white" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['ios_password']->value;?>
">复制</a>
						    </div>
						 </div>
                   
						<?php }?>
					<?php }?>							
					<!--<p>
						(1)&nbsp;Surge - [ SS/VMess ]使用方法:&nbsp;
						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/iOS/Surge" target="_blank">教程文档</a>
						<a class="btn btn-sm btn-outline-default" href="surge3:///install-config?url=<?php echo urlencode($_smarty_tpl->tpl_vars['subInfo']->value['surge4']);?>
">4.x 一键</a>
						<a class="btn btn-sm btn-outline-default" href="surge3:///install-config?url=<?php echo urlencode($_smarty_tpl->tpl_vars['subInfo']->value['surge3']);?>
">3.x 一键</a>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['surge_node'];?>
">3.x 节点</button>
						<a class="btn btn-sm btn-outline-default" href="surge:///install-config?url=<?php echo urlencode($_smarty_tpl->tpl_vars['subInfo']->value['surge2']);?>
">2.x 一键</a>
						</p>-->
					
						<p>
						(1)&nbsp;Quantumult[ SS/SSR/VMess ]:&nbsp;
						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/iOS/Shadowrocket" target="_blank">教程文档</a>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['quantumult_v2'];?>
&extend=0">V2 订阅</button>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['ssr'];?>
&extend=0">SSR 订阅</button>
						<button id="" type="button" class="copy-config btn btn-sm btn-outline-default" onclick=Copyconfig("/link/<?php echo $_smarty_tpl->tpl_vars['ssr_sub_token']->value;?>
?quantumult=2","#quan_sub","quantumult://settings?configuration=clipboard")>混合一键订阅</button>
						<!--<button id="" type="button" class="copy-config btn btn-sm btn-outline-default" onclick=Copyconfig("/link/<?php echo $_smarty_tpl->tpl_vars['ssr_sub_token']->value;?>
?quantumult=3","#quan_conf","quantumult://settings?configuration=clipboard")>完整策略组配置</button>-->
						</p>
						
						<p>
						(2)&nbsp;Shadowrocket[ SS/SSR/VMess ]:&nbsp;<font color="#FF0000">苹果手机版本，请点击（2）里面的教程文档，按教程下载软件使用。</font>免ID版小火箭安装教程--下载爱思助手到电脑上连接苹果手机，再把下载的免ID版本小火箭安装至手机里即可
						 

						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/iOS/Shadowrocket" target="_blank">教程文档</a>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['shadowrocket'];?>
">SS(R)+V2复制订阅</button>
						<button class="btn btn-sm btn-outline-default" onclick=AddSub("<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['shadowrocket'];?>
","sub://")>SS(R)+V2一键订阅</button>
						<a class="btn btn-sm btn-outline-default" href="https://www.i4.cn/" target="_blank">下载爱思助手</a>
						<a class="btn btn-sm btn-outline-default" href="https://pan.baidu.com/s/1eogEOFzMJypzS228am1-Fg" target="_blank"><font color="#FF0000">免ID版小火箭下载提取码c89i</font></a>
						<a class="btn btn-sm btn-outline-default" href="https://wwi.lanzoui.com/isJj9wllpsh" target="_blank"><font color="#FF0000"><i class="fa fa-mobile fa-lg mr-2"></i>&nbsp;免ID版小火箭在线安装[2.1.12版本]</font></a>
						</p>
					</div>
					<div class="tab-pane fade" id="tabs-icons-text-5" role="tabpanel" aria-labelledby="tabs-icons-text-5-tab">
					<!--   <p>
						(1)&nbsp;SSD - [ SS ]使用方法:&nbsp;
                        </p>
                        <p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/Android/ShadowsocksD" target="_blank">教程文档</a>
						<button class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['ssd'];?>
">复制订阅</button>
						<button id="android_ssd" class="copy-config btn btn-sm btn-outline-default" onclick=Copyconfig("/user/getUserAllURL?type=ssd","#android_ssd","")>全部 URL</button>
						</p>-->
						<video width="300" height="150" controls="controls">
                        <source src="https://xhjgw.by777.icu/jiaocheng/android.mp4" type="video/mp4" />
                        </video>
						<p>
						(1)&nbsp;SSR[ SSR ]使用方法:&nbsp;<font color="#FF0000">安卓手机版本，请点击（1）里面的教程文档，按教程下载软件使用。</font>
                        </p>
                        <p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/Android/ShadowsocksR" target="_blank">教程文档</a>
						<button type="button" class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['ssr'];?>
&extend=0">复制订阅</button>
						<a class="btn btn-sm btn-outline-default" href="https://jq.qq.com/?_wv=1027&k=gcBA0GQQ" target="_blank"><font color="#FF0000"><i class="fa fa-android fa-lg mr-2"></i>&nbsp;下载安卓手机版客户端-进QQ群下载</font></a>
						</p>
						
						<p>
						(2)&nbsp;V2rayNG[ SS/VMess ]使用方法:&nbsp;安卓手机版本，请点击（2）里面的教程文档，按教程下载软件使用。
                        </p>
                        <p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/Android/V2rayNG" target="_blank">教程文档</a>
						<button type="button" class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['v2ray'];?>
&extend=0">复制订阅</button>
						<a class="btn btn-sm btn-outline-default" href="https://qm.qq.com/cgi-bin/qm/qr?k=BOHVA7Vp4KbuwHscYeAw1LUoJPGCPK4x&jump_from=webapi" target="_blank"><font color="#FF0000"><i class="fa fa-android fa-lg mr-2"></i>&nbsp;下载安卓手机版V2RAY客户端-进QQ群下载</font></a>
						</p>
						<p>
						(3)&nbsp;ClashRA[ SS/SSR/VMess ]使用方法:&nbsp;
						</p>
						<p>
						<a class="btn btn-sm btn-outline-default" href="/doc/#/Android/ClashRA" target="_blank">教程文档</a> 
						<a class="btn btn-sm btn-outline-default" href="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['clashr'];?>
" target="_blank">配置下载</a>
						<button type="button" class="copy-text btn btn-sm btn-outline-default" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['subInfo']->value['clashr'];?>
">复制订阅</button>
						</p>
					</div>
					
				</div>
			</div>
		</div>
		  
		</div>  
		
        <div class="col-lg-5 col-md-5">
		<?php if ($_smarty_tpl->tpl_vars['user']->value->detect_ban == 1) {?>
		   <!--detect_ban-->
		  <div class="card">
            <div class="card-header bg-transparent">
              <div class="row align-items-center">
                <div class="col">
                  <h3 class="mb-0 text-danger"><i class="fa fa-exclamation-triangle fa-lg"></i>&nbsp;账户违规</h3>
                </div>
              </div>
            </div>
			<div class="card-body">
			  <?php if ($_smarty_tpl->tpl_vars['user']->value->user_detect_ban_number() == 0) {?>
                <p class="description text-left">
					<span>您好, 您的账户因触碰了审计规则或其他原因, 目前被暂停使用. </span>
				</p>
				<p class="description text-left">
					<span><a href="/user/detect/log">查看审计记录</a></span>
				</p>
			  <?php } else { ?>	
			    <p class="description text-left">
					<span>您好, 您的账户因触碰了 <?php echo $_smarty_tpl->tpl_vars['user']->value->user_detect_ban_number();?>
 次审计规则，目前被暂停使用. </span>
				</p>
				<p class="description text-left">
					<span><a href="/user/detect/log">查看审计记录</a></span>
				</p>
			  <?php }?>	 
			    <p class="description text-left">
					<span>解除时间: <?php echo $_smarty_tpl->tpl_vars['user']->value->relieve_time();?>
</span>
				</p>
			</div>
          </div>
		<?php }?>
		<!--Account-->
          <div class="card">
            <div class="card-header bg-transparent">
              <div class="row align-items-center">
                <div class="col">
                  <h3 class="mb-0"><i class="ni ni-bulb-61 ni-lg icon-ver"></i>&nbsp;账号使用情况</h3>
                </div>
              </div>
            </div>
            <div class="card-body">
              <!-- List group -->
              <ul class="list-group list-group-flush" data-toggle="">
                <li class="checklist-entry list-group-item flex-column px-4">
                  <div id="class_notify" class="checklist-item checklist-item-success">
                    <div class="checklist-info">
                      <h5 class="checklist-title mb-0"><?php echo $_smarty_tpl->tpl_vars['level']->value->name;?>
 等级套餐到期时间</h5>
						
						    <h5 class="checklist-title mb-0"><?php echo $_smarty_tpl->tpl_vars['user']->value->class_expire;?>
</h5>
						    <small>剩余<span id="days-level-expire"> </span>天</small>
						
                    </div>
                  </div>
                </li>
				<li class="checklist-entry list-group-item flex-column px-4">
                  <div id="account_notify" class="checklist-item checklist-item-success">
                    <div class="checklist-info">
                      <h5 class="checklist-title mb-0">账户有效期</h5>
						
						    <h5 class="checklist-title mb-0"><?php echo $_smarty_tpl->tpl_vars['user']->value->expire_in;?>
</h5>
						    <small>剩余<span id="days-account-expire"> </span>天</small>
						
                    </div>
                  </div>
                </li>
				<li class="checklist-entry list-group-item flex-column px-4">
                  <div class="checklist-item checklist-item-success">
                    <div class="checklist-info">
                      <h5 class="checklist-title mb-0">上次使用时间</h5>
                        <p class="mb-0">
						  <?php if ($_smarty_tpl->tpl_vars['user']->value->lastSsTime() != "从未使用喵") {?>
						    <small><?php echo $_smarty_tpl->tpl_vars['user']->value->lastSsTime();?>
</small>
						  <?php } else { ?>
						    <small><code>从未使用</code></small>
						  <?php }?>
						</p>
                    </div>
                  </div>
                </li>
			  </ul>
            </div>
          </div>
		  <!--Traffic-->
		  <div class="card">
            <div class="card-header bg-transparent">
              <div class="row align-items-center">
                <div class="col">
                  <h3 class="mb-0"><i class="ni ni-sound-wave ni-lg icon-ver"></i>&nbsp;流量使用情况</h3>
                </div>
              </div>
            </div>
            <div class="card-body">
              <div class="chart" >
                <!-- Chart wrapper -->
                <canvas id="chart-doughnut" ></canvas>
              </div>
            </div>
			<div class="card-body">
              <div class="text-center">
                <span><a href="javascript:void(0);" class="btn btn-slack btn-sm"></a>可用<?php echo $_smarty_tpl->tpl_vars['user']->value->unusedTraffic();?>
&nbsp;</span>
				<span><a href="javascript:void(0);" class="btn btn-danger btn-sm"></a>已用<?php echo $_smarty_tpl->tpl_vars['user']->value->LastusedTraffic();?>
&nbsp;</span>
				<span><a href="javascript:void(0);" class="btn btn-warning btn-sm"></a>今日<?php echo $_smarty_tpl->tpl_vars['user']->value->TodayusedTraffic();?>
</span>
              </div>
            </div>
          </div>
		  <!--Checkin-->
		  <div class="card">
            <div class="card-header bg-transparent">
              <div class="row align-items-center">
                <div class="col">
                  <h3 class="mb-0"><i class="fa fa-calendar-check-o fa-lg"></i>&nbsp;签到得流量</h3>
                </div>
              </div>
            </div>
			<div class="card-body">
			  <?php if ($_smarty_tpl->tpl_vars['user']->value->lastSsTime() != "从未使用喵") {?>
                <p class="description text-center">
					<span>上次使用&nbsp;:&nbsp;<?php echo $_smarty_tpl->tpl_vars['user']->value->lastSsTime();?>
</span>
				</p>
			  <?php } else { ?>	
			    <p class="description text-center">
					<span>从未使用喵~></span>
				</p>
			  <?php }?>	 
			    <p id="checkin-msg" class="description text-center"></p>
				<p class="description text-center">
					<span>上次签到时间&nbsp;:&nbsp;<?php echo $_smarty_tpl->tpl_vars['user']->value->lastCheckInTime();?>
</span>
				</p>
				<p class="text-center">
				<?php if ($_smarty_tpl->tpl_vars['user']->value->isAbleToCheckin()) {?>
					<button id="checkin" type="button" class="btn btn-outline-default" >点我签到 或 使劲点</button>
			    <?php } else { ?>
					<button id="checkin-btn" type="button" class="btn btn-outline-default disabled" >您今日已签到</button>
				<?php }?>	
				</p>
			</div>
          </div>
		  <div class="card">
			<div class="card-header bg-transparent">
				<h3 class="mb-0"><i class="fa fa-link fa-lg"></i>&nbsp;邀请链接</h3>
			</div>
			<div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>剩余可邀请次数: <code><?php echo $_smarty_tpl->tpl_vars['user']->value->invite_num;?>
</code></p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>发送邀请链接给有需要的人, 邀请他人注册时, 请将以下链接发给被邀请者:</p>
			
			<p class="form-group">
				<input type="text" class="form-control form-control-alternative" name="input1"  value="<?php echo $_smarty_tpl->tpl_vars['invite_link']->value;?>
" readonly disabled />
			</p>
			<p>
				<button type="button" class="copy-text btn btn-primary" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['invite_link']->value;?>
"><i class="ni ni-ungroup ni-lg icon-ver"></i>&nbsp;点击复制</button>
		
			</p>
			</div>
		  </div>
        </div>
      </div><!--row-->
	  <?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php $_smarty_tpl->_subTemplateRender('file:user/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php echo '<script'; ?>
 src="/theme/czssr/main/js/chart.min.js"><?php echo '</script'; ?>
>

<?php if ($_smarty_tpl->tpl_vars['config']->value['notice_dialog'] == 'true') {
echo '<script'; ?>
 src="/theme/czssr/main/js/bootstrap-notify.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type = "text/jscript">
type = ['info','success','warning','danger'];
$(function(from, align) {
    color = Math.floor((Math.random() * 3) + 1);
	setTimeout(function() {
    $.notify({
        icon: "notifications",
        message: "<?php echo $_smarty_tpl->tpl_vars['config']->value['notice_massages'];?>
",

    },
    {
        type: type[color],
        time: 3000,
        placement: {
            from: "top",
            align: "center"
        }
    });
    1000});
})
<?php echo '</script'; ?>
>
<?php }?>  
<?php echo '<script'; ?>
>
function DateParse(str_date) {
		var str_date_splited = str_date.split(/[^0-9]/);
		return new Date (str_date_splited[0], str_date_splited[1] - 1, str_date_splited[2], str_date_splited[3], str_date_splited[4], str_date_splited[5]);
}
/*
 * Author: neoFelhz & CloudHammer
 * https://github.com/CloudHammer/CloudHammer/make-sspanel-v3-mod-great-again
 * License: MIT license & SATA license
 */
$(function CountDown() {
    var levelExpire = DateParse("<?php echo $_smarty_tpl->tpl_vars['user']->value->class_expire;?>
");
    var accountExpire = DateParse("<?php echo $_smarty_tpl->tpl_vars['user']->value->expire_in;?>
");
    var nowDate = new Date();
    var a = nowDate.getTime();
    var b = levelExpire - a;
    var c = accountExpire - a;
    var levelExpireDays = Math.floor(b/(24*3600*1000));
    var accountExpireDays = Math.floor(c/(24*3600*1000));
    if (levelExpireDays > ************) {
        document.getElementById('days-level-expire').innerText = "无限期";
    }else if (levelExpireDays > 7 ) {
        document.getElementById('days-level-expire').innerText = levelExpireDays;
    }else if (levelExpireDays > 0 && levelExpireDays < 7) {
        document.getElementById('days-level-expire').innerText = levelExpireDays;
		document.getElementById("class_notify").className = "checklist-item checklist-item-warning";
	}else{
	    document.getElementById("class_notify").className = "checklist-item checklist-item-danger";
     	document.getElementById('days-level-expire').innerText = "已过期 " + levelExpireDays;
		swal('您的套餐已到期', "请进入商店选购新的套餐~",'warning');
    }
    if (accountExpireDays > ************) {
        document.getElementById('days-account-expire').innerText = "无限期";
    }else if (accountExpireDays > 7) {
		document.getElementById('days-account-expire').innerText = accountExpireDays;
	}else if (accountExpireDays > 0 && accountExpireDays < 7) {
        document.getElementById('days-account-expire').innerText = accountExpireDays;
		document.getElementById("account_notify").className = "checklist-item checklist-item-warning";
	}else{
		document.getElementById("account_notify").className = "checklist-item checklist-item-danger";
        document.getElementById('days-account-expire').innerText = "已过期 " + accountExpireDays;
		swal('您的账户有效期已到期', "请进入商店选购新的套餐~",'warning');
    }
  
})
<?php echo '</script'; ?>
>
<?php echo '<script'; ?>
>
  //订阅获取
function AddSub(url,jumpurl="") {
    let tmp = window.btoa(url);
    window.location.href = jumpurl + tmp;
}

function Copyconfig(url,id,jumpurl="") {
    $.ajax({
        url: url,
        type: 'get',
        async: false,
        success: function(res) {
            if(res) {
                $("#result").modal();
                $("#msg").html("获取成功");
                $(id).data('data', res);
            } else {
                $("#result").modal();
               $("#msg").html("获取失败，请稍后再试");
           }
        }
    });
    const clipboard = new ClipboardJS('.copy-config', {
        text: function() {
            return $(id).data('data');
        }
    });
    clipboard.on('success', function(e) {
			    $("#result").modal();
			    if (jumpurl != "") {
				    $("#msg").html("复制成功，即将跳转到 APP");
				    window.setTimeout(function () {
					    window.location.href = jumpurl;
				    }, 1000);

			    } else {
				    $("#msg").html("复制成功");
			    }
		    }
    );
    clipboard.on("error",function(e){
	    console.error('Action:', e.action);
	    console.error('Trigger:', e.trigger);
	    console.error('Text:', e.text);
		}
    );
}
<?php echo '</script'; ?>
>  
<?php echo '<script'; ?>
>
//流量饼图
 //
// Charts
//
var ctx = document.getElementById("chart-doughnut").getContext('2d');
var unusedTraffic = 100 - ((Number(<?php echo $_smarty_tpl->tpl_vars['user']->value->u+$_smarty_tpl->tpl_vars['user']->value->d;?>
) / Number(<?php echo $_smarty_tpl->tpl_vars['user']->value->transfer_enable;?>
))*100).toFixed(2);
var LastusedTraffic = ((Number(<?php echo $_smarty_tpl->tpl_vars['user']->value->u+$_smarty_tpl->tpl_vars['user']->value->d;?>
) / Number(<?php echo $_smarty_tpl->tpl_vars['user']->value->transfer_enable;?>
))*100).toFixed(2);
var myChart = new Chart(ctx, {
  type: 'doughnut',

  data: {
    datasets: [{
      data: [
        unusedTraffic,
        LastusedTraffic,
      ],
      backgroundColor: [
        '#5cb85c',
        '#ddd',

      ],
      label: 'Dataset 1'
    }],
    labels: [
      '可用流量: '+unusedTraffic+' %',
      '过去已用: '+LastusedTraffic+' %',
    ],
  },
  options: {
    responsive: true,
    legend: {
      display: false,
    },
  }
});
<?php echo '</script'; ?>
>
<?php echo '<script'; ?>
>
$(".reset-link").click(function () {
	swal('重置成功', "请变更或添加您的订阅链接！～～",'success');
	window.setTimeout("location.href='/user/url_reset'", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
});

<?php echo '</script'; ?>
>
<?php if ($_smarty_tpl->tpl_vars['user']->value->unusedTraffic() <= 0) {
echo '<script'; ?>
>
window.onload = function() {
	swal('您的流量已经用完了', "请进入商店选购新的套餐~",'warning');
};
<?php echo '</script'; ?>
>
<?php }?>

   <?php }
}
