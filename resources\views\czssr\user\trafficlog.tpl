{include file='user/main.tpl'}
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Traffic log</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">流量记录</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/shop" class="btn btn-sm btn-neutral">商店购买</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">流量记录</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div id="log_chart" style="height: 300px; width: 100%;"></div>
					</div>
				</div>
			</div>
        </div>
      </div><!--row-->

	  {include file='user/footer.tpl'}
	<script src="/theme/czssr/main/js/canvasjs.min.js"></script>
	
    <script type="text/javascript">
        window.onload = function () {
            var log_chart = new CanvasJS.Chart("log_chart",
                    {
                        zoomEnabled: true,
                        title: {
                            text: "您的最近72小时流量消耗",
                            fontSize: 20

                        },
                        animationEnabled: true,
                        axisX: {
                            title: "时间",
                            labelFontSize: 14,
                            titleFontSize: 18
                        },
                        axisY: {
                            title: "流量/KB",
                            lineThickness: 2,
                            labelFontSize: 14,
                            titleFontSize: 18
                        },

                        data: [
                            {
                                type: "scatter",
                                {literal}
                                toolTipContent: "<span style='\"'color: {color};'\"'><strong>产生时间: </strong></span>{x} <br/><span style='\"'color: {color};'\"'><strong>流量: </strong></span>{y} KB <br/><span style='\"'color: {color};'\"'><strong>产生节点: </strong></span>{jd}",
                                {/literal}

                                dataPoints: [


                                    {$i=0}
                                    {foreach $logs as $single_log}
                                    {if $i==0}
                                    {literal}
                                    {
                                        {/literal}
                                        x: new Date({$single_log->log_time*1000}),
                                        y:{$single_log->totalUsedRaw()},
                                        jd: "{$single_log->node()->name}"
                                        {literal}
                                    }
                                    {/literal}
                                    {$i=1}
                                    {else}
                                    {literal}
                                    , {
                                        {/literal}
                                        x: new Date({$single_log->log_time*1000}),
                                        y:{$single_log->totalUsedRaw()},
                                        jd: "{$single_log->node()->name}"
                                        {literal}
                                    }
                                    {/literal}
                                    {/if}
                                    {/foreach}

                                ]
                            }

                        ]
                    });

            log_chart.render();
        }
	</script>