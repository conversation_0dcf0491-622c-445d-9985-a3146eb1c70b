<?php

namespace App\Controllers;

use App\Models\InviteCode;
use App\Models\Node;
use App\Models\TrafficLog;
use App\Models\Payback;
use App\Models\Coupon;
use App\Models\User;
use App\Models\Bought;
use App\Models\Ip;
use App\Utils\Tools;
use App\Services\Analytics;
use App\Services\CheckProof;

use Ozdemir\Datatables\Datatables;
use App\Utils\DatatablesHelper;

/**
 *  Admin Controller
 */
class AdminController extends UserController
{
    
	/*
	public function __construct()
	{
	    if (self::getAuthorKey() == "error") {
            header("Location:" . '/404');
	    }
	}*/
    public function checkin($request, $response, $args) 
	{
        $result = self::getAuthorKey();

	    if ($result == "error") {
        	$res['ret'] = 0;
        	$res['msg'] = "error";
        	return $response->getBody()->write(json_encode($res));
	    }
       $res['ret'] = 1;
       $res['msg'] = $result;
       return $response->getBody()->write(json_encode($res));
	}

	public function unlock_key($txt,$key='M8cTr3gS2qCGRicov77mw23ECqPi')
	{
	    $txt = urldecode($txt);
        $chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-=+";
        $ch = $txt[0];
        $nh = strpos($chars,$ch);
        $mdKey = md5($key.$ch);
        $mdKey = substr($mdKey,$nh%8, $nh%8+7);
        $txt = substr($txt,1);
        $tmp = "";
        $i=0;$j=0; $k = 0;
        for ($i=0; $i<strlen($txt); $i++) {
            $k = $k == strlen($mdKey) ? 0 : $k;
            $j = strpos($chars,$txt[$i])-$nh - ord($mdKey[$k++]);
            while ($j<0) $j+=64;
            $tmp .= $chars[$j];
        }
        return trim(base64_decode($tmp),$key);
    }

	public function getAuthorKey()
    {
	    $author_key_file = BASE_PATH . '/privkey.json';
	    $infom = file_get_contents($author_key_file);
	    if ($infom) {
            $result = self::unlock_key($infom);
	        $info = json_decode($result,true);
            $domain = trim($_SERVER['HTTP_HOST']);
            $md5 = $info['authorKey'];
            $server_name = explode('|', $info['domain']);
			$status = $info['status'];
            $ex_time = $info['endTime'];
           if (in_array($domain, $server_name) && $status == 1 && $ex_time >= time()){
			    return $md5;
            }else{
               return 'error';
            }
	    } else {
	        return 'error';
	    }
	}
    public function index($request, $response, $args)
    {
        $table_config['total_column'] = array("id" => "ID",
                            "email" => "账号", "transfer"=>"剩余流量/GB", "todayTraffic" => "今日流量/GB", "allTraffic" => "累计流量/GB", "transfer_enable"=>"总流量/GB");
        $table_config['default_show_column'] = array("id", "email", "transfer", "todayTraffic", "allTraffic", "transfer_enable");
												
        $table_config['ajax_url'] = 'traffic_used/ajax';
        $sts = new Analytics();

		$sale_money = Bought::select('price')->get();
		$sale_money_num = 0;
		foreach($sale_money as $val){
			$sale_money_num+= $val->price;
		}
		$nodes_all = Node::select('id')->where('sort',0)->orwhere('sort',11)->get();
		$nodes_all_num = count($nodes_all);
		
        return $this->view()->assign(array('table_config'=>$table_config, 'sts' => $sts, 'sale_money_num' => $sale_money_num, 'nodes_all_num'=>$nodes_all_num))->display('admin/index.tpl');
    }

    public function node($request, $response, $args)
    {
        $nodes = Node::all();
        return $this->view()->assign('nodes', $nodes)->display('admin/node.tpl');
    }

    public function invite($request, $response, $args)
    {
        $table_config['total_column'] = array("op"=>"操作", "id" => "ID",
                        "total" => "原始金额", "userid" => "发起用户ID",
                        "event_user_name" => "发起用户名", "ref_by" => "获利用户ID", 
                        "ref_user_name" => "获利用户名", "ref_get" => "获利金额","ref_traffic" => "获得流量GB",
                        "datetime" => "时间");
        $table_config['default_show_column'] = array();
        foreach ($table_config['total_column'] as $column => $value) {
            array_push($table_config['default_show_column'], $column);
        }
        $table_config['ajax_url'] = 'payback/ajax';
        return $this->view()->assign('table_config', $table_config)->display('admin/invite.tpl');
    }

    public function ajax_payback($request, $response, $args)
    {
        $datatables = new Datatables(new DatatablesHelper());
        $datatables->query('Select id as op, payback.id, payback.total, payback.userid, payback.ref_by, payback.ref_traffic, payback.ref_get, payback.datetime from payback');
		
        $datatables->edit('op', function ($data) {
            return '<button class="btn btn-secondary btn-sm" onClick="del(\''.$data["id"].'\')">删除</button>';
        });
        $datatables->edit('total', function ($data) {
        	return round($data['total'],2);
        });
		$datatables->edit('event_user_name', function ($data) {
			$event_user_name = User::where("id",$data['userid'])->first();
			if ($event_user_name == null){
				$event_user_name = "<span class='text-danger'>无此 ID</span>";
			}else{
				$event_user_name = $event_user_name->user_name;
			}
            return $event_user_name;
        });
        $datatables->edit('ref_by', function ($data) {
			$ref_user_id = User::where("id",$data['ref_by'])->first();
			if ($ref_user_id == null){
				$ref_user_id = "<span class='text-danger'>无此 ID</span>";
			}else{
				$ref_user_id = $ref_user_id->id;
			}
            return $ref_user_id;
        });
		$datatables->edit('ref_user_name', function ($data) {
			$ref_user_name = User::where("id",$data['ref_by'])->first();
			if($ref_user_name == null){
				$ref_user_name = "<span class='text-danger'>无此 ID</span>";
			}else{
				$ref_user_name = $ref_user_name->user_name;
			}
            return $ref_user_name;
        });
        $datatables->edit('datetime', function ($data) {
            return date('Y-m-d H:i:s', $data['datetime']);
        });

        $body = $response->getBody();
        $body->write($datatables->generate());
    }
	public function invitedelete($request, $response, $args)
    {
        $id = $request->getParam('deleteid');
		$Payback = Payback::find($id);
		
        if (!$Payback->delete()) {
            $rs['ret'] = 0;
            $rs['msg'] = "删除失败";
            return $response->getBody()->write(json_encode($rs));
        }
        $rs['ret'] = 1;
        $rs['msg'] = "该记录已经从我们的系统中删除";
        return $response->getBody()->write(json_encode($rs));
	
    }
    public function addInvite($request, $response, $args)
    {
        $num = $request->getParam('num');
        $prefix = $request->getParam('prefix');

		if(Tools::isInt($num)==false){
		    $res['ret'] = 0;
            $res['msg'] = "非法请求";
            return $response->getBody()->write(json_encode($res));
		}

        if ($request->getParam('uid')!="0") {
            if (strpos($request->getParam('uid'), "@")!=false) {
                $user=User::where("email", "=", $request->getParam('uid'))->first();
            } else {
                $user=User::Where("id", "=", $request->getParam('uid'))->first();
            }

            if ($user==null) {
                $res['ret'] = 0;
                $res['msg'] = "邀请次数添加失败，检查用户id或者用户邮箱是否输入正确";
                return $response->getBody()->write(json_encode($res));
            }
            $uid = $user->id;
        } else {
            $uid=0;
        }
		$user->invite_num += $num;
		$user->save();
        $res['ret'] = 1;
        $res['msg'] = "邀请次数添加成功";
        return $response->getBody()->write(json_encode($res));
    }

    public function coupon($request, $response, $args)
    {
        $table_config['total_column'] = array("op" => "操作", "id" => "ID", "code" => "优惠码",
                          "expire" => "过期时间", "shop" => "限定商品ID",
                          "credit" => "额度", "onetime" => "次数");
        $table_config['default_show_column'] = array();
        foreach ($table_config['total_column'] as $column => $value) {
            array_push($table_config['default_show_column'], $column);
        }
        $table_config['ajax_url'] = 'coupon/ajax';
        return $this->view()->assign('table_config', $table_config)->display('admin/coupon.tpl');
    }

    public function addCoupon($request, $response, $args)
    {
        $code = new Coupon();
        $code->onetime = $request->getParam('onetime');
        $generate_type = $request->getParam('generate_type');
        $final_code = $request->getParam('prefix');

        if ($final_code == null && ($generate_type == 1 || $generate_type == 3)) {
            $res['ret'] = 0;
            $res['msg'] = '优惠码不能为空';
            return $response->getBody()->write(json_encode($res));
        }

        if ($generate_type == 1) {
            if (Coupon::where('code', $final_code)->count() != 0) {
                $res['ret'] = 0;
                $res['msg'] = '优惠码已存在';
                return $response->getBody()->write(json_encode($res));
            }
        } else {
            while (true) {
                if ($generate_type == 2) {
                    $temp_code = Tools::genRandomChar(8);
                } elseif ($generate_type == 3) {
                    $temp_code = $final_code . Tools::genRandomChar(8);
                }

                if (Coupon::where('code', $temp_code)->count() == 0) {
                    $final_code = $temp_code;
                    break;
                }
            }
        }

        $code->code = $final_code;
        $code->expire = time() + $request->getParam('expire') * 3600;
        $code->shop = $request->getParam('shop');
        $code->credit = $request->getParam('credit');

        $code->save();

        $res['ret'] = 1;
        $res['msg'] = '优惠码添加成功';
        return $response->getBody()->write(json_encode($res));
    }
	public function deleteCoupon($request, $response, $args)
    {
        $id = $request->getParam('id');
        $code = Coupon::find($id);
        if (!$code->delete()) {
            $rs['ret'] = 0;
            $rs['msg'] = "删除失败";
            return $response->getBody()->write(json_encode($rs));
        }
        $rs['ret'] = 1;
        $rs['msg'] = "删除成功";
        return $response->getBody()->write(json_encode($rs));
    }
    public function trafficLog($request, $response, $args)
    {
        $table_config['total_column'] = array("id" => "ID", "user_id" => "用户ID",
                          "user_name" => "用户名", "node_name" => "使用节点",
                          "rate" => "倍率", "origin_traffic" => "实际使用流量",
                          "traffic" => "结算流量",
                          "log_time" => "记录时间");
        $table_config['default_show_column'] = array("id", "user_id",
                                  "user_name", "node_name",
                                  "rate", "origin_traffic",
                                  "traffic", "log_time");
        $table_config['ajax_url'] = 'trafficlog/ajax';
        return $this->view()->assign('table_config', $table_config)->display('admin/trafficlog.tpl');
    }

    public function ajax_trafficLog($request, $response, $args)
    {
        $datatables = new Datatables(new DatatablesHelper());
        $datatables->query('Select log.id,log.user_id,user.user_name,node.name as node_name,log.rate,(log.u + log.d) as origin_traffic,log.traffic,log.log_time from user_traffic_log as log,user,ss_node as node WHERE log.user_id = user.id AND log.node_id = node.id');

        $datatables->edit('log_time', function ($data) {
            return date('Y-m-d H:i:s', $data['log_time']);
        });

        $datatables->edit('origin_traffic', function ($data) {
            return Tools::flowAutoShow($data['origin_traffic']);
        });

        $body = $response->getBody();
        $body->write($datatables->generate());
    }

    public function ajax_coupon($request, $response, $args)
    {
        $datatables = new Datatables(new DatatablesHelper());
        $datatables->query('Select id,code,expire,shop,credit,onetime from coupon');
		$datatables->edit('op', function ($data) {
            return '<a class="btn btn-primary btn-sm" id="delete" value="'.$data['id'].'" href="javascript:void(0);" onClick="delete_modal_show(\''.$data['id'].'\')">删除</a>';
        });
        $datatables->edit('expire', function ($data) {
            return date('Y-m-d H:i:s', $data['expire']);
        });

        $body = $response->getBody();
        $body->write($datatables->generate());
    }

    public function TrafficUsedTop($request, $response, $args) 
    {
        $datatables = new Datatables(new DatatablesHelper());
        $datatables->query('Select user.id, user.email, user.u, user.d, user.transfer_enable, user.last_day_t from user order by last_day_t desc limit 10');
      
        $datatables->edit('DT_RowId', function ($data) {
            return 'row_1_'.$data['id'];
        });
        
        $datatables->edit('transfer', function ($data) {
            $transfe = ($data['transfer_enable'] - ($data['u'] + $data['d']));
            $transfer = round(Tools::flowToGB($transfe),2);
            if ($transfer <= 0){
               return '<span class="text-danger">'.$transfer.'</span>';
            }else{
               return $transfer;
            }
        });
        $datatables->edit('todayTraffic', function ($data) {
            $todayTraffic = round(Tools::flowToGB($data['u'] + $data['d'] - $data['last_day_t']),2);
            if ($todayTraffic >= 50){
               return '<span class="text-danger">'.$todayTraffic.'</span>';
            }else{
               return $todayTraffic;
            }
        });
        $datatables->edit('allTraffic', function ($data) {
            $allTraffic = round(Tools::flowToGB($data['u'] + $data['d']),2);
            $zong = round(Tools::flowToGB($data['transfer_enable']),2);
            if ($allTraffic >= $zong){
               return '<span class="text-danger">'.$allTraffic.'</span>';
            }else{
               return $allTraffic;
            }
        });
        $datatables->edit('transfer_enable', function ($data) {
            $transfer_enable = round(Tools::flowToGB($data['transfer_enable']),2);
            if ($transfer_enable <= 0){
               return '<span class="text-danger">'.$transfer_enable.'</span>';
            }else{
               return $transfer_enable;
            }
        });
       $body = $response->getBody();
       $body->write($datatables->generate());
      
    }
    
}
