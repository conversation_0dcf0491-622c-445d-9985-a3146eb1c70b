<?php
/* Smarty version 3.1.33, created on 2022-07-17 17:49:28
  from '/www/wwwroot/www.shadowingy.com/resources/views/czssr/header.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62d3db28392b68_96625913',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '748c5fb43fe5de0a95bc87a740c738f1582b3165' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.com/resources/views/czssr/header.tpl',
      1 => 1586569024,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_62d3db28392b68_96625913 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE html>
<?php if ($_smarty_tpl->tpl_vars['config']->value['CloseSite'] == 'true') {
echo '<script'; ?>
>window.location.href='<?php echo $_smarty_tpl->tpl_vars['config']->value["baseUrl"];?>
/404';<?php echo '</script'; ?>
>
<?php }?>
<html>
<head>
  <meta charset="UTF-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
  <meta name="author" content="Creative Tim">
  <title><?php echo $_smarty_tpl->tpl_vars['config']->value["appName"];?>
</title>
  <meta name="keywords" content="<?php echo $_smarty_tpl->tpl_vars['config']->value['keywords'];?>
">
  <meta name="description" content="<?php echo $_smarty_tpl->tpl_vars['config']->value['description'];?>
">
  <meta name="author" content="hencework"/>
  <!-- Favicon -->
  <link href="/favicon.png" rel="icon" type="image/png">
  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet">
  <link href="/theme/czssr/main/css/font-awesome/css/font-awesome.min.css" rel="stylesheet">
  <!-- Icons -->
  <link rel="stylesheet" href="/theme/czssr/main/css/nucleo.css" type="text/css">
  <link rel="stylesheet" href="/theme/czssr/main/css/sweetalert2.min.css" type="text/css">
  <link type="text/css" href="/theme/czssr/main/css/czssr-index.css?v=1.1.0" rel="stylesheet">
  
<style>

.goog-te-banner-frame{
	display:none
}
  .engname1{
   color: #d1cde5;
   font-size:1.5rem;
   font-weight: bold;
   vertical-align: middle;
   text-transform: capitalize;
  }
  .engname2{
    font-size: 3rem;
    font-weight: bold;
    vertical-align: middle;
    text-transform: capitalize;
  }
  .engname3{
    font-size: 1rem;
    font-weight: bold;
    vertical-align: middle;
    text-transform: capitalize;
  }
</style>
</head>
<?php }
}
