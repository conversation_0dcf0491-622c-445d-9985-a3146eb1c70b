<?php
/* Smarty version 3.1.33, created on 2022-07-17 23:02:37
  from '/www/wwwroot/www.shadowingy.com/resources/views/czssr/user/ticket/ticket.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62d4248d8c2c49_88377016',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '4e4d0c67730811942a711a50564be71cb3f7d944' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.com/resources/views/czssr/user/ticket/ticket.tpl',
      1 => 1588140988,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:user/main.tpl' => 1,
    'file:user/footer.tpl' => 1,
  ),
),false)) {
function content_62d4248d8c2c49_88377016 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:user/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Tickets</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">工单系统</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/shop" class="btn btn-sm btn-neutral">商店购买</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">工单说明</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
					<a class="btn btn-primary btn-sm mb-3" href="/user/ticket/create">新建工单</a>
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>对于使用教程的问题请查看帮助手册<a href="/doc">点此直达</a>.</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>我们推荐客户自己查看帮助手册解决问题, 实在解决不了的再提交工单.</p>
							<?php if ($_smarty_tpl->tpl_vars['config']->value["pay_ticket_price"] > 0) {?>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>目前每次新建工单需支付 <code><?php echo $_smarty_tpl->tpl_vars['config']->value["pay_ticket_price"];?>
</code> 元.</p>
							<?php }?>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>关于节点线路\合作\技术支持\代理商\大客户\上游渠道商\广告资源等可在此联系.</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
			
            <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h4 class="mb-0">查看工单</h4>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive">
				  <?php echo $_smarty_tpl->tpl_vars['tickets']->value->render();?>

					<table class="table align-items-center table-flush">
					<thead class="thead-light">
						<tr>
							<th>ID</th>
                            <th>发起日期</th>
                            <th>工单标题</th>
                            <th>工单状态</th>
                            <th>操作</th>
						</tr>
					</thead>
					<tbody class="list">
				    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['tickets']->value, 'ticket');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['ticket']->value) {
?>
					<tr>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>#<?php echo $_smarty_tpl->tpl_vars['ticket']->value->id;?>
</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i><?php echo $_smarty_tpl->tpl_vars['ticket']->value->datetime();?>
</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i><?php echo $_smarty_tpl->tpl_vars['ticket']->value->title;?>
</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><?php if ($_smarty_tpl->tpl_vars['ticket']->value->status == 1) {?><i class="bg-success"></i>工单服务中<?php } else { ?><i class="bg-warning"></i>工单已结束<?php }?></span>
						</td>
						<td>
							<a class="btn btn-primary btn-sm" href="/user/ticket/<?php echo $_smarty_tpl->tpl_vars['ticket']->value->id;?>
/view">查看</a>
						</td>
					</tr>
					<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
					</tbody>
					</table>
				   
				</div>
              </div>
            </div><!--card-->
		
        </div>
      </div><!--row-->
	  

	  <?php $_smarty_tpl->_subTemplateRender('file:user/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php }
}
