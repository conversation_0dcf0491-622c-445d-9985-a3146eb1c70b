<?php
/* Smarty version 3.1.33, created on 2022-04-07 15:52:02
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/announcement/edit.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_624e9822bf0b32_57816737',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '5e051fa45faa6c86841af116fdcd1588e20757ca' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/announcement/edit.tpl',
      1 => 1576053404,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/main.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:admin/footer.tpl' => 1,
  ),
),false)) {
function content_624e9822bf0b32_57816737 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<link href="/theme/czssr/main/css/quill.snow.css" rel="stylesheet">
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Create Ann</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/announcement">公告系统</a></li>
                  <li class="breadcrumb-item active" aria-current="page">编辑公告 #<?php echo $_smarty_tpl->tpl_vars['ann']->value->id;?>
</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/ticket" class="btn btn-sm btn-neutral">工单</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">编辑公告</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						
						<div class="form-group mt-3">
							<label class="form-control-label">内容(点击上传图片后会自动上传比较慢等缓存,别重复点)&nbsp;:&nbsp;</label>
    						<div id="editor">
								<div v-html="content"><?php echo $_smarty_tpl->tpl_vars['ann']->value->content;?>
</div>
							</div>
						</div>
						
                      <div class="modal-footer">
						<button id="submit" type="button" class="btn btn-primary">确认修改</button>
					  </div>
					</div>
					
				</div>
			</div>
		
        </div>
      </div><!--row-->
	  
	<?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	<?php $_smarty_tpl->_subTemplateRender('file:admin/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php echo '<script'; ?>
 src="/theme/czssr/main/js/quill.min.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
>
var toolbarOptions = [
  ['bold', 'italic', 'underline', 'strike', 'blockquote', 'code-block', 'link', 'image', 'video'],        // 切换按钮
  [{ 'header': 1 }, { 'header': 2 }, { 'list': 'ordered'}, { 'list': 'bullet' }, { 'align': [] }],               // 用户自定义按钮值
  [{ 'script': 'sub'}, { 'script': 'super' }, { 'indent': '-1'}, { 'indent': '+1' }],      // 上标/下标// 减少缩进/缩进

  [/*{ 'size': ['small', 'large', 'huge']}*/ { 'header': [1, 2, 3, 4, 5, 6, false] }, { 'font': [] }],  //颜色//背景// 用户自定义下拉//大小//字体//
  [{ 'color': [] }, { 'background': [] }],
  ['clean']                                         // 清除格式
];

var quill = new Quill('#editor', {
  modules: {
    toolbar: toolbarOptions
  },
  theme: 'snow'
});

<?php echo '</script'; ?>
>	  
<?php echo '<script'; ?>
>
    $(document).ready(function () {
        function submit() {
		
            $("#result").modal();
            $('#msg').html('正在提交...');
			
			var quill_html = document.querySelector('#editor').children[0].innerHTML;
            var quill_markdown = document.querySelector('#editor').children[0].innerText;
			//html = '<div class="ql-container ql-snow"><div class="ql-editor">'+html+"</div></div>";
			
            $.ajax({
                type: "PUT",
                url: "/admin/announcement/<?php echo $_smarty_tpl->tpl_vars['ann']->value->id;?>
",
                dataType: "json",
                data: {
                    content: quill_html,
                    markdown: quill_markdown
                },
                success: function(data) {
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: function(jqXHR) {
                    $("#result").modal();
                    $('#msg').html("发生错误："+jqXHR.status);
                }
            });
        }

        $("#submit").click(function () {
            submit();
        });
    })

<?php echo '</script'; ?>
><?php }
}
