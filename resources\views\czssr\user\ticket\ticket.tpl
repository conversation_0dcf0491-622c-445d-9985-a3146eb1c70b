{include file='user/main.tpl'}
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Tickets</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">工单系统</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/shop" class="btn btn-sm btn-neutral">商店购买</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">工单说明</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
					<a class="btn btn-primary btn-sm mb-3" href="/user/ticket/create">新建工单</a>
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>对于使用教程的问题请查看帮助手册<a href="/doc">点此直达</a>.</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>我们推荐客户自己查看帮助手册解决问题, 实在解决不了的再提交工单.</p>
							{if $config["pay_ticket_price"] > 0}
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>目前每次新建工单需支付 <code>{$config["pay_ticket_price"]}</code> 元.</p>
							{/if}
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>关于节点线路\合作\技术支持\代理商\大客户\上游渠道商\广告资源等可在此联系.</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
			
            <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h4 class="mb-0">查看工单</h4>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive">
				  {$tickets->render()}
					<table class="table align-items-center table-flush">
					<thead class="thead-light">
						<tr>
							<th>ID</th>
                            <th>发起日期</th>
                            <th>工单标题</th>
                            <th>工单状态</th>
                            <th>操作</th>
						</tr>
					</thead>
					<tbody class="list">
				    {foreach $tickets as $ticket}
					<tr>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>#{$ticket->id}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$ticket->datetime()}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$ticket->title}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4">{if $ticket->status==1}<i class="bg-success"></i>工单服务中{else}<i class="bg-warning"></i>工单已结束{/if}</span>
						</td>
						<td>
							<a class="btn btn-primary btn-sm" href="/user/ticket/{$ticket->id}/view">查看</a>
						</td>
					</tr>
					{/foreach}
					</tbody>
					</table>
				   
				</div>
              </div>
            </div><!--card-->
		
        </div>
      </div><!--row-->
	  

	  {include file='user/footer.tpl'}
	  