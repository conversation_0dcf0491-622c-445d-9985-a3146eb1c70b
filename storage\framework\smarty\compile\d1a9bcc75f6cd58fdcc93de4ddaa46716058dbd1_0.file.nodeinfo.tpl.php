<?php
/* Smarty version 3.1.33, created on 2022-02-04 14:39:11
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/nodeinfo.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_61fcca0f05e2b2_74144545',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'd1a9bcc75f6cd58fdcc93de4ddaa46716058dbd1' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/nodeinfo.tpl',
      1 => 1586078014,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_61fcca0f05e2b2_74144545 (Smarty_Internal_Template $_smarty_tpl) {
?>
<html>
<head>
  <meta charset="UTF-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
  <meta name="author" content="Creative Tim">
  <title><?php echo $_smarty_tpl->tpl_vars['config']->value["appName"];?>
</title>
  <!-- Favicon -->
  <link href="/favicon.png" rel="icon" type="image/png">
  <!-- Fonts -->
  <link href="/theme/czssr/main/css/datatables.bootstrap4.min.css" rel="stylesheet">
  <!-- Icons -->
  <link rel="stylesheet" href="/theme/czssr/main/css/nucleo.css" type="text/css">
  <link href="/theme/czssr/main/css/font-awesome/css/font-awesome.min.css" rel="stylesheet">
  <link type="text/css" href="/theme/czssr/main/css/czssr.css?v=1.3.7" rel="stylesheet">
  <link rel="stylesheet" href="/theme/czssr/main/css/sweetalert2.min.css" type="text/css">
  
<style>

.goog-te-banner-frame{
	display:none
}
.icon-ver {
    vertical-align:middle;
}
  .col-xxxs {
  float:none;
  width: 17%;
}

</style>
</head>

<body>

<?php $_smarty_tpl->_assignInScope('ssr_prefer', App\Utils\URL::SSRCanConnect($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['mu']->value));?>

   <!-- Main content -->
  <div class="main-content" id="panel" style="overflow-y: auto;">
    <div class="container-fluid">
      <!-- Table -->
      <div class="row">
        <div class="col-lg-12 col-sm-12">
		<!-- Custom form validation -->
            <div class="card mt-3">
              <!-- Card header -->
              <div class="card-header" style="padding:.8rem 1.2rem">
                <h5 class="mb-0">配置信息<?php echo $_smarty_tpl->tpl_vars['mu']->value;?>
</h5>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <div class="nav-wrapper">
					<ul class="nav nav-pills nav-fill" id="ssrtype" role="tablist">
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0 active" id="ssrtype-1-tab" data-toggle="tab" href="#ssrtype-1" role="tab" aria-controls="ssrtype-1" aria-selected="true"><i class="fa fa-plane fa-lg icon-ver"></i> ShadowsocksR</a>
						</li>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="ssrtype-2-tab" data-toggle="tab" href="#ssrtype-2" role="tab" aria-controls="ssrtype-2" aria-selected="false"><i class="fa  fa-fighter-jet fa-lg icon-ver"></i> Shadowsocks</a>
						</li>
					</ul>
				</div>
				<div class="tab-content" id="myTabContent1">
					<div class="tab-pane fade show active" id="ssrtype-1" role="tabpanel" aria-labelledby="ssrtype-1-tab">
						
						<?php if (App\Utils\URL::SSRCanConnect($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['mu']->value)) {?>
                            <?php $_smarty_tpl->_assignInScope('ssr_item', App\Utils\URL::getItem($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['node']->value,$_smarty_tpl->tpl_vars['mu']->value,$_smarty_tpl->tpl_vars['relay_rule_id']->value,0));?>
                            <?php if ($_smarty_tpl->tpl_vars['ssr_item']->value['obfs'] == "v2ray") {?>
                                <p>您好，Shadowsocks V2Ray-Plugin 节点需要您的加密方式使用 AEAD 系列。请您到 资料编辑
                                    页面修改后再来查看此处。</p>
                            <?php } else { ?>
                                <p>服务器地址：<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['address'];?>
<br>
                                    服务器端口：<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['port'];?>
<br>
                                    加密方式：<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['method'];?>
<br>
                                    密码：<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['passwd'];?>
<br>
                                    协议：<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['protocol'];?>
<br>
                                    协议参数：<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['protocol_param'];?>
<br>
                                    混淆：<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['obfs'];?>
<br>
                                    混淆参数：<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['obfs_param'];?>
<br></p>
                            <?php }?>
                        <?php } else { ?>
                            <p>您好，您目前的 加密方式，混淆，或者协议设置在 ShadowsocksR 客户端下无法连接。请您选用 Shadowsocks
                                客户端来连接，或者到 资料编辑 页面修改后再来查看此处。</p>
                            <p>同时, ShadowsocksR 单端口多用户的连接不受您设置的影响,您可以在此使用相应的客户端进行连接~</p>
                        <?php }?>
					</div> 
					<div class="tab-pane fade" id="ssrtype-2" role="tabpanel" aria-labelledby="ssrtype-2-tab">
						
						<?php if (App\Utils\URL::SSCanConnect($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['mu']->value)) {?>
                            <?php $_smarty_tpl->_assignInScope('ss_item', App\Utils\URL::getItem($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['node']->value,$_smarty_tpl->tpl_vars['mu']->value,$_smarty_tpl->tpl_vars['relay_rule_id']->value,1));?>
                            <?php if ($_smarty_tpl->tpl_vars['ss_item']->value['obfs'] == "v2ray" && App\Utils\URL::CanMethodConnect($_smarty_tpl->tpl_vars['user']->value->method) != 2) {?>
                                <p>您好，Shadowsocks V2Ray-Plugin 节点需要您的加密方式使用 AEAD 系列。请您到 资料编辑
                                    页面修改后再来查看此处。</p>
                            <?php } else { ?>
                                <p>服务器地址：<?php echo $_smarty_tpl->tpl_vars['ss_item']->value['address'];?>
<br>
                                    服务器端口：<?php echo $_smarty_tpl->tpl_vars['ss_item']->value['port'];?>
<br>
                                    加密方式：<?php echo $_smarty_tpl->tpl_vars['ss_item']->value['method'];?>
<br>
                                    密码：<?php echo $_smarty_tpl->tpl_vars['ss_item']->value['passwd'];?>
<br>
                                    混淆：<?php echo $_smarty_tpl->tpl_vars['ss_item']->value['obfs'];?>
<br>
                                    混淆参数：<?php echo $_smarty_tpl->tpl_vars['ss_item']->value['obfs_param'];?>
<br></p>
                            <?php }?>
                        <?php } else { ?>
                            <p>您好，您目前的 加密方式，混淆，或者协议设置在 Shadowsocks 客户端下无法连接。请您选用 ShadowsocksR
                                客户端来连接，或者到 资料编辑 页面修改后再来查看此处。</p>
                        <?php }?>
					</div>
					
				</div>
              </div>
            </div><!--card-->
		</div>	
		<div class="col-lg-12 col-sm-12">
			<div class="card">
              <!-- Card header -->
              <div class="card-header" style="padding:.8rem 1.2rem">
                <h5 class="mb-0">配置Json</h5>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <div class="nav-wrapper">
					<ul class="nav nav-pills nav-fill" id="ssrtypejson" role="tablist">
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0 active" id="ssrtypejson-3-tab" data-toggle="tab" href="#ssrtypejson-3" role="tab" aria-controls="ssrtypejson-3" aria-selected="true"><i class="fa fa-plane fa-lg icon-ver"></i> ShadowsocksR</a>
						</li>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="ssrtypejson-4-tab" data-toggle="tab" href="#ssrtypejson-4" role="tab" aria-controls="ssrtypejson-4" aria-selected="false"><i class="fa  fa-fighter-jet fa-lg icon-ver"></i> Shadowsocks</a>
						</li>
					</ul>
				</div>
				<div class="tab-content" id="myTabContent2">
					<div class="tab-pane fade show active" id="ssrtypejson-3" role="tabpanel" aria-labelledby="ssrtypejson-3-tab">
						 <div class="text-left">
                        <?php if (App\Utils\URL::SSRCanConnect($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['mu']->value)) {?>
                                            <?php if ($_smarty_tpl->tpl_vars['ssr_item']->value['obfs'] == "v2ray") {?>
                                                <p>您好，Shadowsocks V2Ray-Plugin 节点需要您的加密方式使用 AEAD 系列。请您到 资料编辑
                                                    页面修改后再来查看此处。</p>
                                            <?php } else { ?>
                                <div class="bg-secondary text-center" style="word-break:break-all">
									<blockquote class="blockquote">
										<p>
{
    "server": "<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['address'];?>
",
    "local_address": "127.0.0.1",
    "local_port": 1080,
    "timeout": 300,
    "workers": 1,
    "server_port": <?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['port'];?>
,
    "password": "<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['passwd'];?>
",
    "method": "<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['method'];?>
",
    "obfs": "<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['obfs'];?>
",
    "obfs_param": "<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['obfs_param'];?>
",
    "protocol": "<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['protocol'];?>
",
    "protocol_param": "<?php echo $_smarty_tpl->tpl_vars['ssr_item']->value['protocol_param'];?>
"
}
                                      
                                        </p>
									</blockquote>
								</div>
                                            <?php }?>
                        <?php } else { ?>
                            <p>您好，您目前的 加密方式，混淆，或者协议设置在 ShadowsocksR 客户端下无法连接。请您选用 Shadowsocks 客户端来连接，或者到
                                资料编辑 页面修改后再来查看此处。</p>
                        <?php }?>
                       </div>
					</div> 
					<div class="tab-pane fade" id="ssrtypejson-4" role="tabpanel" aria-labelledby="ssrtypejson-4-tab">
						 <div class="text-left">
                        <?php if (App\Utils\URL::SSCanConnect($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['mu']->value)) {?>
                                            <?php if ($_smarty_tpl->tpl_vars['ss_item']->value['obfs'] == "v2ray" && App\Utils\URL::CanMethodConnect($_smarty_tpl->tpl_vars['user']->value->method) != 2) {?>
                                                <p>您好，Shadowsocks V2Ray-Plugin 节点需要您的加密方式使用 AEAD 系列。请您到 资料编辑
                                                    页面修改后再来查看此处。</p>
                                            <?php } else { ?>
                                <div class="bg-secondary text-center" style="word-break:break-all">
									<blockquote class="blockquote">
										<p>
{
        "server": "<?php echo $_smarty_tpl->tpl_vars['ss_item']->value['address'];?>
",
        "local_address": "127.0.0.1",
        "local_port": 1080,
        "timeout": 300,
        "workers": 1,
        "server_port": <?php echo $_smarty_tpl->tpl_vars['ss_item']->value['port'];?>
,
        "password": "<?php echo $_smarty_tpl->tpl_vars['ss_item']->value['passwd'];?>
",
        "method": "<?php echo $_smarty_tpl->tpl_vars['ss_item']->value['method'];?>
",
        "plugin": "<?php echo App\Utils\URL::getJsonObfs($_smarty_tpl->tpl_vars['ss_item']->value);?>
"
}
                                      
                                        </p>
									</blockquote>
								</div>


                                            <?php }?>
                        <?php } else { ?>
                            <p>您好，您目前的 加密方式，混淆，或者协议设置在 Shadowsocks 客户端下无法连接。请您选用 ShadowsocksR 客户端来连接，或者到
                                资料编辑 页面修改后再来查看此处。</p>
                        <?php }?>
                      </div>
					</div>
				</div>
              </div>
            </div><!--card-->
		</div>
		<div class="col-lg-12 col-sm-12">
			<div class="card">
              <!-- Card header -->
              <div class="card-header" style="padding:.8rem 1.2rem">
                <h5 class="mb-0">配置链接</h5>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <div class="nav-wrapper">
					<ul class="nav nav-pills nav-fill" id="ssrtypelink" role="tablist">
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0 active" id="ssrtypelink-5-tab" data-toggle="tab" href="#ssrtypelink-5" role="tab" aria-controls="ssrtypelink-5" aria-selected="true"><i class="fa fa-plane fa-lg icon-ver"></i> ShadowsocksR</a>
						</li>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="ssrtypelink-6-tab" data-toggle="tab" href="#ssrtypelink-6" role="tab" aria-controls="ssrtypelink-6" aria-selected="false"><i class="fa  fa-fighter-jet fa-lg icon-ver"></i> Shadowsocks</a>
						</li>
					</ul>
				</div>
				<div class="tab-content" id="myTabContent3">
					<div class="tab-pane fade show active" id="ssrtypelink-5" role="tabpanel" aria-labelledby="ssrtypelink-5-tab">
						
						<?php if (App\Utils\URL::SSRCanConnect($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['mu']->value)) {?>
                            <?php if ($_smarty_tpl->tpl_vars['ssr_item']->value['obfs'] == "v2ray") {?>
                                <p>您好，Shadowsocks V2Ray-Plugin 节点需要您的加密方式使用 AEAD 系列。请您到 资料编辑
                                    页面修改后再来查看此处。</p>
                            <?php } else { ?>
                                <p><a class="copy-text" href="javascript:void(0);" data-clipboard-text="<?php echo App\Utils\URL::getItemUrl($_smarty_tpl->tpl_vars['ssr_item']->value,0);?>
">点我复制配置链接</a>
                                </p>
                                <p><a href="<?php echo App\Utils\URL::getItemUrl($_smarty_tpl->tpl_vars['ssr_item']->value,0);?>
">iOS 上用 Safari
                                        打开点我即可直接添加</a></p>
                            <?php }?>
                        <?php } else { ?>
                            <p>您好，您目前的 加密方式，混淆，或者协议设置在 ShadowsocksR 客户端下无法连接。请您选用 Shadowsocks 客户端来连接，或者到
                                资料编辑 页面修改后再来查看此处。</p>
                        <?php }?>
					</div> 
					<div class="tab-pane fade" id="ssrtypelink-6" role="tabpanel" aria-labelledby="ssrtypelink-6-tab">
						
						<?php if (App\Utils\URL::SSCanConnect($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['mu']->value)) {?>
                            <?php if ($_smarty_tpl->tpl_vars['ss_item']->value['obfs'] == "v2ray" && App\Utils\URL::CanMethodConnect($_smarty_tpl->tpl_vars['user']->value->method) != 2) {?>
                                <p>您好，Shadowsocks V2Ray-Plugin 节点需要您的加密方式使用 AEAD 系列。请您到 资料编辑
                                    页面修改后再来查看此处。</p>
                            <?php } else { ?>
                                <p><a class="copy-text" href="javascript:void(0);" data-clipboard-text="<?php echo App\Utils\URL::getItemUrl($_smarty_tpl->tpl_vars['ss_item']->value,1);?>
">点我复制配置链接</a>
                                </p>
                                <p><a href="<?php echo App\Utils\URL::getItemUrl($_smarty_tpl->tpl_vars['ss_item']->value,1);?>
">iOS 上用 Safari 打开点我即可直接添加</a>
                                </p>
                            <?php }?>
                        <?php } else { ?>
                            <p>您好，您目前的 加密方式，混淆，或者协议设置在 Shadowsocks 客户端下无法连接。请您选用 ShadowsocksR 客户端来连接，或者到
                                资料编辑 页面修改后再来查看此处。</p>
                        <?php }?>
					</div>
				</div>
              </div>
            </div><!--card-->
		</div>
		<div class="col-lg-12 col-sm-12">
			<div class="card">
              <!-- Card header -->
              <div class="card-header" style="padding:.8rem 1.2rem">
                <h5 class="mb-0">配置二维码</h5>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <div class="nav-wrapper">
					<ul class="nav nav-pills nav-fill" id="ssrtypecode" role="tablist">
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0 active" id="ssrtypecode-7-tab" data-toggle="tab" href="#ssrtypecode-7" role="tab" aria-controls="ssrtypecode-7" aria-selected="true"><i class="fa fa-plane fa-lg icon-ver"></i> ShadowsocksR</a>
						</li>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="ssrtypecode-8-tab" data-toggle="tab" href="#ssrtypecode-8" role="tab" aria-controls="ssrtypecode-8" aria-selected="false"><i class="fa  fa-fighter-jet fa-lg icon-ver"></i> Shadowsocks</a>
						</li>
					</ul>
				</div>
				<div class="tab-content" id="myTabContent4">
					<div class="tab-pane fade show active" id="ssrtypecode-7" role="tabpanel" aria-labelledby="ssrtypecode-7-tab">
						
						<?php if (App\Utils\URL::SSRCanConnect($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['mu']->value)) {?>
                            <?php if ($_smarty_tpl->tpl_vars['ssr_item']->value['obfs'] == "v2ray") {?>
                                <p>您好，Shadowsocks V2Ray-Plugin 节点需要您的加密方式使用 AEAD 系列。请您到 资料编辑
                                    页面修改后再来查看此处。</p>
                            <?php } else { ?>
                                <div class="text-center">
                                    <div id="ss-qr-n"></div>
                                </div>
								<br>
								<div class="bg-secondary text-center">
									<blockquote class="blockquote" style="word-break:break-all">
										<p><?php echo App\Utils\URL::getItemUrl($_smarty_tpl->tpl_vars['ssr_item']->value,0);?>
</p>
									</blockquote>
								</div>
                            <?php }?>
                        <?php } else { ?>
                            <p>您好，您目前的 加密方式，混淆，或者协议设置在 ShadowsocksR 客户端下无法连接。请您选用 Shadowsocks 客户端来连接，或者到
                                资料编辑 页面修改后再来查看此处。</p>
                        <?php }?>
					</div> 
					<div class="tab-pane fade" id="ssrtypecode-8" role="tabpanel" aria-labelledby="ssrtypecode-8-tab">
						<div class="text-center">
						<?php if (App\Utils\URL::SSCanConnect($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['mu']->value)) {?>
                            <?php if ($_smarty_tpl->tpl_vars['ss_item']->value['obfs'] == "v2ray" && App\Utils\URL::CanMethodConnect($_smarty_tpl->tpl_vars['user']->value->method) != 2) {?>
                                <p>您好，Shadowsocks V2Ray-Plugin 节点需要您的加密方式使用 AEAD 系列。请您到 资料编辑
                                    页面修改后再来查看此处。</p>
                            <?php } else { ?>
                                
                                    <div class="text-center">
                                        <div id="ss-qr"></div>
                                    </div>
									
									<br>
								<div class="bg-secondary text-center" style="word-break:break-all">
									<blockquote class="blockquote">
										<p><?php echo App\Utils\URL::getItemUrl($_smarty_tpl->tpl_vars['ss_item']->value,1);?>
</p>
									</blockquote>
								</div>
                                
                            <?php }?>
                        <?php } else { ?>
                            <p>您好，您目前的 加密方式，混淆，或者协议设置在 Shadowsocks 客户端下无法连接。请您选用 ShadowsocksR 客户端来连接，或者到
                                资料编辑 页面修改后再来查看此处。</p>
                        <?php }?>
                        </div>
					</div>
				</div>
              </div>
            </div><!--card-->
		</div>	
	  </div><!--row-->
 
        <!-- Footer -->
      <footer class="footer pt-0">
        <div class="row align-items-center justify-content-lg-between">
          <div class="col-lg-6">
            <div class="copyright text-center text-lg-left text-muted">
              &copy; 2020 <a href="https://t.me/Fanyum" class="font-weight-bold ml-1" target="_blank">Fanyum</a>
            </div>
          </div>
          <div class="col-lg-6">
            <ul class="nav nav-footer justify-content-center justify-content-lg-end">
              <li class="nav-item">
                <a class="nav-link" href="/user/code" title="充值" target="_blank"><i class="fa fa-credit-card-alt fa-lg"></i>&nbsp;充值</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/help" title="帮助中心" target="_blank"><i class="fa fa-heart fa-lg"></i>&nbsp;帮助</a>
              </li>
              
              <li class="nav-item">
                <a class="nav-link" href="/user" title="用户中心" target="_blank"><i class="fa fa-home fa-lg"></i>&nbsp;用户</a>
              </li>
              
              
              <?php if ($_smarty_tpl->tpl_vars['config']->value['admin_qq_bottom'] != null) {?>
              <li class="nav-item">
                <a class="nav-link" href="http://wpa.qq.com/msgrd?v=3&uin=<?php echo $_smarty_tpl->tpl_vars['config']->value['admin_qq_bottom'];?>
&site=qq&menu=yes" title="QQ客服" target="_blank"><i class="fa fa-qq fa-lg icon-ver"></i>&nbsp;QQ</a>
              </li>
              <?php }?>
            </ul>
          </div>
        </div>
      </footer>
      </div><!-- Page content -->
  </div><!-- Main content -->
  <!-- Core -->
  <?php echo '<script'; ?>
 src="/theme/czssr/main/js/<EMAIL>?v=1.1.4"><?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
 src="/theme/czssr/main/js/bootstrap.bundle.min.js"><?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
 src="/theme/czssr/main/js/clipboard.min.js"><?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
 src="/theme/czssr/main/js/sweetalert2.min.js"><?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
 src="/theme/czssr/main/js/jquery.scrollbar.min.js"><?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
 src="/theme/czssr/main/js/jquery-scrolllock.min.js"><?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
>
   var md5 = '<?php echo $_smarty_tpl->tpl_vars['config']->value["verify"];?>
';
  <?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
 src="/theme/czssr/main/js/czssr.js?v=1.1.12"><?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
 src="/theme/czssr/main/js/qrcode.min.js"><?php echo '</script'; ?>
>
	
<?php echo '<script'; ?>
>

    <?php if (App\Utils\URL::SSCanConnect($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['mu']->value)) {?>
    var text_qrcode = '<?php echo App\Utils\URL::getItemUrl($_smarty_tpl->tpl_vars['ss_item']->value,1);?>
';
          //  text_qrcode_win = '<?php echo App\Utils\URL::getItemUrl($_smarty_tpl->tpl_vars['ss_item']->value,2);?>
';

    var qrcode1 = new QRCode(document.getElementById("ss-qr"), {
                correctLevel: 3
            });
          //  qrcode2 = new QRCode(document.getElementById("ss-qr-win"), {
          //      correctLevel: 3
          //  });

    qrcode1.clear();
    qrcode1.makeCode(text_qrcode);
   // qrcode2.clear();
   // qrcode2.makeCode(text_qrcode_win);
    <?php }?>

    <?php if (App\Utils\URL::SSRCanConnect($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['mu']->value)) {?>
    var text_qrcode2 = '<?php echo App\Utils\URL::getItemUrl($_smarty_tpl->tpl_vars['ssr_item']->value,0);?>
';
    var qrcode3 = new QRCode(document.getElementById("ss-qr-n"), {
        correctLevel: 3
    });
    qrcode3.clear();
    qrcode3.makeCode(text_qrcode2);
    <?php }
echo '</script'; ?>
>

  </body>

</html>
<?php }
}
