<?php
/* Smarty version 3.1.33, created on 2022-07-17 17:56:19
  from '/www/wwwroot/www.shadowingy.com/resources/views/czssr/admin/index.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62d3dcc382a384_76199693',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '6d8a18e15d8a62ac5498a763a4faaa4a8a351304' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.com/resources/views/czssr/admin/index.tpl',
      1 => 1575627708,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/main.tpl' => 1,
    'file:table/checkbox.tpl' => 1,
    'file:table/table.tpl' => 1,
    'file:admin/footer.tpl' => 1,
    'file:table/js_1.tpl' => 1,
    'file:table/lang_chinese.tpl' => 1,
    'file:table/js_3.tpl' => 1,
  ),
),false)) {
function content_62d3dcc382a384_76199693 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Default</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">管理中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">首页</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/ticket" class="btn btn-sm btn-neutral">工单</a>
            </div>
          </div>
          <!-- Card stats -->
          <div class="row">
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">累计注册用户</h5>
                      <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['sts']->value->getTotalUser();?>
 <small>个用户</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-red text-white rounded-circle shadow">
                        <i class="ni ni-active-40"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/admin/user"><i class="ni ni-spaceship icon-ver"></i>&nbsp;查看用户列表</a></span>
                  </p>
                </div>
              </div>
            </div>
			<div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">历史累计收入</h5>
                      <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['sale_money_num']->value;?>
 <small>CNY</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-green text-white rounded-circle shadow">
                        <i class="ni ni-money-coins"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/admin/bought"><i class="ni ni-credit-card icon-ver"></i>&nbsp;查看购买记录</a></span>
                  </p>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">当前在线人数</h5>
                      <span class="h2 font-weight-bold mb-0"><?php echo ($_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(3600)-$_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(60));?>
 <small>人</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-orange text-white rounded-circle shadow">
                        <i class="ni ni-chart-pie-35"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/admin/alive"><i class="ni ni-laptop icon-ver"></i>&nbsp;查看在线人数详情</a></span>
                  </p>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">累计节点线路</h5>
                      <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['nodes_all_num']->value;?>
 <small>条</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                        <i class="ni ni-chart-bar-32"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/admin/node"><i class="ni ni-spaceship icon-ver"></i>&nbsp;查看节点列表</a></span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <div class="row">
        <div class="col-lg-6 col-md-6">
          <div class="card">
			<div class="card-header bg-transparent">
               <h4 class="mb-0">用户签到情况(总用户 <?php echo $_smarty_tpl->tpl_vars['sts']->value->getTotalUser();?>
人)</h4>
            </div>
            <div class="card-body">
				<div style="height: 300px; width: 100%;">
				<canvas id="chart-doughnut1"></canvas>
				</div>
			</div>
          </div>
		  <div class="card">
			<div class="card-header bg-transparent">
               <h4 class="mb-0">用户在线情况(总用户 <?php echo $_smarty_tpl->tpl_vars['sts']->value->getTotalUser();?>
人)</h4>
            </div>
            <div class="card-body">
				<div style="height: 300px; width: 100%;">
				<canvas id="chart-doughnut3"></canvas>
				</div>
			</div>
          </div>
		</div>  
		
        <div class="col-lg-6 col-md-6">
          <div class="card">
			<div class="card-header bg-transparent">
               <h4 class="mb-0">节点在线情况(节点数 <?php echo $_smarty_tpl->tpl_vars['sts']->value->getTotalNodes();?>
个)</h4>
            </div>
            <div class="card-body">
				<div style="height: 300px; width: 100%;">
				<canvas id="chart-doughnut2"></canvas>
				</div>
			</div>
          </div>
		  <!--Traffic-->
		  <div class="card">
		    <div class="card-header bg-transparent">
               <h4 class="mb-0">流量使用情况(总分配流量 <?php echo $_smarty_tpl->tpl_vars['sts']->value->getTotalTraffic();?>
)</h4>
            </div>
            <div class="card-body">
				<div style="height: 300px; width: 100%;">
				<canvas id="chart-doughnut4"></canvas>
				</div>
			</div>
          </div>
        </div>
		
      </div><!--row-->
	  <div class="row">
		<div class="col-lg-4 col-md-6 col-sm-6">
          <!-- Basic with card header -->
          <div class="card">
            <!-- Card header -->
            <div class="card-header">
              <!-- Title -->
              <h5 class="h3 mb-0">财务简报</h5>
            </div>
            <!-- Card image -->
            <!-- List group -->
            <!-- Card body -->
            <div class="card-body">
              <ul class="list-group list-group-flush">
              	<li class="list-group-item badge-dot mr-4"><i class="bg-success"></i>今日笔数: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getFinanceToday('count');?>
 笔&nbsp;<i class="bg-success"></i>昨日笔数: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getFinanceYesterday('count');?>
 笔</li>
              	<li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>今日流水: ￥<?php echo $_smarty_tpl->tpl_vars['sts']->value->getFinanceToday('total');?>
&nbsp;<i class="bg-warning"></i>昨日流水: ￥<?php echo $_smarty_tpl->tpl_vars['sts']->value->getFinanceYesterday('total');?>
</li>
              	<li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>7天笔数: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getFinanceWeek('count');?>
 笔&nbsp;<i class="bg-warning"></i>7天流水: ￥<?php echo $_smarty_tpl->tpl_vars['sts']->value->getFinanceWeek('total');?>
</li>
                <li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>上月笔数: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getFinanceMonth('count');?>
 笔&nbsp;<i class="bg-warning"></i>上月流水: ￥<?php echo $_smarty_tpl->tpl_vars['sts']->value->getFinanceMonth('total');?>
</li>
              </ul>
            </div>
          </div>
		</div>
		<div class="col-lg-4 col-md-6 col-sm-6">
          <!-- Basic with card header -->
          <div class="card">
            <!-- Card header -->
            <div class="card-header">
              <!-- Title -->
              <h5 class="h3 mb-0">人数简报</h5>
            </div>
            <!-- Card image -->
            <!-- List group -->
            <!-- Card body -->
            <div class="card-body">
              <ul class="list-group list-group-flush">
              	<li class="list-group-item badge-dot mr-4"><i class="bg-success"></i>今日新注册: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getUserNumToday();?>
 人&nbsp;<i class="bg-success"></i>昨日注册: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getUserNumYesterday();?>
 人</li>
              	<li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>7天注册: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getUserNumWeek();?>
 人&nbsp;<i class="bg-warning"></i>上月注册: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getUserNumMonth();?>
 人</li>
              	<li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>
                  <?php if ($_smarty_tpl->tpl_vars['sts']->value->getUserNumWeek() != 0) {?>
                  7天转化: <?php echo round(($_smarty_tpl->tpl_vars['sts']->value->getFinanceWeek('count')/$_smarty_tpl->tpl_vars['sts']->value->getUserNumWeek()*100),2);?>
 %&nbsp;
                  <?php } else { ?>
                  7天转化: 0 %&nbsp;
                  <?php }?>
                  <i class="bg-warning"></i>
                  <?php if ($_smarty_tpl->tpl_vars['sts']->value->getUserNumMonth() != 0) {?>
                  上月转化: <?php echo round(($_smarty_tpl->tpl_vars['sts']->value->getFinanceMonth('count')/$_smarty_tpl->tpl_vars['sts']->value->getUserNumMonth()*100),2);?>
 %</li>
                  <?php } else { ?>
                  上月转化: 0 %
                  <?php }?>
				<li class="list-group-item badge-dot mr-4"><i class="bg-danger"></i>7天账号过期: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getUserClassExp('expire_in');?>
 人&nbsp;<i class="bg-danger"></i>7天等级过期: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getUserClassExp('class_expire');?>
 人</li>
              </ul>
            </div>
          </div>
		</div>
		<div class="col-lg-4 col-md-6 col-sm-6">
          <!-- Basic with card header -->
          <div class="card">
            <!-- Card header -->
            <div class="card-header">
              <!-- Title -->
              <h5 class="h3 mb-0">客户付费简报</h5>
            </div>
            <!-- Card image -->
            <!-- List group -->
            <!-- Card body -->
            <div class="card-body">
              <ul class="list-group list-group-flush">
              	<li class="list-group-item badge-dot mr-4"><i class="bg-success"></i>总用户: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getTotalUser();?>
 人&nbsp;</li>
              	<li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>白嫖用户: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getUserPayNum('nopay');?>
 人&nbsp;</li>
              	<li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>付费用户: <?php echo $_smarty_tpl->tpl_vars['sts']->value->getUserPayNum('yespay');?>
 人&nbsp;</li>
                <li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>付费转化: <?php echo round(($_smarty_tpl->tpl_vars['sts']->value->getUserPayNum('yespay')/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()*100),2);?>
 %&nbsp;</li>
              </ul>
            </div>
          </div>
		</div>
	  </div><!--row-->
	  <div class="row">
		<div class="col">
	        <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h4 class="mb-0">流量排行榜(异常示警)</h4>
              </div>
              <!-- Card body -->
			  <div class="card-body">
				<blockquote class="blockquote mb-0">
				     <?php $_smarty_tpl->_subTemplateRender('file:table/checkbox.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
				</blockquote>
			  </div>			
              <div class="card-body">
				<div class="table-responsive">
				  <?php $_smarty_tpl->_subTemplateRender('file:table/table.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
				</div>
              </div>
            </div><!--card-->
		</div><!--Gird-->
	   </div><!--row-->
      
	  <?php $_smarty_tpl->_subTemplateRender('file:admin/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	<?php echo '<script'; ?>
 src="/theme/czssr/main/js/chart.min.js"><?php echo '</script'; ?>
>
    <?php echo '<script'; ?>
 src="//cdn.jsdelivr.net/npm/datatables.net@1.10.19"><?php echo '</script'; ?>
>  
    <?php echo '<script'; ?>
 src="/theme/czssr/main/js/dataTables.material.min.js"><?php echo '</script'; ?>
>
<!--流量排行榜-->
<?php echo '<script'; ?>
>
 <?php $_smarty_tpl->_subTemplateRender('file:table/js_1.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
    window.addEventListener('load', () => {
       table_1 = $('#table_1').DataTable({
           order: [[1, 'asc']],
           stateSave: true,
           serverSide: true,
           ajax: {
               url: "/admin/traffic_used/ajax",
               type: "POST",
           },
           columns: [
               
               {"data": "id"},
               {"data": "email"},
               {"data": "transfer"},
               {"data": "todayTraffic"},
               {"data": "allTraffic"},
               {"data": "transfer_enable"},
			   
            ],
			"columnDefs": [
                {
                    targets: ['_all'],
                    className: 'mdl-data-table__cell--non-numeric'
                }
            ],

            <?php $_smarty_tpl->_subTemplateRender('file:table/lang_chinese.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
        });

       <?php $_smarty_tpl->_subTemplateRender('file:table/js_3.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>  
  })	   
<?php echo '</script'; ?>
>	
<!--签到情况-->	
<?php echo '<script'; ?>
>
	//
	// Charts
	//
	'use strict';
	window.chartColors = {
		red: 'rgb(255, 99, 132)',
		orange: 'rgb(255, 159, 64)',
		yellow: 'rgb(255, 205, 86)',
		green: 'rgb(75, 192, 192)',
		blue: 'rgb(54, 162, 235)',
		purple: 'rgb(153, 102, 255)',
		grey: 'rgb(201, 203, 207)'
	};

	var ctx = document.getElementById("chart-doughnut1").getContext('2d');
	var myChart = new Chart(ctx, {
		type: 'doughnut',
		data: {
		datasets: [{
			data: [
					<?php echo number_format($_smarty_tpl->tpl_vars['sts']->value->getTodayCheckinUser()/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()*100,2);?>
,
					<?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getCheckinUser()-$_smarty_tpl->tpl_vars['sts']->value->getTodayCheckinUser())/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser())*100,2);?>
,
					<?php echo number_format((1-($_smarty_tpl->tpl_vars['sts']->value->getCheckinUser()/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()))*100,2);?>

						
					
				],
			backgroundColor: [
				window.chartColors.red,
				window.chartColors.orange,
				window.chartColors.blue,
			],
			label: 'Dataset 1'
		}],
			labels: [
				"今日签到用户 <?php echo number_format($_smarty_tpl->tpl_vars['sts']->value->getTodayCheckinUser()/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()*100,2);?>
% <?php echo $_smarty_tpl->tpl_vars['sts']->value->getTodayCheckinUser();?>
人",
				"曾经签到过的用户 <?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getCheckinUser()-$_smarty_tpl->tpl_vars['sts']->value->getTodayCheckinUser())/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser())*100,2);?>
% <?php echo $_smarty_tpl->tpl_vars['sts']->value->getCheckinUser()-$_smarty_tpl->tpl_vars['sts']->value->getTodayCheckinUser();?>
人",
				"没有签到过的用户 <?php echo number_format((1-($_smarty_tpl->tpl_vars['sts']->value->getCheckinUser()/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()))*100,2);?>
% <?php echo $_smarty_tpl->tpl_vars['sts']->value->getTotalUser()-$_smarty_tpl->tpl_vars['sts']->value->getCheckinUser();?>
人"
			]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			title: {
				display: false,
				//text: '用户签到情况(总用户 <?php echo $_smarty_tpl->tpl_vars['sts']->value->getTotalUser();?>
人)'
			}
		}
	});
<?php echo '</script'; ?>
>

<!--节点情况-->
<?php echo '<script'; ?>
>
	//
	// Charts
	//
	'use strict';
	window.chartColors = {
		red: 'rgb(255, 99, 132)',
		orange: 'rgb(255, 159, 64)',
		yellow: 'rgb(255, 205, 86)',
		green: 'rgb(75, 192, 192)',
		blue: 'rgb(54, 162, 235)',
		purple: 'rgb(153, 102, 255)',
		grey: 'rgb(201, 203, 207)'
	};

	var ctx = document.getElementById("chart-doughnut2").getContext('2d');
	var myChart = new Chart(ctx, {
		type: 'doughnut',
		data: {
		datasets: [{
			data: [
					<?php if ($_smarty_tpl->tpl_vars['sts']->value->getTotalNodes() != 0) {?>
					<?php echo number_format((1-($_smarty_tpl->tpl_vars['sts']->value->getAliveNodes()/$_smarty_tpl->tpl_vars['sts']->value->getTotalNodes()))*100,2);?>
,
					<?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getAliveNodes()/$_smarty_tpl->tpl_vars['sts']->value->getTotalNodes()))*100,2);?>

					<?php }?>
					
				],
			backgroundColor: [
				window.chartColors.red,
				//window.chartColors.orange,
				window.chartColors.blue,
			],
			label: 'Dataset 1'
		}],
			labels: [
				<?php if ($_smarty_tpl->tpl_vars['sts']->value->getTotalNodes() != 0) {?>
				"离线节点 <?php echo number_format((1-($_smarty_tpl->tpl_vars['sts']->value->getAliveNodes()/$_smarty_tpl->tpl_vars['sts']->value->getTotalNodes()))*100,2);?>
% <?php echo $_smarty_tpl->tpl_vars['sts']->value->getTotalNodes()-$_smarty_tpl->tpl_vars['sts']->value->getAliveNodes();?>
个",
				"在线节点 <?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getAliveNodes()/$_smarty_tpl->tpl_vars['sts']->value->getTotalNodes()))*100,2);?>
% <?php echo $_smarty_tpl->tpl_vars['sts']->value->getAliveNodes();?>
个"
				<?php }?>
			]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			title: {
				display: false,
				//text: '节点在线情况(节点数 <?php echo $_smarty_tpl->tpl_vars['sts']->value->getTotalNodes();?>
个)'
			}
		}
	});
<?php echo '</script'; ?>
>

<!--在线人数情况-->
<?php echo '<script'; ?>
>
	//
	// Charts
	//
	'use strict';
	window.chartColors = {
		red: 'rgb(255, 99, 132)',
		orange: 'rgb(255, 159, 64)',
		yellow: 'rgb(255, 205, 86)',
		green: 'rgb(75, 192, 192)',
		blue: 'rgb(54, 162, 235)',
		purple: 'rgb(153, 102, 255)',
		grey: 'rgb(201, 203, 207)'
	};

	var ctx = document.getElementById("chart-doughnut3").getContext('2d');
	var myChart = new Chart(ctx, {
		type: 'doughnut',
		data: {
		datasets: [{
			data: [
					<?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getUnusedUser()/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()))*100,2);?>
,
					<?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getTotalUser()-$_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(86400)-$_smarty_tpl->tpl_vars['sts']->value->getUnusedUser())/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser())*100,2);?>
,
					<?php echo number_format(($_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(86400)-$_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(3600))/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()*100,2);?>
,
					<?php echo number_format(($_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(3600)-$_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(60))/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()*100,2);?>
,
					<?php echo number_format(($_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(60))/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()*100,2);?>

					
					
				],
			backgroundColor: [
                window.chartColors.red,
                window.chartColors.orange,
                window.chartColors.yellow,
                window.chartColors.green,
                window.chartColors.blue,
            ],
			label: 'Dataset 1'
		}],
			labels: [
				"从未在线的用户 <?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getUnusedUser()/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()))*100,2);?>
% <?php echo (($_smarty_tpl->tpl_vars['sts']->value->getUnusedUser()));?>
人",
				"一天以前在线的用户 <?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getTotalUser()-$_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(86400)-$_smarty_tpl->tpl_vars['sts']->value->getUnusedUser())/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser())*100,2);?>
% <?php echo ($_smarty_tpl->tpl_vars['sts']->value->getTotalUser()-$_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(86400)-$_smarty_tpl->tpl_vars['sts']->value->getUnusedUser());?>
人",
				"一天内在线的用户 <?php echo number_format(($_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(86400)-$_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(3600))/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()*100,2);?>
% <?php echo ($_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(86400)-$_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(3600));?>
人",
				"一小时内在线的用户 <?php echo number_format(($_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(3600)-$_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(60))/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()*100,2);?>
% <?php echo ($_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(3600)-$_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(60));?>
人",
				"一分钟内在线的用户 <?php echo number_format(($_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(60))/$_smarty_tpl->tpl_vars['sts']->value->getTotalUser()*100,2);?>
% <?php echo ($_smarty_tpl->tpl_vars['sts']->value->getOnlineUser(60));?>
人"
			]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			title: {
				display: false,
				//text: '用户在线情况(总用户 <?php echo $_smarty_tpl->tpl_vars['sts']->value->getTotalUser();?>
人)'
			}
		}
	});
<?php echo '</script'; ?>
>
			
<!--流量使用情况-->
<?php echo '<script'; ?>
>
	//
	// Charts
	//
	'use strict';
	window.chartColors = {
		red: 'rgb(255, 99, 132)',
		orange: 'rgb(255, 159, 64)',
		yellow: 'rgb(255, 205, 86)',
		green: 'rgb(75, 192, 192)',
		blue: 'rgb(54, 162, 235)',
		purple: 'rgb(153, 102, 255)',
		grey: 'rgb(201, 203, 207)'
	};

	var ctx = document.getElementById("chart-doughnut4").getContext('2d');
	var myChart = new Chart(ctx, {
		type: 'doughnut',
		data: {
		datasets: [{
			data: [
					<?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getRawUnusedTrafficUsage()/$_smarty_tpl->tpl_vars['sts']->value->getRawTotalTraffic()))*100,2);?>
,
					<?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getRawLastTrafficUsage()/$_smarty_tpl->tpl_vars['sts']->value->getRawTotalTraffic()))*100,2);?>
,
					<?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getRawTodayTrafficUsage()/$_smarty_tpl->tpl_vars['sts']->value->getRawTotalTraffic()))*100,2);?>

					
				],
			backgroundColor: [
				window.chartColors.blue,
				window.chartColors.orange,
				window.chartColors.red,
            ],
			label: 'Dataset 1'
		}],
			labels: [
				"总剩余可用 <?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getRawUnusedTrafficUsage()/$_smarty_tpl->tpl_vars['sts']->value->getRawTotalTraffic()))*100,2);?>
% <?php echo (($_smarty_tpl->tpl_vars['sts']->value->getUnusedTrafficUsage()));?>
",
				"总过去已用 <?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getRawLastTrafficUsage()/$_smarty_tpl->tpl_vars['sts']->value->getRawTotalTraffic()))*100,2);?>
% <?php echo (($_smarty_tpl->tpl_vars['sts']->value->getLastTrafficUsage()));?>
",
				"总今日已用 <?php echo number_format((($_smarty_tpl->tpl_vars['sts']->value->getRawTodayTrafficUsage()/$_smarty_tpl->tpl_vars['sts']->value->getRawTotalTraffic()))*100,2);?>
% <?php echo (($_smarty_tpl->tpl_vars['sts']->value->getTodayTrafficUsage()));?>
"
			]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			title: {
				display: false,
				//text: '流量使用情况(总分配流量 <?php echo $_smarty_tpl->tpl_vars['sts']->value->getTotalTraffic();?>
)'
			}
		}
	});
		
<?php echo '</script'; ?>
>
		<?php }
}
