<?php
/**
 * Created by PhpStorm.
 * User: tonyzou
 * Date: 2018/9/24
 * Time: 下午4:23
 */

namespace App\Services\Gateway;

use App\Models\Paylist;
use App\Models\Payback;
use App\Models\User;
use App\Models\Code;
use App\Services\Config;
use App\Utils\Telegram;

abstract class AbstractPayment
{
    abstract public function purchase($request, $response, $args);
    abstract public function notify($request, $response, $args);
    abstract public function getPurchaseHTML();
    abstract public function getReturnHTML($request, $response, $args);
    abstract public function getStatus($request, $response, $args);

    public function postPayment($pid, $method){
        $p = Paylist::where("tradeno", $pid)->first();

        if($p->status==1){
            return json_encode(['errcode'=>0]);
        }

        $p->status=1;
        $p->save();
        $user = User::find($p->userid);
        $user->money += $p->total;
        $user->save();
        $codeq=new Code();
        $codeq->code=$method;
        $codeq->isused=1;
        $codeq->type=-1;
        $codeq->number=$p->total;
        $codeq->usedatetime=date("Y-m-d H:i:s");
        $codeq->userid=$user->id;
        $codeq->save();

        if ($user->ref_by >= 1) {
            $gift_user=User::where("id", "=", $user->ref_by)->first();
            $gift_user->ref_money=($gift_user->ref_money+($codeq->number*($gift_user->payback_code /100)));
            $gift_user->save();
            $Payback=new Payback();
            $Payback->total=$codeq->number;
            $Payback->userid=$user->id;
            $Payback->ref_by=$user->ref_by;
            $Payback->ref_get=$codeq->number*($gift_user->payback_code /100);
            $Payback->datetime=time();
            $Payback->save();
        }

        if (Config::get('enable_donate') == 'true') {
            if ($user->is_hide == 1) {
                Telegram::Send("一位不愿透露姓名的大老爷给我们捐了 ".$codeq->number." 元!");
            } else {
                Telegram::Send($user->user_name." 大老爷给我们捐了 ".$codeq->number." 元！");
            }
        }
        return 0;

    }

    public static function generateGuid() {
        mt_srand((double)microtime()*10000);
        $charid = strtoupper(md5(uniqid(rand() + time(), true)));
        $hyphen = chr(45);
        $uuid   = chr(123)
            .substr($charid, 0, 8).$hyphen
            .substr($charid, 8, 4).$hyphen
            .substr($charid,12, 4).$hyphen
            .substr($charid,16, 4).$hyphen
            .substr($charid,20,12)
            .chr(125);
        $uuid = str_replace(['}', '{', '-'],'',$uuid);
        $uuid = substr($uuid, 0, 8);
        return $uuid;
    }


}
