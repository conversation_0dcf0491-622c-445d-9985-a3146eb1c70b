{include file='user/main.tpl'}
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Donate</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">捐赠公示</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/code" class="btn btn-sm btn-neutral">充值</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">捐赠公示</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
                      <a class="btn btn-primary btn-sm mb-3" href="/user/code">充值</a>
						<blockquote class="blockquote mb-0">
							<p class="description">感谢各位捐赠来支撑服务器的日常支出！您可以在充值界面进行充值，这样就等同于捐赠了.</p>
							{if $user->isAdmin()}
                                <p class="description">总收入: {$total_in} 元</p>
                            {/if}
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">匿名捐赠:</label>
							<p class="description">当前设置: <code>{if $user->is_hide==1} 匿名 {else} 不匿名 {/if}</code></p>
							<select id="hide" class="form-control form-control-sm" name="hide">
								<option value="1" {if $user->is_hide==1}selected{/if}>匿名</option>
                                <option value="0" {if $user->is_hide==0}selected{/if}>不匿名</option>
							</select>
						</div>
					</div>	
					<div class="modal-footer">
						<button id="hide-update" type="button" class="btn btn-primary">确认提交</button>
					</div>
				</div>
			</div>
			
            <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">捐赠表</h3>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive">
				{$codes->render()}
					<table class="table align-items-center table-flush">
					<thead class="thead-light">
						<tr>
							<th>ID</th>
                            <th>用户名</th>
                            <th>类型</th>
                            <th>操作</th>
                            <th>备注</th>
                            <th>时间</th>
						</tr>
					</thead>
					<tbody class="list">
				     {foreach $codes as $code}
					<tr>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>#{$code->id}</span>
						</td>
						{if $code->user() != null && $code->user()->is_hide == 0}
                        <td>{$code->user()->user_name}</td>
                            {else}
                        <td>用户匿名或已注销</td>
                            {/if}
                            {if $code->type == -1}
                        <td>充值捐赠</td>
                            {/if}
                            {if $code->type == -2}
                        <td>财务支出</td>
                            {/if}
                            {if $code->type == -1}
                        <td>捐赠 {$code->number} 元</td>
                            {/if}
                            {if $code->type == -2}
                        <td>支出 {$code->number} 元</td>
                            {/if}
                        <td>{$code->code}</td>
                        <td>{$code->usedatetime}</td>
					</tr>
					{/foreach}
					</tbody>
					</table>
				  {$codes->render()}
				</div>
              </div>
            </div><!--card-->
		
        </div>
      </div><!--row-->
	  
	  {include file='dialog.tpl'}

	  {include file='user/footer.tpl'}

	  