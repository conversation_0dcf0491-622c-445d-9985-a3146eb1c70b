<?php
/* Smarty version 3.1.33, created on 2022-02-04 15:37:41
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/table/js_delete.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_61fcd7c5e62297_34973229',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'f9f761cf56f7e4587b15a7cb8c2076fcb7b12dac' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/table/js_delete.tpl',
      1 => 1548128136,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_61fcd7c5e62297_34973229 (Smarty_Internal_Template $_smarty_tpl) {
?>table_1
    .row('#row_1_' + deleteid)
    .remove()
    .draw();
<?php }
}
