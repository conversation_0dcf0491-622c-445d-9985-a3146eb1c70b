## 应用概述
## [点击在线观看android视频教程](https://cdn.jsdelivr.net/gh/lxiaoya1003/tuchuang@master/androidssr99.mp4)

<video width="300" height="150" controls="controls">
<source src="https://cdn.jsdelivr.net/gh/lxiaoya1003/tuchuang@master/androidssr99.mp4" type="video/mp4" />
</video>

V2rayNG 是在 Android 平台上的客户端软件，支持 VMess 及 Shadowsocks 协议。

## 应用下载

以下是各平台该应用的下载地址。

## 无法下载APP点击进QQ群下载：
## [一键进QQ群](https://jq.qq.com/?_wv=1027&k=xnDoFVye)
手动复制群号：8群-748432512
![](/getsoft?type=V2rayNG ':include :type=markdown')

## 获取订阅

此处将显示您的订阅链接，请注意为登录状态：

[cinwell website](/sublink?type=v2ray ':include :type=markdown')

!> 这个 **订阅链接** 非常重要，你应当把它当做密码一样妥善保管。

## 配置 V2rayNG

打开 V2rayNG 点击左上角的菜单图标打开侧边栏，随后点击 **订阅设置**。

![1](https://i.loli.net/2019/02/13/5c62fd8327c0e.png ':size=600')

点击右上角的加号按钮，在新页面的备注中填写本站名称，地址输入框中粘贴上方 **[获取订阅](#获取订阅)** 中的订阅链接并点击右上角的 **√**。

![2](https://i.loli.net/2019/02/13/5c62fef253cd4.jpg ':size=200')

再次从侧边栏进入 **设置** 页面，点击 **路由模式** 将其更改为 **绕过局域网及大陆地址**。

![3](https://i.loli.net/2019/02/13/5c62ffab506fb.jpeg ':size=600')

随后从侧边栏回到 **配置文件** 页面，点击右上角的省略号图标选择更新订阅。

![4](https://i.loli.net/2019/02/13/5c630072445ec.jpeg ':size=400')

## 开始使用

点击选择您中意的节点，点击右下角的按钮即可连接。

如操作系统提示添加 VPN 配置，请点击 运行 并验证您的 密码、指纹等。

