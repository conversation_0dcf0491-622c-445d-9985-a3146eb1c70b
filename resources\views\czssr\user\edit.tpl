{include file='user/main.tpl'}
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Advanced Settings</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">连接设置</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/ticket/create" class="btn btn-sm btn-neutral">工单</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
          <!-- Card stats -->

        </div>
      </div>
    </div><!-- Header -->
	<div class="container-fluid mt--6">
	  <div class="row row-example">
		<div class="col-lg-7 col-md-7">
          <div class="card">
            <div class="card-header bg-transparent">
                <h3 class="mb-0"><i class="ni ni-atom ni-lg"></i>&nbsp;协议和混淆设置</h3>
            </div>
            <div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>当前协议:&nbsp;<code id="ajax-user-protocol">[{if URL::CanProtocolConnect($user->protocol) == 3}SS/SSD/SSR{else}SSR{/if} 可连接] {$user->protocol}</code></p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>如果需要兼容 SS/SSD 请设置为 origin 或选择带_compatible的兼容选项.</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>auth_chain 系为实验性协议, 可能造成不稳定或无法使用, 开启前请询问是否支持.</p>
				{$protocol_list = $config_service->getSupportParam('protocol')}
				<select id="protocol" class="form-control form-control-sm" value="{$user->protocol}">
				{foreach $protocol_list as $protocol}
					<option value="{$protocol}" {if $user->protocol == $protocol}selected{/if}>[{if URL::CanProtocolConnect($protocol) == 3}SS/SSD/SSR{else}SSR{/if} 可连接] {$protocol}</option>
				{/foreach}
				</select>
            </div>
			<div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>当前混淆:&nbsp;<code id="ajax-user-obfs">[{if URL::CanObfsConnect($user->obfs) >= 3}SS/SSD/SSR{elseif URL::CanObfsConnect($user->obfs) == 1}SSR{else}SS/SSD{/if} 可连接] {$user->obfs}</code></p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>如果需要兼容 SS/SSD 请设置为 plain 或选择带_compatible的兼容选项.</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>SS/SSD 和 SSR 支持的混淆类型有所不同, simple_obfs_* 为 SS/SSD 的混淆方式, 其他为 SSR 的混淆方式.</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>如果使用 SS/SSD 作为客户端, 请确保自己知道如何下载并使用混淆插件.</p>
				{$obfs_list = $config_service->getSupportParam('obfs')}
				<select id="obfs" class="form-control form-control-sm" value="{$user->obfs}">
				{foreach $obfs_list as $obfs}
					<option value="{$obfs}" {if $user->obfs == $obfs}selected{/if}>[{if URL::CanObfsConnect($obfs) >= 3}SS/SSD/SSR{else}{if URL::CanObfsConnect($obfs) == 1}SSR{else}SS/SSD{/if}{/if} 可连接] {$obfs}</option>
				{/foreach}
				</select>
            </div>
			<div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>当前混淆参数:&nbsp;<code id="ajax-user-obfs-param">{if $user->obfs_param == null}无{else}{$user->obfs_param}{/if}</code></p>
				<div class="p-4 bg-secondary">
					<input id="obfs-param" type="text" class="form-control form-control-alternative"  placeholder="请输入混淆参数">
				</div>
            </div>
			<div class="modal-footer">
               <button id="ssr-update" type="button" class="btn btn-danger">提交更改</button>
            </div>
          </div>
		  
		{if $config['port_price']>=0 || $config['port_price_specify']>=0}
		  <div class="card">
            <div class="card-header bg-transparent">
               <h3 class="mb-0"><i class="fa fa-repeat fa-lg"></i>&nbsp;重置端口</h3>
            </div>
			{if $config['port_price']>=0}
            <div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>随机更换一个端口, 价格:&nbsp;<code>{$config['port_price']}</code>元/次</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>重置后请更新订阅, 1分钟左右生效.</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>当前使用端口:&nbsp;<code id="ajax-user-port">{$user->port}</code></p>
            </div>
			<div class="modal-footer">
               <button id="portreset" type="button" class="btn btn-danger">确认提交</button>
            </div>
			{/if}
			{if $config['port_price_specify']>=0}
			<div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>不想摇号？来钦定端口吧～！</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>价格:&nbsp;<code>{$config['port_price_specify']}</code>元/次</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>端口范围:&nbsp;<code>{$config['min_port']}～{$config['max_port']}</code></p>
				<div class="p-4 bg-secondary">
					<input id="port-specify" type="text" class="form-control form-control-alternative" placeholder="请输入你想要的端口号">
				</div>
            </div>
			<div class="modal-footer">
               <button id="portspecify" type="button" class="btn btn-danger">确认提交</button>
            </div>
			{/if}
          </div>
		{/if}
		</div>
		
		<div class="col-lg-5 col-md-5">
			<div class="card">
            <div class="card-header bg-transparent">
               <h3 class="mb-0"><i class="ni ni-lock-circle-open ni-lg"></i>&nbsp;连接密码</h3>
            </div>
            <div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>当前连接密码:&nbsp;<code id="ajax-user-passwd">{$user->passwd}</code>&nbsp;&nbsp;您需要了解的是&nbsp;:</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>为了确保您的安全, 节点连接密码不允许自定义. 点击提交按钮将会自动生成由随机字母和数字组成的连接密码. </p>
                <p class="description badge-dot mr-4"><i class="bg-warning"></i>修改连接密码同时也会自动为您重新生成 V2Ray 节点的 UUID.</p>
                <p class="description badge-dot mr-4"><i class="bg-warning"></i>修改密码后, 请立刻更新各个客户端上的连接信息.</p>
				<div class="p-4 bg-secondary">
					<input type="text" class="form-control form-control-alternative" value="{$user->passwd}" readonly disabled />
				</div>
            </div>
			<div class="modal-footer">
               <button id="ss-pwd-update" type="button" class="btn btn-primary">确认提交</button>
            </div>
          </div>
		  <div class="card">
            <div class="card-header bg-transparent">
               <h3 class="mb-0"><i class="ni ni-key-25 ni-lg"></i>&nbsp;加密方式</h3>
            </div>
            <div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>当前加密方式:&nbsp;<code id="ajax-user-method">[{if URL::CanMethodConnect($user->method) == 2}SS/SSD{else}SS/SSR{/if}可连接] {$user->method}</code></p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>SS/SSD/SSR 各个客户端支持的加密方式有所不同, 请根据实际情况来进行选择.</p>
				{$method_list = $config_service->getSupportParam('method')}
				<select id="method" class="form-control form-control-sm" value="{$user->method}">
				{foreach $method_list as $method}
					<option value="{$method}" {if $user->method == $method}selected{/if}>[{if URL::CanMethodConnect($method) == 2}SS/SSD{else}SS/SSR{/if} 可连接] {$method}</option>
				{/foreach}
				</select>
            </div>
			<div class="modal-footer">
               <button id="method-update" type="button" class="btn btn-primary">确认提交</button>
            </div>
          </div>
        </div>
		
	  </div><!--row-->
	  
	  {include file='dialog.tpl'}
	  {include file='user/footer.tpl'}
	 
