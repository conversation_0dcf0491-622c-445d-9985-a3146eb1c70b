{include file='admin/main.tpl'}


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Create Detect rule</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/detect">审计系统</a></li>
                  <li class="breadcrumb-item active" aria-current="page">审计规则添加</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/user" class="btn btn-sm btn-neutral">用户</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">审计规则添加</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">规则名称:</label>
							<input id="name" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">规则描述:</label>
							<input id="text" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">规则正则表达式:</label>
							<input id="regex" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">规则类型:</label>
							<select id="type" class="form-control form-control-sm" name="type">
							    <option value="1">数据包明文匹配</option>
                                <option value="2">数据包 hex 匹配</option>
                            </select>
						</div>
					</div>
					<div class="modal-footer">
						<button id="submit" type="button" class="btn btn-primary">确认提交</button>
					</div>
				</div>
			</div>
			
        </div>
      </div><!--row-->
       
	 
		{include file='dialog.tpl'}
       
	  {include file='admin/footer.tpl'}
	  
<script>
    {include file='table/js_1.tpl'}

    function delete_modal_show(id) {
        deleteid = id;
        $("#delete_modal").modal();
    }


    window.addEventListener('load', () => {

        function submit() {
             $.ajax({
                type: "POST",
                url: "/admin/detect",
                dataType: "json",
                data: {
                    name: $$getValue("name"),
                    text: $$getValue("text"),
                    regex: $$getValue("regex"),
                    type: $$getValue("type")
                },
                success: data => {
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $('#msg').html("发生错误："+jqXHR.status);
                }
            });
        }

        $("#submit").on("click", submit);

    })

</script>

