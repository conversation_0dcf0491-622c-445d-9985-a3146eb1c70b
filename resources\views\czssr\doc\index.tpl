<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta charset="UTF-8">
  <title>{$appName}</title>
  <link rel="stylesheet" href="/theme/czssr/assets/doc/css/vue.css">
</head>
<body>
  <nav>
    <ul>
    <li><a href="/">回到主页</a></li>
      <li><a href="/user/">用户中心</a>
        <ul>
          <li><a href="/user/edit">资料编辑</a></li>
          <li><a href="/user/node">节点中心</a></li>
          <li><a href="/user/code">充值捐赠</a></li>
          <li><a href="/user/shop">套餐购买</a></li>
        </ul>
      </li>
    </ul>
  </nav>
  <div id="docs">加载中...</div>
  <script>
    const root = window.location.host;
    var test = window.location.href;
	var vars = test.split("/");
	var varss = vars[3].split("?");
	

    window.$docsify = {
      name: '{$appName}',
      alias: {
            '/.*/_sidebar.md': '/_sidebar.md'
      },
      basePath: '{$basePath}',
      auto2top: true,
      loadSidebar: true,
      autoHeader: true,
      homepage: 'index.md',
      nameLink: '/doc/',
      el: '#docs',
      copyCode: {
          buttonText : '点击拷贝',
          errorText  : '拷贝失败',
          successText: '拷贝成功'
      },
      {literal}
    
      plugins: [
        function(hook, vm) {
          hook.beforeEach((markdown) => {
            const result1 = markdown.replace(/\/sublink\?type=(\w+)/g, `//${root}/sublink?type=$1`);
            return result1;
          });
          hook.beforeEach((markdown) => {
            const result2 = markdown.replace(/\/getsoft\?type=(\w+)/g, `//${root}/getsoft?type=$1`);
            return result2;
          });
        },
      ]
      {/literal}
     
    }
    
  </script>
  <script src="/theme/czssr/assets/doc/js/docsify.min.js"></script>
  <script src="/theme/czssr/assets/doc/js/emoji.js"></script>
  <script src="/theme/czssr/assets/doc/js/zoom-image.js"></script>
  <script src="/theme/czssr/assets/doc/js/docsify-copy-code"></script>
  <script src="/theme/czssr/assets/doc/js/prism-yaml.min.js"></script>
</body>
</html>