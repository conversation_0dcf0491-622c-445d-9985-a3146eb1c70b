<?php
/* Smarty version 3.1.33, created on 2022-02-06 16:08:43
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/node/edit.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_61ff820b346a96_85329880',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '5fbfb14ca4947dbb968cfc6a31410fdf710facd5' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/node/edit.tpl',
      1 => 1577225266,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/main.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:admin/footer.tpl' => 1,
  ),
),false)) {
function content_61ff820b346a96_85329880 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Nodes</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
                  <li class="breadcrumb-item"><a href="/admin/node">节点列表</a></li>
				  <li class="breadcrumb-item active">编辑节点</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/user" class="btn btn-sm btn-neutral">用户</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card" id="main_form">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">编辑节点 #<?php echo $_smarty_tpl->tpl_vars['node']->value->id;?>
</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">节点名称:</label>
							<input id="name" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['node']->value->name;?>
">
						</div>
						<div class="form-group">
							<label class="form-control-label">节点地址(入口地址,端口偏移写这里 ;port=单端口号#偏移的目标端口号):</label>
							<input id="server" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['node']->value->server;?>
">
						</div>
						<div class="form-group">
							<label class="form-control-label">出口解析地址(V2须带上参数例如 xxx.com;8989;2;tls;ws;path=/v2ray|host=xxx.com)</label>
							<input id="server_out" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['node']->value->server_out;?>
">
						</div>
						<div class="form-group">
							<label class="form-control-label">节点IP(出口地址为域名的情况下可留空,不要把端口偏移写这里):</label>
							<input id="node_ip" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['node']->value->node_ip;?>
">
						</div>
						
						<div class="form-group" hidden="hidden">
							<label class="form-control-label">加密方式(隐藏):</label>
							<input id="method" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['node']->value->method;?>
">
						</div>
						<div class="form-group">
							<label class="form-control-label">流量比例:</label>
							<input id="rate" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['node']->value->traffic_rate;?>
">
						</div>
						<div class="form-group" hidden="hidden">
							<label class="custom-toggle icon-ver" for="custom_method">
								<input id="custom_method" type="checkbox" <?php if ($_smarty_tpl->tpl_vars['node']->value->custom_method == 1) {?>checked<?php }?>>
								<span class="custom-toggle-slider rounded-circle" data-label-off="No" data-label-on="Yes"></span>
							</label>&nbsp;&nbsp;自定义加密(隐藏)
						</div>
						<div class="form-group" hidden="hidden">
							<label class="custom-toggle icon-ver" for="custom_rss">
								<input id="custom_rss" type="checkbox" <?php if ($_smarty_tpl->tpl_vars['node']->value->custom_rss == 1) {?>checked<?php }?>>
								<span class="custom-toggle-slider rounded-circle" data-label-off="No" data-label-on="Yes"></span>
							</label>&nbsp;&nbsp;自定义协议&混淆(隐藏)
						</div>
						
						<label class="form-control-label" for="mu_only">单端口多用户启用:</label>
						<select id="mu_only" class="form-control form-control-sm" name="mu_only">
                            <option value="0" <?php if ($_smarty_tpl->tpl_vars['node']->value->mu_only == 0) {?>selected<?php }?>>单端口多用户与普通端口并存</option>
                            <option value="-1" <?php if ($_smarty_tpl->tpl_vars['node']->value->mu_only == -1) {?>selected<?php }?>>只启用普通端口</option>
                            <option value="1" <?php if ($_smarty_tpl->tpl_vars['node']->value->mu_only == 1) {?>selected<?php }?>>只启用单端口多用户</option>
                        </select>
					</div>
				</div>
			
			<div class="card bg-gradient-Secondary">
				<div class="card-body">
					<div class="form-group">
						<label class="custom-toggle icon-ver" for="type">
							<input id="type" type="checkbox" <?php if ($_smarty_tpl->tpl_vars['node']->value->type == 1) {?>checked<?php }?>>
							<span class="custom-toggle-slider rounded-circle" data-label-off="No" data-label-on="Yes"></span>
						</label>&nbsp;&nbsp;是否显示
					</div>
                   
					<div class="form-group">
						<label class="form-control-label">节点图标(例如中国填写:CN 美国填写:US):</label>
						<input id="status" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['node']->value->status;?>
">
					</div>
                  <div class="form-group">
					<label class="form-control-label" for="sort">节点类型:</label>
					<select id="sort" class="form-control form-control-sm" name="sort">
                        <option value="0" <?php if ($_smarty_tpl->tpl_vars['node']->value->sort == 0) {?>selected<?php }?>>Shadowsocks</option>
                         <option value="1" <?php if ($_smarty_tpl->tpl_vars['node']->value->sort == 1) {?>selected<?php }?>>VPN/Radius基础</option>
                         <option value="2" <?php if ($_smarty_tpl->tpl_vars['node']->value->sort == 2) {?>selected<?php }?>>SSH</option>
                         <option value="5" <?php if ($_smarty_tpl->tpl_vars['node']->value->sort == 5) {?>selected<?php }?>>Anyconnect</option>
                         <option value="9" <?php if ($_smarty_tpl->tpl_vars['node']->value->sort == 9) {?>selected<?php }?>>Shadowsocks 单端口多用户</option>
                         <option value="10" <?php if ($_smarty_tpl->tpl_vars['node']->value->sort == 10) {?>selected<?php }?>>Shadowsocks 中转</option>
                         <option value="11" <?php if ($_smarty_tpl->tpl_vars['node']->value->sort == 11) {?>selected<?php }?>>V2Ray</option>
                         <option value="12" <?php if ($_smarty_tpl->tpl_vars['node']->value->sort == 12) {?>selected<?php }?>>V2Ray 中转</option>
                         <option value="13" <?php if ($_smarty_tpl->tpl_vars['node']->value->sort == 13) {?>selected<?php }?>>Shadowsocks V2Ray-Plugin</option>
                    </select>
				  </div>
					<div class="form-group">
						<label class="form-control-label">节点描述:</label>
						<input id="info" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['node']->value->info;?>
">
					</div>
					<div class="form-group">
					<label class="form-control-label" for="class">节点等级:</label>
					<select id="class" class="form-control form-control-sm" name="class">
                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['levelList']->value, 'level');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['level']->value) {
?>
							<option value="<?php echo $_smarty_tpl->tpl_vars['level']->value->level;?>
" <?php if ($_smarty_tpl->tpl_vars['level']->value->level == $_smarty_tpl->tpl_vars['node']->value->node_class) {?>selected<?php }?>><?php echo $_smarty_tpl->tpl_vars['level']->value->name;?>
</option>
						<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>	
                    </select>
					</div>
					<div class="form-group">
					<label class="form-control-label" for="group">节点群组(等于用户群组):</label>
					<select id="group" class="form-control form-control-sm" name="group">
                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['groupList']->value, 'group');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['group']->value) {
?>
							<option value="<?php echo $_smarty_tpl->tpl_vars['group']->value->level;?>
" <?php if ($_smarty_tpl->tpl_vars['group']->value->level == $_smarty_tpl->tpl_vars['node']->value->node_group) {?>selected<?php }?>><?php echo $_smarty_tpl->tpl_vars['group']->value->name;?>
</option>
						<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>	
                    </select>
					</div>
				</div>
			</div>		
            <div class="card bg-gradient-Secondary">
				<div class="card-body">
					<div class="form-group">
						<label class="form-control-label">节点流量上限(GB, 不设上限请填0):</label>
						<input id="node_bandwidth_limit" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['node']->value->node_bandwidth_limit/1024/1024/1024;?>
">
					</div>
					<div class="form-group">
						<label class="form-control-label">节点流量上限清空日:</label>
						<input id="bandwidthlimit_resetday" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['node']->value->bandwidthlimit_resetday;?>
">
					</div>
					<div class="form-group">
						<label class="form-control-label">节点限速(Mbps):</label>
						<input id="node_speedlimit" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['node']->value->node_speedlimit;?>
">
					</div>
				</div>
			</div><!--card-->

			<div class="modal-footer">
                <button id="submit" type="button" class="btn btn-primary">确认提交</button>
			</div>
		  </div><!--card-->
        </div>
      </div><!--row-->
		<?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
       
	  <?php $_smarty_tpl->_subTemplateRender('file:admin/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


<?php echo '<script'; ?>
>

    window.addEventListener('load', () => {
		function submit() {
            if ($$.getElementById('custom_method').checked) {
                var custom_method = 1;
            } else {
                var custom_method = 0;
            }

            if ($$.getElementById('type').checked) {
                var type = 1;
            } else {
                var type = 0;
            }

            if ($$.getElementById('custom_rss').checked) {
                var custom_rss = 1;
            } else {
                var custom_rss = 0;
            }

            $.ajax({

                type: "PUT",
                url: "/admin/node/<?php echo $_smarty_tpl->tpl_vars['node']->value->id;?>
",
                dataType: "json",
                data: {
                    name: $$getValue('name'),
                    server: $$getValue('server'),
					server_out: $$getValue('server_out'),
                    node_ip: $$getValue('node_ip'),
                    method: $$getValue('method'),
                    custom_method,
                    rate: $$getValue('rate'),
                    info: $$getValue('info'),
                    type,
                    group: $$getValue('group'),
                    status: $$getValue('status'),
                    sort: $$getValue('sort'),
                    node_speedlimit: $$getValue('node_speedlimit'),
                    class: $$getValue('class'),
                    node_bandwidth_limit: $$getValue('node_bandwidth_limit'),
                    bandwidthlimit_resetday: $$getValue('bandwidthlimit_resetday'),
                    custom_rss,
                    mu_only: $$getValue('mu_only')
                },
                success: (data) => {
                    if (data.ret) {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                       // window.setTimeout("location.href=top.document.referrer", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);

                    } else {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                    }
                },

                error: (jqXHR) => {
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            });
		}
		$("#submit").on("click", submit);
    })
<?php echo '</script'; ?>
>

<?php }
}
