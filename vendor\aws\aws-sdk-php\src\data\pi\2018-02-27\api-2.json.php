<?php
// This file was auto-generated from sdk-root/src/data/pi/2018-02-27/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-02-27', 'endpointPrefix' => 'pi', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'AWS PI', 'serviceFullName' => 'AWS Performance Insights', 'serviceId' => 'PI', 'signatureVersion' => 'v4', 'signingName' => 'pi', 'targetPrefix' => 'PerformanceInsightsv20180227', 'uid' => 'pi-2018-02-27', ], 'operations' => [ 'DescribeDimensionKeys' => [ 'name' => 'DescribeDimensionKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDimensionKeysRequest', ], 'output' => [ 'shape' => 'DescribeDimensionKeysResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'GetResourceMetrics' => [ 'name' => 'GetResourceMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourceMetricsRequest', ], 'output' => [ 'shape' => 'GetResourceMetricsResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], ], 'shapes' => [ 'DataPoint' => [ 'type' => 'structure', 'required' => [ 'Timestamp', 'Value', ], 'members' => [ 'Timestamp' => [ 'shape' => 'ISOTimestamp', ], 'Value' => [ 'shape' => 'Double', ], ], ], 'DataPointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataPoint', ], ], 'DescribeDimensionKeysRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'Identifier', 'StartTime', 'EndTime', 'Metric', 'GroupBy', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Identifier' => [ 'shape' => 'String', ], 'StartTime' => [ 'shape' => 'ISOTimestamp', ], 'EndTime' => [ 'shape' => 'ISOTimestamp', ], 'Metric' => [ 'shape' => 'String', ], 'PeriodInSeconds' => [ 'shape' => 'Integer', ], 'GroupBy' => [ 'shape' => 'DimensionGroup', ], 'PartitionBy' => [ 'shape' => 'DimensionGroup', ], 'Filter' => [ 'shape' => 'MetricQueryFilterMap', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeDimensionKeysResponse' => [ 'type' => 'structure', 'members' => [ 'AlignedStartTime' => [ 'shape' => 'ISOTimestamp', ], 'AlignedEndTime' => [ 'shape' => 'ISOTimestamp', ], 'PartitionKeys' => [ 'shape' => 'ResponsePartitionKeyList', ], 'Keys' => [ 'shape' => 'DimensionKeyDescriptionList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DimensionGroup' => [ 'type' => 'structure', 'required' => [ 'Group', ], 'members' => [ 'Group' => [ 'shape' => 'String', ], 'Dimensions' => [ 'shape' => 'StringList', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DimensionKeyDescription' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'DimensionMap', ], 'Total' => [ 'shape' => 'Double', ], 'Partitions' => [ 'shape' => 'MetricValuesList', ], ], ], 'DimensionKeyDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionKeyDescription', ], ], 'DimensionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Double' => [ 'type' => 'double', ], 'GetResourceMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'Identifier', 'MetricQueries', 'StartTime', 'EndTime', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Identifier' => [ 'shape' => 'String', ], 'MetricQueries' => [ 'shape' => 'MetricQueryList', ], 'StartTime' => [ 'shape' => 'ISOTimestamp', ], 'EndTime' => [ 'shape' => 'ISOTimestamp', ], 'PeriodInSeconds' => [ 'shape' => 'Integer', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetResourceMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'AlignedStartTime' => [ 'shape' => 'ISOTimestamp', ], 'AlignedEndTime' => [ 'shape' => 'ISOTimestamp', ], 'Identifier' => [ 'shape' => 'String', ], 'MetricList' => [ 'shape' => 'MetricKeyDataPointsList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ISOTimestamp' => [ 'type' => 'timestamp', ], 'Integer' => [ 'type' => 'integer', ], 'InternalServiceError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, ], 'InvalidArgumentException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Limit' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 20, 'min' => 0, ], 'MetricKeyDataPoints' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'ResponseResourceMetricKey', ], 'DataPoints' => [ 'shape' => 'DataPointsList', ], ], ], 'MetricKeyDataPointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricKeyDataPoints', ], ], 'MetricQuery' => [ 'type' => 'structure', 'required' => [ 'Metric', ], 'members' => [ 'Metric' => [ 'shape' => 'String', ], 'GroupBy' => [ 'shape' => 'DimensionGroup', ], 'Filter' => [ 'shape' => 'MetricQueryFilterMap', ], ], ], 'MetricQueryFilterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'MetricQueryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricQuery', ], 'max' => 15, 'min' => 1, ], 'MetricValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], ], 'NotAuthorizedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResponsePartitionKey' => [ 'type' => 'structure', 'required' => [ 'Dimensions', ], 'members' => [ 'Dimensions' => [ 'shape' => 'DimensionMap', ], ], ], 'ResponsePartitionKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponsePartitionKey', ], ], 'ResponseResourceMetricKey' => [ 'type' => 'structure', 'required' => [ 'Metric', ], 'members' => [ 'Metric' => [ 'shape' => 'String', ], 'Dimensions' => [ 'shape' => 'DimensionMap', ], ], ], 'ServiceType' => [ 'type' => 'string', 'enum' => [ 'RDS', ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 10, 'min' => 1, ], ],];
