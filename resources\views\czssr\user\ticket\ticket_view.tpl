{include file='user/main.tpl'}
<link rel="stylesheet" href="/theme/czssr/main/css/quill.core.css" type="text/css">
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Tickets</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
				  <li class="breadcrumb-item"><a href="/user/ticket">工单系统</a></li>
                  <li class="breadcrumb-item active" aria-current="page">查看工单</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/shop" class="btn btn-sm btn-neutral">商店购买</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">查看工单</h3>
              </div>
              <!-- Card body -->
				<div class="bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group mt-3">
							<label class="form-control-label">内容(点击上传图片后会自动上传比较慢等缓存,别重复点)&nbsp;:&nbsp;</label>
							<div id="editor" data-toggle="quill">
			
							</div>
						</div>
                      <div class="modal-footer">
						<button id="submit" type="button" class="btn btn-primary">添加</button>
						<button id="close" type="button" class="btn btn-Secondary">添加并关闭</button>
						<button id="close_directly" type="button" class="btn btn-Secondary">直接关闭</button>
					  </div>
					</div>
					<div class="card-body">
					{$ticketset->render()}
						<div class="accordion" id="accordionExample">
						{foreach $ticketset as $ticket}
							<div class="card">
								<div class="card-header" id="heading" data-toggle="collapse" data-target="#collapse{$ticket->id}" aria-expanded="false" aria-controls="collapseOne">
								  {if $ticketset_nums+1 == $ticket->id}
                                  <h5 class="mb-0 badge-dot mr-4"><i class="bg-success"></i> {$ticket->datetime()} # {$ticket->User()->user_name}</h5>
                                  {else}
                                  <h5 class="mb-0"> {$ticket->datetime()} # {$ticket->User()->user_name}</h5>
                                  {/if}
								</div>
								<div id="collapse{$ticket->id}" class="collapse" aria-labelledby="heading" data-parent="#accordionExample">
									<div class="card-body col-md-auto">
									{$ticket->content}
									</div>
								</div>
							</div>
						{/foreach}
						</div>
					{$ticketset->render()}	
					</div>
				</div>
			</div>
		
        </div>
      </div><!--row-->
	  
	{include file='dialog.tpl'}
	{include file='user/footer.tpl'}
<script src="/theme/czssr/main/js/quill.min.js"></script>	  

<script>
// Quill.js

'use strict';

var QuillEditor = (function() {

	// Variables
	var $quill = $('[data-toggle="quill"]');

	// Methods

	function init($this) {

		// Get placeholder
		var placeholder = $this.data('quill-placeholder');

		// Init editor
		var quill = new Quill('#editor', {
			modules: {
				toolbar: [
					['bold', 'italic'],
					['link', 'blockquote', 'code', 'image'],
					[{
						'list': 'ordered'
					}, {
						'list': 'bullet'
					}]
				]
			},
			placeholder: placeholder,
			theme: 'snow'
		});

	}
        
	// Events

	if ($quill.length) {
		$quill.each(function() {
			init($(this));
		});
	}

})();
  
</script>
		
<script>
    $(document).ready(function () {
        function submit() {
		
            $("#result").modal();
            $$.getElementById('msg').innerHTML = '正在提交';
			
			var quill_html = document.querySelector('#editor').children[0].innerHTML;
			
            $.ajax({
                type: "PUT",
                url: "/user/ticket/{$id}",
                dataType: "json",
                data: {
                    content: quill_html,
                    status
                },
                success: function(data){
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href='/user/ticket'", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: function(jqXHR) {
                    $("#result").modal();
                    $("#msg").html(jqXHR+"  发生了错误。");
                }
            });
        }

        $("#submit").click(function () {
            status = 1;
            submit();
        });
		$("#close").click(function () {
            status = 0;
            submit();
        });
		$("#close_directly").click(function () {
            status = 0;
            $("#result").modal();
            $$.getElementById('msg').innerHTML = '正在提交';
            $.ajax({
                type: "PUT",
                url: "/user/ticket/{$id}",
                dataType: "json",
                data: {
                    content: '这条工单已被关闭',
                    status
                },
                success: function(data){
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href='/user/ticket'", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: function(jqXHR) {
                    $("#result").modal();
                    $("#msg").html(jqXHR+"  发生了错误。");
                }
            });
        });
    })
</script>