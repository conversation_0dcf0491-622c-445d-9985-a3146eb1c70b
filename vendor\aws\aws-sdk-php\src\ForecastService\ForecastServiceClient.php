<?php
namespace Aws\ForecastService;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Forecast Service** service.
 * @method \Aws\Result createDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDatasetAsync(array $args = [])
 * @method \Aws\Result createDatasetGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDatasetGroupAsync(array $args = [])
 * @method \Aws\Result createDatasetImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDatasetImportJobAsync(array $args = [])
 * @method \Aws\Result createForecast(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createForecastAsync(array $args = [])
 * @method \Aws\Result createForecastExportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createForecastExportJobAsync(array $args = [])
 * @method \Aws\Result createPredictor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPredictorAsync(array $args = [])
 * @method \Aws\Result deleteDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDatasetAsync(array $args = [])
 * @method \Aws\Result deleteDatasetGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDatasetGroupAsync(array $args = [])
 * @method \Aws\Result deleteDatasetImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDatasetImportJobAsync(array $args = [])
 * @method \Aws\Result deleteForecast(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteForecastAsync(array $args = [])
 * @method \Aws\Result deleteForecastExportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteForecastExportJobAsync(array $args = [])
 * @method \Aws\Result deletePredictor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePredictorAsync(array $args = [])
 * @method \Aws\Result describeDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDatasetAsync(array $args = [])
 * @method \Aws\Result describeDatasetGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDatasetGroupAsync(array $args = [])
 * @method \Aws\Result describeDatasetImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDatasetImportJobAsync(array $args = [])
 * @method \Aws\Result describeForecast(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeForecastAsync(array $args = [])
 * @method \Aws\Result describeForecastExportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeForecastExportJobAsync(array $args = [])
 * @method \Aws\Result describePredictor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describePredictorAsync(array $args = [])
 * @method \Aws\Result getAccuracyMetrics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAccuracyMetricsAsync(array $args = [])
 * @method \Aws\Result listDatasetGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDatasetGroupsAsync(array $args = [])
 * @method \Aws\Result listDatasetImportJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDatasetImportJobsAsync(array $args = [])
 * @method \Aws\Result listDatasets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDatasetsAsync(array $args = [])
 * @method \Aws\Result listForecastExportJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listForecastExportJobsAsync(array $args = [])
 * @method \Aws\Result listForecasts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listForecastsAsync(array $args = [])
 * @method \Aws\Result listPredictors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPredictorsAsync(array $args = [])
 * @method \Aws\Result updateDatasetGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDatasetGroupAsync(array $args = [])
 */
class ForecastServiceClient extends AwsClient {}
