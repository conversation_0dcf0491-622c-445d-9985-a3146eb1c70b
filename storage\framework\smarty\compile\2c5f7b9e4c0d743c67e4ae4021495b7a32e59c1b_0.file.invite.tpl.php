<?php
/* Smarty version 3.1.33, created on 2022-07-06 00:03:54
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/invite.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62c460ea285569_48929182',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '2c5f7b9e4c0d743c67e4ae4021495b7a32e59c1b' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/invite.tpl',
      1 => 1657037023,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:user/main.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:user/footer.tpl' => 1,
  ),
),false)) {
function content_62c460ea285569_48929182 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:user/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Novice Guide</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">邀请中心</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/shop" class="btn btn-sm btn-neutral">商店购买</a>
              <a href="/user/code" class="btn btn-sm btn-neutral">我的钱包</a>
            </div>
          </div>
		  <!--Card status-->
		  <div class="row">
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">当前邀请账户余额</h5>
                      <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['user']->value->ref_money;?>
 <small>火箭币</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-red text-white rounded-circle shadow">
                        <i class="ni ni-active-40"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
			<div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">可兑换Vip时长</h5>
                      <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['user']->value->ref_class;?>
 <small>Hours</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-green text-white rounded-circle shadow">
                        <i class="ni ni-money-coins"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">历史提现总数</h5>
					  <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['tixian_sum']->value;?>
 <small>火箭币</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-orange text-white rounded-circle shadow">
                        <i class="ni ni-chart-pie-35"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">历史获得流量</h5>
					  <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['ref_traffic_sum']->value;?>
 <small>GB</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                        <i class="ni ni-chart-bar-32"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
		  
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">邀请奖励说明</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
					<?php if ($_smarty_tpl->tpl_vars['config']->value['invite_price'] >= 0) {?>
					<button class="btn btn-primary text-white btn-sm mb-3" data-toggle="modal" data-target="#buyinvite">购买邀请次数</button>
					<?php }?>
					<?php if ($_smarty_tpl->tpl_vars['config']->value['custom_invite_price'] >= 0) {?>
					<button class="btn btn-secondary btn-sm mb-3" data-toggle="modal" data-target="#customlink">定制链接后缀</button>
					<?php }?>
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>每邀请1位用户注册:</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>你将获得<code><?php echo $_smarty_tpl->tpl_vars['config']->value["invite_gift"];?>
 G</code>流量.</p>
							<?php if ($_smarty_tpl->tpl_vars['config']->value["invite_money"] > 0) {?>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>你将获得<code><?php echo $_smarty_tpl->tpl_vars['config']->value["invite_money"];?>
 元</code>金额.</p>
							<?php }?>
							<?php if ($_smarty_tpl->tpl_vars['config']->value['invite_class'] > 0) {?>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>你将获得 <code><?php echo $_smarty_tpl->tpl_vars['config']->value['invite_class'];?>
</code> 小时Vip等级的时长累计.可兑换Vip套餐时长.</p>
							<?php }?>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>Ta充值时你可获得Ta充值金额的<code><?php echo $_smarty_tpl->tpl_vars['user']->value->payback_code;?>
 %</code>返利.</p>
							<?php if ($_smarty_tpl->tpl_vars['config']->value['invite_get_traffic'] > 0) {?>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>不使用你的邀请链接注册只有<code><?php echo $_smarty_tpl->tpl_vars['config']->value["defaultTraffic"];?>
 G</code>流量, 使用邀请链接注册可获得<code><?php echo $_smarty_tpl->tpl_vars['config']->value['invite_get_traffic'];?>
 G</code>流量.</p>
							<?php }?>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>邀请链接请发给需要的人，如：你的朋友圈子, 你的校园圈子, 你的游戏圈子等.</p>
							
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
		</div>	
	  </div>
	    <div class="row row-example">
		    <div class="col-lg-7 col-md-7">
              <div class="card">
				<div class="card-header bg-transparent">
					<h3 class="mb-0"><i class="fa fa-credit-card-alt fa-lg"></i>&nbsp;业务处理</h3>
				</div>
				<div class="card-body">
				
					<label class="form-control-label badge-dot mr-4"><i class="bg-warning"></i>当前提现账号(已经提交过的不可再次修改):</label>
					<div class="input-group mb-3">
						<div class="input-group-prepend">
							<select id="dist_accid" class="form-control form-control-sm">
								<option value="1" <?php if ($_smarty_tpl->tpl_vars['user']->value->ref_accid == 1) {?>selected<?php }?> data="dist_accid">支付宝</option>
								<option value="2" <?php if ($_smarty_tpl->tpl_vars['user']->value->ref_accid == 2) {?>selected<?php }?> data="dist_accid">微信</option>
							</select>
						</div>
						<?php if ($_smarty_tpl->tpl_vars['user']->value->ref_acc != null) {?>
						<input type="text" class="form-control form-control-sm" value="<?php echo $_smarty_tpl->tpl_vars['user']->value->ref_acc;?>
" disabled required>
						<div class="input-group-append">
							<button class="btn btn-primary btn-sm disabled" type="button">保存</button>
						</div>
						<?php } else { ?>
						<input id="ref_acc" type="text" class="form-control form-control-sm" placeholder="请填写你的账号...">
						<div class="input-group-append">
							<button class="btn btn-primary btn-sm" type="button" id="ref_acc_update">保存</button>
						</div>
						<?php }?>
					</div>
					<?php if ($_smarty_tpl->tpl_vars['user']->value->ref_acc != null) {?>
						<div class="custom-file mb-3">
							<div class="bg-secondary"><input type="text" class="form-control" value="<?php if ($_smarty_tpl->tpl_vars['user']->value->tx_image != null) {
echo $_smarty_tpl->tpl_vars['config']->value['baseUrl'];?>
/upload/invite/<?php echo $_smarty_tpl->tpl_vars['user']->value->tx_image;
}?>" disabled required></div>
						</div>
					<?php } else { ?>
						<div id="up_re" class="custom-file mb-3">
							<input type="file" class="custom-file-input" id="tx_image">
							<label id="up_result" class="custom-file-label" for="tx_image">请提交你的收款二维码...</label>
						</div>
					<?php }?>
					<?php if ($_smarty_tpl->tpl_vars['config']->value['invite_class'] > 0) {?>
					<label class="form-control-labe badge-dot mr-4"><i class="bg-warning"></i>可以兑换VIP <code><?php echo $_smarty_tpl->tpl_vars['config']->value['invite_class'];?>
</code> 等级时长(小时):</label>
					<div class="input-group mb-3">
						<input id="ref_class" type="text" class="form-control form-control-sm" value="<?php echo $_smarty_tpl->tpl_vars['user']->value->ref_class;?>
" aria-describedby="change_class" readonly disabled>
						<div class="input-group-append">
							<button class="btn btn-primary btn-sm" type="button" id="change_class">兑换</button>
						</div>
					</div>
					<?php }?>
					<label class="form-control-labe badge-dot mr-4"><i class="bg-success"></i>申请转入余额消费:</label>
					<div class="input-group mb-3">
						<input id="check_in_val" type="text" class="form-control form-control-sm" placeholder="请输入1的倍数..." aria-describedby="aff_check_in">
						<div class="input-group-append">
							<button class="btn btn-success btn-sm" type="button" id="aff_check_in">提交</button>
						</div>
					</div>

					<label class="form-control-labe badge-dot mr-4"><i class="bg-danger"></i>申请提现:</label>
					<div class="input-group mb-3">
						<input id="check_out_val" type="text" class="form-control form-control-sm" placeholder="请输入1的倍数..." aria-describedby="aff_check_out">
						<div class="input-group-append">
							<button class="btn btn-primary btn-sm" type="button" id="aff_check_out">申请</button>
						</div>
					</div>

				</div>
              </div>
            </div>
            <div class="col-lg-5 col-md-5">
			   <div class="card">
				<div class="card-header bg-transparent">
					<h3 class="mb-0"><i class="fa fa-link fa-lg"></i>&nbsp;邀请链接</h3>
				</div>
				<div class="card-body">
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>剩余可邀请次数: <code><?php echo $_smarty_tpl->tpl_vars['user']->value->invite_num;?>
</code></p>
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>发送邀请链接给有需要的人, 邀请他人注册时, 请将以下链接发给被邀请者:</p>
				
				<p class="form-group">
					<input type="text" class="form-control form-control-alternative" name="input1"  value="<?php echo $_smarty_tpl->tpl_vars['invite_link']->value;?>
" readonly disabled />
				</p>
				<p>
					<button type="button" class="copy-text btn btn-primary" data-clipboard-text="<?php echo $_smarty_tpl->tpl_vars['invite_link']->value;?>
"><i class="ni ni-ungroup ni-lg icon-ver"></i>&nbsp;点击复制</button>
					<button type="button" class="reset-link btn btn-danger float-right"><i class="fa fa-repeat fa-lg icon-ver"></i>&nbsp;重置链接</button>
				</p>
				</div>
			  </div>
            </div>
        </div><!--row-->
		
	  <div class="row">
        <div class="col">
            <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h4 class="mb-0">业务记录</h4>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive">
					<table class="table align-items-center table-flush">
					<thead class="thead-light">
						<tr>
							<th>ID</th>
							<th>类型</th>
							<th>状态</th>
							<th>账号</th>
							<th>数额</th>
							<th>时间</th>
						</tr>
					</thead>
					<tbody class="list">
				    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['tixians']->value, 'tixian');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['tixian']->value) {
?>
					<tr>
						<td>
							<span class="badge badge-dot"><i class="bg-warning"></i>#<?php echo $_smarty_tpl->tpl_vars['tixian']->value->id;?>
</span>
						</td>
						<td>
							<span class="badge badge-dot"><?php if ($_smarty_tpl->tpl_vars['tixian']->value->type == 1) {?>转入<?php } elseif ($_smarty_tpl->tpl_vars['tixian']->value->type == 0) {?>提现<?php } else { ?>兑换<?php }?></span>
						</td>
						<td>
							<span class="badge badge-dot"><i class="bg-warning"></i><?php if ($_smarty_tpl->tpl_vars['tixian']->value->ref_tx == 0) {?>申请中<?php } elseif ($_smarty_tpl->tpl_vars['tixian']->value->ref_tx == 1) {?>已通过<?php } else { ?>已拒绝<?php }?></span>
						</td>
						<td>
							<span class="badge badge-dot"><?php echo $_smarty_tpl->tpl_vars['tixian']->value->ref_acc;?>
</span>
						</td>
						<td>
							<span class="badge badge-dot"><?php echo $_smarty_tpl->tpl_vars['tixian']->value->tx_money;?>
</span>
						</td>
						<td>
							<span class="badge badge-dot"><?php echo $_smarty_tpl->tpl_vars['tixian']->value->tx_time;?>
</span>
						</td>
					</tr>
					<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
					</tbody>
					</table>
				</div>
              </div>
            </div><!--card-->
			
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h4 class="mb-0">返利记录</h4>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive">
					<table class="table align-items-center table-flush">
					<thead class="thead-light">
						<tr>
							<th>ID</th>
							<th>被邀请用户ID</th>
							<th>获得返利</th>
							<th>获得流量</th>
							<th>时间</th>
						</tr>
					</thead>
					<tbody class="list">
				    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['paybacks']->value, 'payback');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['payback']->value) {
?>
					<tr>
						<td>
							<span class="badge badge-dot"><i class="bg-warning"></i>#<?php echo $_smarty_tpl->tpl_vars['payback']->value->id;?>
</span>
						</td>
						<td>
							<span class="badge badge-dot"><i class="bg-warning"></i><?php echo $_smarty_tpl->tpl_vars['payback']->value->userid;?>
</span>
						</td>
						<td>
							<span class="badge badge-dot"><?php echo $_smarty_tpl->tpl_vars['payback']->value->ref_get;?>
 元</span>
						</td>
						<td>
							<span class="badge badge-dot"><?php echo $_smarty_tpl->tpl_vars['payback']->value->ref_traffic;?>
 GB</span>
						</td>
						<td>
							<span class="badge badge-dot"><?php echo date('Y-m-d H:i:s',$_smarty_tpl->tpl_vars['payback']->value->datetime);?>
</span>
						</td>
					</tr>
					<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
					</tbody>
					</table>
					<?php echo $_smarty_tpl->tpl_vars['paybacks']->value->render();?>

				</div>
              </div>
            </div><!--card-->
			
			
        </div>
      </div><!--row-->
	  
		<!-- buy-invite-num Modal -->
		<div class="modal fade" id="buyinvite" tabindex="-1" role="dialog" aria-labelledby="buyinviteModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="buyinviteModalLabel">购买邀请次数</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<div class="form-group mt-3">
						<label class="form-control-label">输入购买次数&nbsp;:&nbsp;</label>
						<input id="buy-invite-num" class="form-control form-control-sm" type="num">
					</div>
				</div>	 
		      </div>
			  <div class="modal-footer">
                    <button id="buy-invite" type="button" class="btn btn-primary">确认提交</button>
               </div>
		    </div>
		  </div>
		</div>
		<!-- custom-invite-link Modal -->
		<div class="modal fade" id="customlink" tabindex="-1" role="dialog" aria-labelledby="customlinkModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="customlinkModalLabel">定制链接后缀</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description mt-3">价格: <code><?php echo $_smarty_tpl->tpl_vars['config']->value['custom_invite_price'];?>
 元/次</code></p>
					<p class="description mt-3">例: 输入<code>vip</code>则链接变为<code><?php echo $_smarty_tpl->tpl_vars['config']->value["baseUrl"];?>
/auth/register?code=vip</code></p>
					<div class="form-group mt-3">
						<label class="form-control-label">输入链接后缀&nbsp;:&nbsp;</label>
						<input id="custom-invite-link" class="form-control form-control-sm" type="text">
					</div>
				</div>	 
		      </div>
			  <div class="modal-footer">
                    <button id="custom-invite-confirm" type="button" class="btn btn-default">确认提交</button>
               </div>
		    </div>
		  </div>
		</div>
		
	  <?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php $_smarty_tpl->_subTemplateRender('file:user/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php echo '<script'; ?>
>
$(".reset-link").click(function () {
	swal('重置完成','已重置您的邀请链接 ～～','success');
	window.setTimeout("location.href='/user/inviteurl_reset'", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
});
<?php echo '</script'; ?>
><?php }
}
