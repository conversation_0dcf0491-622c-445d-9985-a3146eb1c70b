{include file='admin/main.tpl'}


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Create Group</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/nodegroup">群组设置</a></li>
                  <li class="breadcrumb-item active" aria-current="page">新建群组</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/node" class="btn btn-sm btn-neutral">节点</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">新建群组 </h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">群组名称:</label>
							<input id="name" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">群组等级(仅数字):</label>
							<input id="level" class="form-control form-control-sm" type="number">
						</div>
					</div>
				<div class="modal-footer">
                    <button id="submit" type="button" class="btn btn-primary">确认提交</button>
				</div>
				</div>
			</div>
			
        </div>
      </div><!--row-->
	  
		{include file='dialog.tpl'}
	  {include file='admin/footer.tpl'}

<script>

    window.addEventListener('load', () => {
        function submit() {

            $.ajax({
                type: "POST",
                url: "/admin/nodegroup",
                dataType: "json",
                data: {
                    name: $$getValue('name'),
					level: $$getValue('level')

                },
                success: data => {
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            });
        }

		$("#submit").on("click", submit);

    });
    
</script>

