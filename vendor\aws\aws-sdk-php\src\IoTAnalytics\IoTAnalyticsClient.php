<?php
namespace Aws\IoTAnalytics;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS IoT Analytics** service.
 * @method \Aws\Result batchPutMessage(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchPutMessageAsync(array $args = [])
 * @method \Aws\Result cancelPipelineReprocessing(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelPipelineReprocessingAsync(array $args = [])
 * @method \Aws\Result createChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createChannelAsync(array $args = [])
 * @method \Aws\Result createDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDatasetAsync(array $args = [])
 * @method \Aws\Result createDatasetContent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDatasetContentAsync(array $args = [])
 * @method \Aws\Result createDatastore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDatastoreAsync(array $args = [])
 * @method \Aws\Result createPipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPipelineAsync(array $args = [])
 * @method \Aws\Result deleteChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteChannelAsync(array $args = [])
 * @method \Aws\Result deleteDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDatasetAsync(array $args = [])
 * @method \Aws\Result deleteDatasetContent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDatasetContentAsync(array $args = [])
 * @method \Aws\Result deleteDatastore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDatastoreAsync(array $args = [])
 * @method \Aws\Result deletePipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePipelineAsync(array $args = [])
 * @method \Aws\Result describeChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeChannelAsync(array $args = [])
 * @method \Aws\Result describeDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDatasetAsync(array $args = [])
 * @method \Aws\Result describeDatastore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDatastoreAsync(array $args = [])
 * @method \Aws\Result describeLoggingOptions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeLoggingOptionsAsync(array $args = [])
 * @method \Aws\Result describePipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describePipelineAsync(array $args = [])
 * @method \Aws\Result getDatasetContent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDatasetContentAsync(array $args = [])
 * @method \Aws\Result listChannels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listChannelsAsync(array $args = [])
 * @method \Aws\Result listDatasetContents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDatasetContentsAsync(array $args = [])
 * @method \Aws\Result listDatasets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDatasetsAsync(array $args = [])
 * @method \Aws\Result listDatastores(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDatastoresAsync(array $args = [])
 * @method \Aws\Result listPipelines(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPipelinesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result putLoggingOptions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putLoggingOptionsAsync(array $args = [])
 * @method \Aws\Result runPipelineActivity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise runPipelineActivityAsync(array $args = [])
 * @method \Aws\Result sampleChannelData(array $args = [])
 * @method \GuzzleHttp\Promise\Promise sampleChannelDataAsync(array $args = [])
 * @method \Aws\Result startPipelineReprocessing(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startPipelineReprocessingAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateChannelAsync(array $args = [])
 * @method \Aws\Result updateDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDatasetAsync(array $args = [])
 * @method \Aws\Result updateDatastore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDatastoreAsync(array $args = [])
 * @method \Aws\Result updatePipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updatePipelineAsync(array $args = [])
 */
class IoTAnalyticsClient extends AwsClient {}
