{include file='admin/main.tpl'}

    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Create Shop</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/shop">商品列表</a></li>
				  <li class="breadcrumb-item active">新建商品</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/user" class="btn btn-sm btn-neutral">用户</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">新建商品</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">名称:</label>
							<input id="name" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">价格:</label>
							<input id="price" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">自动续订天数:</label>
							<input id="auto_renew" class="form-control form-control-sm" type="text" value="0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>0为不允许自动续订, 其他为到了那么多天之后就会自动从用户的账户上划钱抵扣</p>
						</div>
						<div class="form-group">
							<label class="form-control-label">流量(GB):</label>
							<input id="bandwidth" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="custom-toggle icon-ver" for="auto_reset_bandwidth">
								<input id="auto_reset_bandwidth" type="checkbox">
								<span class="custom-toggle-slider rounded-circle" data-label-off="No" data-label-on="Yes"></span>
							</label>&nbsp;&nbsp;续费时自动重置用户流量为上面这个流量值
						</div>
						<div class="form-group">
							<label class="form-control-label">账户有效期天数:</label>
							<input id="expire" class="form-control form-control-sm" type="text" value="0">
						</div>
						<div class="form-group">
							<label class="form-control-label">等级:</label>
							<select id="class" class="form-control form-control-sm" name="class">
							{foreach $levelList as $level}
								<option value="{$level->level}">{$level->name}</option>
							{/foreach}	
							</select>
							<p class="description badge-dot"><i class="bg-warning"></i>你想把商品放到哪个用户等级</p>
						</div>
										
						<div class="form-group">
							<label class="form-control-label">等级有效期天数:</label>
							<input id="class_expire" class="form-control form-control-sm" type="text" value="0">
						</div>
                      	<div class="form-group">
                            <p class="form-control-label">用户群组(请确保该群组有对应的节点可用):</p>
                         {foreach $grouplist as $group}
                           <div class="checkbox-inline custom-control custom-checkbox">
								<input id="group{$group->level}" name="group" type="checkbox" value="{$group->level}" class="custom-control-input icon-ver">
								<label class="custom-control-label" for="group{$group->level}">{$group->name}</label>
					 	   </div>
                          {/foreach}
                          <p class="description badge-dot"><i class="bg-warning"></i>购买该商品将分配以上选中的随机1个群组</p>
                      	</div>
						<div class="form-group">
							<label class="form-control-label">多少天内:</label>
							<input id="reset_exp" class="form-control form-control-sm" type="text" value="0">
						</div>
						<div class="form-group">
							<label class="form-control-label">每多少天:</label>
							<input id="reset" class="form-control form-control-sm" type="text" value="0">
						</div>
						<div class="form-group">
							<label class="form-control-label">重置流量为多少G:</label>
							<input id="reset_value" class="form-control form-control-sm" type="text" value="0">
						</div>
                        <div class="form-group">
							<label class="form-control-label">端口限速(Mbps):</label>
							<input id="speedlimit" class="form-control form-control-sm" type="text" value="0">
						</div>
						<div class="form-group">
							<label class="form-control-label">IP限制(客户端数量):</label>
							<input id="connector" class="form-control form-control-sm" type="text" value="0">
						</div>
						<div class="form-group">
							<label class="form-control-label">服务支持:</label>
							<input class="form-control form-control-sm" id="content_extra" type="text">
							<p class="description badge-dot"><i class="bg-warning"></i>例:<code>check-全球节点分布;times-快速客服响应</code>, 减号左边为<a href="http://www.fontawesome.com.cn/faicons/" target="_blank">fa图标</a>代号右边为文字,以;隔开</p>
						</div>
					</div>
				</div>

			
			<div class="modal-footer">
                <button id="submit" type="button" class="btn btn-primary">确认提交</button>
			</div>
		  </div><!--card-->
        </div>
      </div><!--row-->
	  
		 {include file='dialog.tpl'}
	  {include file='admin/footer.tpl'}
<script src="/theme/czssr/main/js/bootstrap-tagsinput.min.js"></script>
<script>
    window.addEventListener('load', () => {
        function submit() {
            if ($$.getElementById('auto_reset_bandwidth').checked) {
				var auto_reset_bandwidth=1;
            } else {
				var auto_reset_bandwidth=0;
			}

            let contentExtra = $$getValue('content_extra');
			if (contentExtra === '' || contentExtra === '-') {
                contentExtra = 'check-全球节点分布;check-快速客服响应;check-全平台客户端';
			}
           function fun(){
                obj = document.getElementsByName("group");
                check_val = [];
               for(k in obj){
                   if(obj[k].checked)
                     check_val.push(obj[k].value);
                     
               }
             let strNew=check_val.join('|');
             return strNew; 
           }
            $.ajax({
                type: "POST",
                url: "/admin/shop",
                dataType: "json",
                data: {
                    name: $$getValue('name'),
                    auto_reset_bandwidth,
                    price: $$getValue('price'),
                    auto_renew: $$getValue('auto_renew'),
                    bandwidth: $$getValue('bandwidth'),
                    speedlimit: $$getValue('speedlimit'),
                    connector: $$getValue('connector'),
                    expire: $$getValue('expire'),
                    class: $$getValue('class'),
                    class_expire: $$getValue('class_expire'),
                    group: fun(),
                    reset: $$getValue('reset'),
                    reset_value: $$getValue('reset_value'),
                    reset_exp: $$getValue('reset_exp'),
                    content_extra: contentExtra,
                },
                success: data => {
                    if (data.ret) {
                        $("#result").modal();
                         $('#msg').html(data.msg);
                        window.setTimeout("location.href='/admin/shop'", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                         $('#msg').html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $('#msg').html("发生错误："+jqXHR.status);
                }
            });
        }

         $("#submit").on("click", submit);

    })
</script>