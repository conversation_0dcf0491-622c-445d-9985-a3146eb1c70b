<?php
/* Smarty version 3.1.33, created on 2022-02-12 18:22:44
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/password/token.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62078a74689c46_77694763',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '50fbdda364d8691f5621d2c336eedc6e366e346d' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/password/token.tpl',
      1 => 1575720198,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.tpl' => 1,
    'file:footer.tpl' => 1,
  ),
),false)) {
function content_62078a74689c46_77694763 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:header.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<body>
  <header class="header-global">
    <nav id="navbar-main" class="navbar navbar-main navbar-expand-lg navbar-transparent navbar-light headroom">
      <div class="container">
        <a class="navbar-brand mr-lg-5" href="/">
          <img src="/theme/czssr/main/picture/white.png" alt="brand">
          <span class="engname1"> <?php echo $_smarty_tpl->tpl_vars['config']->value['engname'];?>
</span>
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar_global" aria-controls="navbar_global" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button><!--菜单button-->
        <div class="navbar-collapse collapse" id="navbar_global">
          <div class="navbar-collapse-header">
            <div class="row">
              <div class="col-6 collapse-brand">
                <a href="/">
                  <img src="/theme/czssr/main/picture/blue.png" alt="brand">
                  <span class="engname3"> <?php echo $_smarty_tpl->tpl_vars['config']->value['engname'];?>
</span>
                </a>
              </div>
              <div class="col-6 collapse-close"><!--关闭button-->
                <button type="button" class="navbar-toggler" data-toggle="collapse" data-target="#navbar_global" aria-controls="navbar_global" aria-expanded="false" aria-label="Toggle navigation">
                  <span></span>
                  <span></span>
                </button>
              </div>
            </div>
          </div>
          <ul class="navbar-nav navbar-nav-hover align-items-lg-center">
				<li class="nav-item ">
				<?php if ($_smarty_tpl->tpl_vars['user']->value->isLogin) {?>
				<a href="/user" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-sign-in"></i>
					<span class="nav-link-inner--text">My Account</span>
				</a>
				<?php } else { ?>
				<a href="/auth/login" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-sign-in"></i>
					<span class="nav-link-inner--text">Login</span>
				</a>
				<?php }?>
				</li>
				<li class="nav-item ">
				<?php if ($_smarty_tpl->tpl_vars['config']->value['register_mode'] != 'close') {?>
				<a href="/auth/register" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-user-plus"></i>
					<span class="nav-link-inner--text">Sign Up</span>
				</a>
				<?php } else { ?>
				<a href="javascript:void(0);" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-user-plus"></i>
					<span class="nav-link-inner--text">Stop register</span>
				</a>
				<?php }?>
				</li>
				<li class="nav-item ">
				<a href="/password/reset" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-repeat"></i>
					<span class="nav-link-inner--text">Reset Password</span>
				</a>
				</li>
				<li class="nav-item ">
				<a href="/doc" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-question-circle"></i>
					<span class="nav-link-inner--text">Support</span>
				</a>
				</li>
           </ul>
            <ul class="navbar-nav align-items-lg-center ml-lg-auto">
				<li class="nav-item">
				<!--<a class="nav-link nav-link-icon" href="#" target="_blank" data-toggle="tooltip" title="Star us on Telegram">
					<i class="fa fa-telegram"></i>
					<span class="nav-link-inner--text d-lg-none">Telegram</span>
				</a>-->
				
				</li>
            </ul>
        </div>
      </div>
    </nav>
  </header>
  <main>
    <section class="section section-shaped section-lg" style="min-height: calc(100vh);">
      <div class="shape shape-style-1 bg-gradient-default">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
      <div class="container pt-lg-md">
        <div class="row justify-content-center">
          <div class="col-lg-5">
            <div class="card bg-secondary shadow border-0">
              <div class="card-header bg-white">
                <div class="text-muted text-center mb-3"><small>Reset Your Password</small></div>
               </div>
              <div class="card-body px-lg-5 py-lg-5">
                <form action="javascript:void(0);" method="POST">
                  <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                      <div class="input-group-prepend">
                        <span class="input-group-text"><i class="ni ni-lock-circle-open"></i></span>
                      </div>
                      <input class="form-control" placeholder="Passwd" type="password" id="passwd">
                    </div>
                  </div>
                  <div class="form-group">
					<div class="input-group input-group-alternative">
					<div class="input-group-prepend">
						<span class="input-group-text"><i class="ni ni-lock-circle-open"></i></span>
					</div>
					<input class="form-control" placeholder="REpassword" type="password" id="repasswd">
					</div>
				  </div>

                  <div class="form-group mb-3">
                   
                   <span class="mb-0 font-sm"><i class="fa fa-exclamation-triangle fa-lg"></i>&nbsp;请注意保管好你的密码.</span>

                  </div>
                  <div class="text-center">
                    <button type="button" class="btn btn-primary my-4" id="reset">Reset</button>
                  </div>
                </form>
				
              </div>
            </div>

          </div>
        </div>
      </div>
    </section>
  </main>
  <!-- Footer -->
<?php $_smarty_tpl->_subTemplateRender('file:footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
echo '<script'; ?>
>

    $(document).ready(function(){
        function reset(){
		
			if (($("#passwd").val() == "") || ($("#repasswd").val() == "")) {
			     swal('Oops...','密码不能为空','error');
			    return;
			}

            $.ajax({
                type:"POST",
                url:"/password/token/<?php echo $_smarty_tpl->tpl_vars['token']->value;?>
",
                dataType:"json",
                data:{
                    password: $("#passwd").val(),
                    repasswd: $("#repasswd").val(),
                },
                success:function(data){
                    if(data.ret){
                      swal({
                        type: "success",
                        title: data.msg,
                        button: "登录"
                      });
                      window.setTimeout("location.href='/auth/login'", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                    }else{
                       swal({
                          type: "error",
                          title: data.msg,
                          button: "知道了"
                        });
                    }
                },
                error:function(jqXHR){
                   swal({
                          type: "error",
                          title: "发生错误："+jqXHR.status,
                        });
                   
                    // 在控制台输出错误信息
                    console.log(removeHTMLTag(jqXHR.responseText));
                }
            });
        }
        $("html").keydown(function(event){
            if(event.keyCode==13){
                reset();
            }
        });
        $("#reset").click(function(){
            reset();
        });
    })
<?php echo '</script'; ?>
>

  <?php }
}
