{include file='user/main.tpl'}
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Transshipment</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
				  <li class="breadcrumb-item"><a href="/user/relay">中转规则</a></li>
                  <li class="breadcrumb-item active" aria-current="page">添加规则</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/ticket/create" class="btn btn-sm btn-neutral">工单</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h4 class="mb-0">添加规则</h4>
              </div>
              
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<p class="description mt-3">起源节点&nbsp;:&nbsp;</p>
						<select id="source_node" class="form-control form-control-sm">
							
							<option value="-1">请选择节点</option>
							
						{foreach $source_nodes as $source_node}
							<option value="{$source_node->id}">{$source_node->name}</option>
						{/foreach}	
						</select>
						<p class="description mt-3">目标节点&nbsp;:&nbsp;</p>
						<select id="dist_node" class="form-control form-control-sm">
							<option value="-1">不进行中转</option>
						{foreach $dist_nodes as $dist_node}
							<option value="{$dist_node->id}">{$dist_node->name}</option>
						{/foreach}	
						</select>
						<p class="description mt-3">端口选择&nbsp;:&nbsp;</p>
						<select id="port" class="form-control form-control-sm">
						{foreach $ports as $port}
							<option value="{$port}">{$port}</option>
						{/foreach}	
						</select>
						<div class="form-group mt-3">
							<label class="form-control-label">优先级&nbsp;:&nbsp;</label>
							<input id="priority" class="form-control form-control-sm" type="text" value="0">
						</div>
					</div>
					<div class="modal-footer">
						<button id="create_button" type="button" class="btn btn-primary">确认添加</button>
					</div>
				</div>
			 
			</div>
		</div>
	  </div><!--row-->
	  
	  {include file='dialog.tpl'}
	  {include file='user/footer.tpl'}
	  
<script>

   function validateNumber(value){
    var pattern = /^[0-9]\d*$/;
	if (pattern.test(value)) {
      return true; 
    }else{
      return false;
    }
  }  
 $(document).ready(() => {

     function create_button() {
          	
		if (!validateNumber($("#priority").val())) {
		    $("#result").modal();
			$("#msg").html("优先级输入不合法.")
			return;
		}
        if ($("#source_node").val() == "-1" || $("#dist_node").val() == "-1") {
       		$("#result").modal();
			$("#msg").html("起源节点或者目标节点设置错误.")
			return;
        }
            $.ajax({
                type: "POST",
                url: "/user/relay",
                dataType: "json",

                data: {
                    source_node: $('#source_node').val(),
                    dist_node: $('#dist_node').val(),
                    port: $('#port').val(),
                    priority: $('#priority').val()

                },
                success: function(data) {
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);

                        window.setTimeout("location.href=top.document.referrer", {$config['jump_delay']});

                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: function(jqXHR) {
                    $("#result").modal();
                    $("#msg").html(jqXHR+"  发生了错误。");
                }
            });
        }
		$("#create_button").click(() => {
            create_button();
        });
    });

</script>
	  