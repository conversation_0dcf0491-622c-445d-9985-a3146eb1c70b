{include file='user/main.tpl'}
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Audit rules</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">审计规则</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/ticket/create" class="btn btn-sm btn-neutral">工单</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">审计规则公示</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
                      <a class="btn btn-Secondary btn-sm mb-3" href="/user/detect/log">记录表查看</a>
						<blockquote class="blockquote mb-0">
							<p class="description">为了爱与和平, 也同时为了系统的正常运行, 特制定了如下过滤规则, 当您使用节点执行这些动作时. 您的通信就会被截断.</p>
							<p class="description">关于隐私: 我们仅用以下规则进行实时匹配和记录匹配到的规则, 您的通信方向和通信内容我们不会做任何记录, 请您放心. 也请您理解我们对于这些不当行为的管理, 谢谢.</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
			
            <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">审计表</h3>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive">
				{$rules->render()}
					<table class="table align-items-center table-flush">
					<thead class="thead-light">
						<tr>
							<th>ID</th>
                            <th>名称</th>
                            <th>描述</th>
                            <th>正则表达式</th>
                            <th>类型</th>
						</tr>
					</thead>
					<tbody class="list">
				     {foreach $rules as $rule}
					<tr>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>#{$rule->id}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$rule->name}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$rule->text}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$rule->regex}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{if $rule->type == 1}数据包明文匹配{else}数据包 hex 匹配{/if}</span>
						</td>
					</tr>
					{/foreach}
					</tbody>
					</table>
				 {$rules->render()}
				</div>
              </div>
            </div><!--card-->
		
        </div>
      </div><!--row-->
	  

	  {include file='user/footer.tpl'}
	  