<?php
// This file was auto-generated from sdk-root/src/data/sms/2016-10-24/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-10-24', 'endpointPrefix' => 'sms', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'SMS', 'serviceFullName' => 'AWS Server Migration Service', 'serviceId' => 'SMS', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSServerMigrationService_V2016_10_24', 'uid' => 'sms-2016-10-24', ], 'operations' => [ 'CreateApp' => [ 'name' => 'CreateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAppRequest', ], 'output' => [ 'shape' => 'CreateAppResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'CreateReplicationJob' => [ 'name' => 'CreateReplicationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateReplicationJobRequest', ], 'output' => [ 'shape' => 'CreateReplicationJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ServerCannotBeReplicatedException', ], [ 'shape' => 'ReplicationJobAlreadyExistsException', ], [ 'shape' => 'NoConnectorsAvailableException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'TemporarilyUnavailableException', ], ], ], 'DeleteApp' => [ 'name' => 'DeleteApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAppRequest', ], 'output' => [ 'shape' => 'DeleteAppResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'DeleteAppLaunchConfiguration' => [ 'name' => 'DeleteAppLaunchConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAppLaunchConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteAppLaunchConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'DeleteAppReplicationConfiguration' => [ 'name' => 'DeleteAppReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAppReplicationConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteAppReplicationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'DeleteReplicationJob' => [ 'name' => 'DeleteReplicationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReplicationJobRequest', ], 'output' => [ 'shape' => 'DeleteReplicationJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ReplicationJobNotFoundException', ], ], ], 'DeleteServerCatalog' => [ 'name' => 'DeleteServerCatalog', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteServerCatalogRequest', ], 'output' => [ 'shape' => 'DeleteServerCatalogResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], ], ], 'DisassociateConnector' => [ 'name' => 'DisassociateConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateConnectorRequest', ], 'output' => [ 'shape' => 'DisassociateConnectorResponse', ], 'errors' => [ [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'GenerateChangeSet' => [ 'name' => 'GenerateChangeSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GenerateChangeSetRequest', ], 'output' => [ 'shape' => 'GenerateChangeSetResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'GenerateTemplate' => [ 'name' => 'GenerateTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GenerateTemplateRequest', ], 'output' => [ 'shape' => 'GenerateTemplateResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'GetApp' => [ 'name' => 'GetApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAppRequest', ], 'output' => [ 'shape' => 'GetAppResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'GetAppLaunchConfiguration' => [ 'name' => 'GetAppLaunchConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAppLaunchConfigurationRequest', ], 'output' => [ 'shape' => 'GetAppLaunchConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'GetAppReplicationConfiguration' => [ 'name' => 'GetAppReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAppReplicationConfigurationRequest', ], 'output' => [ 'shape' => 'GetAppReplicationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'GetConnectors' => [ 'name' => 'GetConnectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetConnectorsRequest', ], 'output' => [ 'shape' => 'GetConnectorsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], ], ], 'GetReplicationJobs' => [ 'name' => 'GetReplicationJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetReplicationJobsRequest', ], 'output' => [ 'shape' => 'GetReplicationJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'UnauthorizedOperationException', ], ], ], 'GetReplicationRuns' => [ 'name' => 'GetReplicationRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetReplicationRunsRequest', ], 'output' => [ 'shape' => 'GetReplicationRunsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'UnauthorizedOperationException', ], ], ], 'GetServers' => [ 'name' => 'GetServers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetServersRequest', ], 'output' => [ 'shape' => 'GetServersResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], ], ], 'ImportServerCatalog' => [ 'name' => 'ImportServerCatalog', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportServerCatalogRequest', ], 'output' => [ 'shape' => 'ImportServerCatalogResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'NoConnectorsAvailableException', ], ], ], 'LaunchApp' => [ 'name' => 'LaunchApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'LaunchAppRequest', ], 'output' => [ 'shape' => 'LaunchAppResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'ListApps' => [ 'name' => 'ListApps', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAppsRequest', ], 'output' => [ 'shape' => 'ListAppsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'PutAppLaunchConfiguration' => [ 'name' => 'PutAppLaunchConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutAppLaunchConfigurationRequest', ], 'output' => [ 'shape' => 'PutAppLaunchConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'PutAppReplicationConfiguration' => [ 'name' => 'PutAppReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutAppReplicationConfigurationRequest', ], 'output' => [ 'shape' => 'PutAppReplicationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'StartAppReplication' => [ 'name' => 'StartAppReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartAppReplicationRequest', ], 'output' => [ 'shape' => 'StartAppReplicationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'StartOnDemandReplicationRun' => [ 'name' => 'StartOnDemandReplicationRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartOnDemandReplicationRunRequest', ], 'output' => [ 'shape' => 'StartOnDemandReplicationRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ReplicationRunLimitExceededException', ], ], ], 'StopAppReplication' => [ 'name' => 'StopAppReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopAppReplicationRequest', ], 'output' => [ 'shape' => 'StopAppReplicationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'TerminateApp' => [ 'name' => 'TerminateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TerminateAppRequest', ], 'output' => [ 'shape' => 'TerminateAppResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'UpdateApp' => [ 'name' => 'UpdateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAppRequest', ], 'output' => [ 'shape' => 'UpdateAppResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'UpdateReplicationJob' => [ 'name' => 'UpdateReplicationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateReplicationJobRequest', ], 'output' => [ 'shape' => 'UpdateReplicationJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'ServerCannotBeReplicatedException', ], [ 'shape' => 'ReplicationJobNotFoundException', ], [ 'shape' => 'InternalError', ], [ 'shape' => 'TemporarilyUnavailableException', ], ], ], ], 'shapes' => [ 'AmiId' => [ 'type' => 'string', ], 'AppDescription' => [ 'type' => 'string', ], 'AppId' => [ 'type' => 'string', ], 'AppIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppId', ], ], 'AppLaunchStatus' => [ 'type' => 'string', 'enum' => [ 'READY_FOR_CONFIGURATION', 'CONFIGURATION_IN_PROGRESS', 'CONFIGURATION_INVALID', 'READY_FOR_LAUNCH', 'VALIDATION_IN_PROGRESS', 'LAUNCH_PENDING', 'LAUNCH_IN_PROGRESS', 'LAUNCHED', 'DELTA_LAUNCH_IN_PROGRESS', 'DELTA_LAUNCH_FAILED', 'LAUNCH_FAILED', 'TERMINATE_IN_PROGRESS', 'TERMINATE_FAILED', 'TERMINATED', ], ], 'AppLaunchStatusMessage' => [ 'type' => 'string', ], 'AppName' => [ 'type' => 'string', ], 'AppReplicationStatus' => [ 'type' => 'string', 'enum' => [ 'READY_FOR_CONFIGURATION', 'CONFIGURATION_IN_PROGRESS', 'CONFIGURATION_INVALID', 'READY_FOR_REPLICATION', 'VALIDATION_IN_PROGRESS', 'REPLICATION_PENDING', 'REPLICATION_IN_PROGRESS', 'REPLICATED', 'DELTA_REPLICATION_IN_PROGRESS', 'DELTA_REPLICATED', 'DELTA_REPLICATION_FAILED', 'REPLICATION_FAILED', 'REPLICATION_STOPPING', 'REPLICATION_STOP_FAILED', 'REPLICATION_STOPPED', ], ], 'AppReplicationStatusMessage' => [ 'type' => 'string', ], 'AppStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'DELETING', 'DELETED', 'DELETE_FAILED', ], ], 'AppStatusMessage' => [ 'type' => 'string', ], 'AppSummary' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], 'name' => [ 'shape' => 'AppName', ], 'description' => [ 'shape' => 'AppDescription', ], 'status' => [ 'shape' => 'AppStatus', ], 'statusMessage' => [ 'shape' => 'AppStatusMessage', ], 'replicationStatus' => [ 'shape' => 'AppReplicationStatus', ], 'replicationStatusMessage' => [ 'shape' => 'AppReplicationStatusMessage', ], 'latestReplicationTime' => [ 'shape' => 'Timestamp', ], 'launchStatus' => [ 'shape' => 'AppLaunchStatus', ], 'launchStatusMessage' => [ 'shape' => 'AppLaunchStatusMessage', ], 'launchDetails' => [ 'shape' => 'LaunchDetails', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastModified' => [ 'shape' => 'Timestamp', ], 'roleName' => [ 'shape' => 'RoleName', ], 'totalServerGroups' => [ 'shape' => 'TotalServerGroups', ], 'totalServers' => [ 'shape' => 'TotalServers', ], ], ], 'Apps' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppSummary', ], ], 'AssociatePublicIpAddress' => [ 'type' => 'boolean', ], 'BucketName' => [ 'type' => 'string', ], 'ClientToken' => [ 'type' => 'string', ], 'Connector' => [ 'type' => 'structure', 'members' => [ 'connectorId' => [ 'shape' => 'ConnectorId', ], 'version' => [ 'shape' => 'ConnectorVersion', ], 'status' => [ 'shape' => 'ConnectorStatus', ], 'capabilityList' => [ 'shape' => 'ConnectorCapabilityList', ], 'vmManagerName' => [ 'shape' => 'VmManagerName', ], 'vmManagerType' => [ 'shape' => 'VmManagerType', ], 'vmManagerId' => [ 'shape' => 'VmManagerId', ], 'ipAddress' => [ 'shape' => 'IpAddress', ], 'macAddress' => [ 'shape' => 'MacAddress', ], 'associatedOn' => [ 'shape' => 'Timestamp', ], ], ], 'ConnectorCapability' => [ 'type' => 'string', 'enum' => [ 'VSPHERE', 'SCVMM', 'HYPERV-MANAGER', 'SNAPSHOT_BATCHING', ], ], 'ConnectorCapabilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorCapability', ], ], 'ConnectorId' => [ 'type' => 'string', ], 'ConnectorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Connector', ], ], 'ConnectorStatus' => [ 'type' => 'string', 'enum' => [ 'HEALTHY', 'UNHEALTHY', ], ], 'ConnectorVersion' => [ 'type' => 'string', ], 'CreateAppRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AppName', ], 'description' => [ 'shape' => 'AppDescription', ], 'roleName' => [ 'shape' => 'RoleName', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'serverGroups' => [ 'shape' => 'ServerGroups', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateAppResponse' => [ 'type' => 'structure', 'members' => [ 'appSummary' => [ 'shape' => 'AppSummary', ], 'serverGroups' => [ 'shape' => 'ServerGroups', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateReplicationJobRequest' => [ 'type' => 'structure', 'required' => [ 'serverId', 'seedReplicationTime', ], 'members' => [ 'serverId' => [ 'shape' => 'ServerId', ], 'seedReplicationTime' => [ 'shape' => 'Timestamp', ], 'frequency' => [ 'shape' => 'Frequency', ], 'runOnce' => [ 'shape' => 'RunOnce', ], 'licenseType' => [ 'shape' => 'LicenseType', ], 'roleName' => [ 'shape' => 'RoleName', ], 'description' => [ 'shape' => 'Description', ], 'numberOfRecentAmisToKeep' => [ 'shape' => 'NumberOfRecentAmisToKeep', ], 'encrypted' => [ 'shape' => 'Encrypted', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'CreateReplicationJobResponse' => [ 'type' => 'structure', 'members' => [ 'replicationJobId' => [ 'shape' => 'ReplicationJobId', ], ], ], 'DeleteAppLaunchConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], ], ], 'DeleteAppLaunchConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAppReplicationConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], ], ], 'DeleteAppReplicationConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAppRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], 'forceStopAppReplication' => [ 'shape' => 'ForceStopAppReplication', ], 'forceTerminateApp' => [ 'shape' => 'ForceTerminateApp', ], ], ], 'DeleteAppResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteReplicationJobRequest' => [ 'type' => 'structure', 'required' => [ 'replicationJobId', ], 'members' => [ 'replicationJobId' => [ 'shape' => 'ReplicationJobId', ], ], ], 'DeleteReplicationJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteServerCatalogRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteServerCatalogResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', ], 'DisassociateConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'connectorId', ], 'members' => [ 'connectorId' => [ 'shape' => 'ConnectorId', ], ], ], 'DisassociateConnectorResponse' => [ 'type' => 'structure', 'members' => [], ], 'EC2KeyName' => [ 'type' => 'string', ], 'Encrypted' => [ 'type' => 'boolean', ], 'ErrorMessage' => [ 'type' => 'string', ], 'ForceStopAppReplication' => [ 'type' => 'boolean', ], 'ForceTerminateApp' => [ 'type' => 'boolean', ], 'Frequency' => [ 'type' => 'integer', ], 'GenerateChangeSetRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], 'changesetFormat' => [ 'shape' => 'OutputFormat', ], ], ], 'GenerateChangeSetResponse' => [ 'type' => 'structure', 'members' => [ 's3Location' => [ 'shape' => 'S3Location', ], ], ], 'GenerateTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], 'templateFormat' => [ 'shape' => 'OutputFormat', ], ], ], 'GenerateTemplateResponse' => [ 'type' => 'structure', 'members' => [ 's3Location' => [ 'shape' => 'S3Location', ], ], ], 'GetAppLaunchConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], ], ], 'GetAppLaunchConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], 'roleName' => [ 'shape' => 'RoleName', ], 'serverGroupLaunchConfigurations' => [ 'shape' => 'ServerGroupLaunchConfigurations', ], ], ], 'GetAppReplicationConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], ], ], 'GetAppReplicationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'serverGroupReplicationConfigurations' => [ 'shape' => 'ServerGroupReplicationConfigurations', ], ], ], 'GetAppRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], ], ], 'GetAppResponse' => [ 'type' => 'structure', 'members' => [ 'appSummary' => [ 'shape' => 'AppSummary', ], 'serverGroups' => [ 'shape' => 'ServerGroups', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'GetConnectorsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetConnectorsResponse' => [ 'type' => 'structure', 'members' => [ 'connectorList' => [ 'shape' => 'ConnectorList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetReplicationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'replicationJobId' => [ 'shape' => 'ReplicationJobId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetReplicationJobsResponse' => [ 'type' => 'structure', 'members' => [ 'replicationJobList' => [ 'shape' => 'ReplicationJobList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetReplicationRunsRequest' => [ 'type' => 'structure', 'required' => [ 'replicationJobId', ], 'members' => [ 'replicationJobId' => [ 'shape' => 'ReplicationJobId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetReplicationRunsResponse' => [ 'type' => 'structure', 'members' => [ 'replicationJob' => [ 'shape' => 'ReplicationJob', ], 'replicationRunList' => [ 'shape' => 'ReplicationRunList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetServersRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'vmServerAddressList' => [ 'shape' => 'VmServerAddressList', ], ], ], 'GetServersResponse' => [ 'type' => 'structure', 'members' => [ 'lastModifiedOn' => [ 'shape' => 'Timestamp', ], 'serverCatalogStatus' => [ 'shape' => 'ServerCatalogStatus', ], 'serverList' => [ 'shape' => 'ServerList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ImportServerCatalogRequest' => [ 'type' => 'structure', 'members' => [], ], 'ImportServerCatalogResponse' => [ 'type' => 'structure', 'members' => [], ], 'InstanceType' => [ 'type' => 'string', ], 'InternalError' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'IpAddress' => [ 'type' => 'string', ], 'KeyName' => [ 'type' => 'string', ], 'KmsKeyId' => [ 'type' => 'string', ], 'LaunchAppRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], ], ], 'LaunchAppResponse' => [ 'type' => 'structure', 'members' => [], ], 'LaunchDetails' => [ 'type' => 'structure', 'members' => [ 'latestLaunchTime' => [ 'shape' => 'Timestamp', ], 'stackName' => [ 'shape' => 'StackName', ], 'stackId' => [ 'shape' => 'StackId', ], ], ], 'LaunchOrder' => [ 'type' => 'integer', ], 'LicenseType' => [ 'type' => 'string', 'enum' => [ 'AWS', 'BYOL', ], ], 'ListAppsRequest' => [ 'type' => 'structure', 'members' => [ 'appIds' => [ 'shape' => 'AppIds', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListAppsResponse' => [ 'type' => 'structure', 'members' => [ 'apps' => [ 'shape' => 'Apps', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'LogicalId' => [ 'type' => 'string', ], 'MacAddress' => [ 'type' => 'string', ], 'MaxResults' => [ 'type' => 'integer', ], 'MissingRequiredParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'NextToken' => [ 'type' => 'string', ], 'NoConnectorsAvailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'NumberOfRecentAmisToKeep' => [ 'type' => 'integer', ], 'OperationNotPermittedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'OutputFormat' => [ 'type' => 'string', 'enum' => [ 'JSON', 'YAML', ], ], 'PutAppLaunchConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], 'roleName' => [ 'shape' => 'RoleName', ], 'serverGroupLaunchConfigurations' => [ 'shape' => 'ServerGroupLaunchConfigurations', ], ], ], 'PutAppLaunchConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutAppReplicationConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], 'serverGroupReplicationConfigurations' => [ 'shape' => 'ServerGroupReplicationConfigurations', ], ], ], 'PutAppReplicationConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'ReplicationJob' => [ 'type' => 'structure', 'members' => [ 'replicationJobId' => [ 'shape' => 'ReplicationJobId', ], 'serverId' => [ 'shape' => 'ServerId', ], 'serverType' => [ 'shape' => 'ServerType', ], 'vmServer' => [ 'shape' => 'VmServer', ], 'seedReplicationTime' => [ 'shape' => 'Timestamp', ], 'frequency' => [ 'shape' => 'Frequency', ], 'runOnce' => [ 'shape' => 'RunOnce', ], 'nextReplicationRunStartTime' => [ 'shape' => 'Timestamp', ], 'licenseType' => [ 'shape' => 'LicenseType', ], 'roleName' => [ 'shape' => 'RoleName', ], 'latestAmiId' => [ 'shape' => 'AmiId', ], 'state' => [ 'shape' => 'ReplicationJobState', ], 'statusMessage' => [ 'shape' => 'ReplicationJobStatusMessage', ], 'description' => [ 'shape' => 'Description', ], 'numberOfRecentAmisToKeep' => [ 'shape' => 'NumberOfRecentAmisToKeep', ], 'encrypted' => [ 'shape' => 'Encrypted', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'replicationRunList' => [ 'shape' => 'ReplicationRunList', ], ], ], 'ReplicationJobAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ReplicationJobId' => [ 'type' => 'string', ], 'ReplicationJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationJob', ], ], 'ReplicationJobNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ReplicationJobState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACTIVE', 'FAILED', 'DELETING', 'DELETED', 'COMPLETED', 'PAUSED_ON_FAILURE', 'FAILING', ], ], 'ReplicationJobStatusMessage' => [ 'type' => 'string', ], 'ReplicationJobTerminated' => [ 'type' => 'boolean', ], 'ReplicationRun' => [ 'type' => 'structure', 'members' => [ 'replicationRunId' => [ 'shape' => 'ReplicationRunId', ], 'state' => [ 'shape' => 'ReplicationRunState', ], 'type' => [ 'shape' => 'ReplicationRunType', ], 'stageDetails' => [ 'shape' => 'ReplicationRunStageDetails', ], 'statusMessage' => [ 'shape' => 'ReplicationRunStatusMessage', ], 'amiId' => [ 'shape' => 'AmiId', ], 'scheduledStartTime' => [ 'shape' => 'Timestamp', ], 'completedTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'encrypted' => [ 'shape' => 'Encrypted', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'ReplicationRunId' => [ 'type' => 'string', ], 'ReplicationRunLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ReplicationRunList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationRun', ], ], 'ReplicationRunStage' => [ 'type' => 'string', ], 'ReplicationRunStageDetails' => [ 'type' => 'structure', 'members' => [ 'stage' => [ 'shape' => 'ReplicationRunStage', ], 'stageProgress' => [ 'shape' => 'ReplicationRunStageProgress', ], ], ], 'ReplicationRunStageProgress' => [ 'type' => 'string', ], 'ReplicationRunState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'MISSED', 'ACTIVE', 'FAILED', 'COMPLETED', 'DELETING', 'DELETED', ], ], 'ReplicationRunStatusMessage' => [ 'type' => 'string', ], 'ReplicationRunType' => [ 'type' => 'string', 'enum' => [ 'ON_DEMAND', 'AUTOMATIC', ], ], 'RoleName' => [ 'type' => 'string', ], 'RunOnce' => [ 'type' => 'boolean', ], 'S3Location' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'BucketName', ], 'key' => [ 'shape' => 'KeyName', ], ], ], 'SecurityGroup' => [ 'type' => 'string', ], 'Server' => [ 'type' => 'structure', 'members' => [ 'serverId' => [ 'shape' => 'ServerId', ], 'serverType' => [ 'shape' => 'ServerType', ], 'vmServer' => [ 'shape' => 'VmServer', ], 'replicationJobId' => [ 'shape' => 'ReplicationJobId', ], 'replicationJobTerminated' => [ 'shape' => 'ReplicationJobTerminated', ], ], ], 'ServerCannotBeReplicatedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ServerCatalogStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_IMPORTED', 'IMPORTING', 'AVAILABLE', 'DELETED', 'EXPIRED', ], ], 'ServerGroup' => [ 'type' => 'structure', 'members' => [ 'serverGroupId' => [ 'shape' => 'ServerGroupId', ], 'name' => [ 'shape' => 'ServerGroupName', ], 'serverList' => [ 'shape' => 'ServerList', ], ], ], 'ServerGroupId' => [ 'type' => 'string', ], 'ServerGroupLaunchConfiguration' => [ 'type' => 'structure', 'members' => [ 'serverGroupId' => [ 'shape' => 'ServerGroupId', ], 'launchOrder' => [ 'shape' => 'LaunchOrder', ], 'serverLaunchConfigurations' => [ 'shape' => 'ServerLaunchConfigurations', ], ], ], 'ServerGroupLaunchConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServerGroupLaunchConfiguration', ], ], 'ServerGroupName' => [ 'type' => 'string', ], 'ServerGroupReplicationConfiguration' => [ 'type' => 'structure', 'members' => [ 'serverGroupId' => [ 'shape' => 'ServerGroupId', ], 'serverReplicationConfigurations' => [ 'shape' => 'ServerReplicationConfigurations', ], ], ], 'ServerGroupReplicationConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServerGroupReplicationConfiguration', ], ], 'ServerGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServerGroup', ], ], 'ServerId' => [ 'type' => 'string', ], 'ServerLaunchConfiguration' => [ 'type' => 'structure', 'members' => [ 'server' => [ 'shape' => 'Server', ], 'logicalId' => [ 'shape' => 'LogicalId', ], 'vpc' => [ 'shape' => 'VPC', ], 'subnet' => [ 'shape' => 'Subnet', ], 'securityGroup' => [ 'shape' => 'SecurityGroup', ], 'ec2KeyName' => [ 'shape' => 'EC2KeyName', ], 'userData' => [ 'shape' => 'UserData', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'associatePublicIpAddress' => [ 'shape' => 'AssociatePublicIpAddress', ], ], ], 'ServerLaunchConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServerLaunchConfiguration', ], ], 'ServerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Server', ], ], 'ServerReplicationConfiguration' => [ 'type' => 'structure', 'members' => [ 'server' => [ 'shape' => 'Server', ], 'serverReplicationParameters' => [ 'shape' => 'ServerReplicationParameters', ], ], ], 'ServerReplicationConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServerReplicationConfiguration', ], ], 'ServerReplicationParameters' => [ 'type' => 'structure', 'members' => [ 'seedTime' => [ 'shape' => 'Timestamp', ], 'frequency' => [ 'shape' => 'Frequency', ], 'runOnce' => [ 'shape' => 'RunOnce', ], 'licenseType' => [ 'shape' => 'LicenseType', ], 'numberOfRecentAmisToKeep' => [ 'shape' => 'NumberOfRecentAmisToKeep', ], 'encrypted' => [ 'shape' => 'Encrypted', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'ServerType' => [ 'type' => 'string', 'enum' => [ 'VIRTUAL_MACHINE', ], ], 'StackId' => [ 'type' => 'string', ], 'StackName' => [ 'type' => 'string', ], 'StartAppReplicationRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], ], ], 'StartAppReplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartOnDemandReplicationRunRequest' => [ 'type' => 'structure', 'required' => [ 'replicationJobId', ], 'members' => [ 'replicationJobId' => [ 'shape' => 'ReplicationJobId', ], 'description' => [ 'shape' => 'Description', ], ], ], 'StartOnDemandReplicationRunResponse' => [ 'type' => 'structure', 'members' => [ 'replicationRunId' => [ 'shape' => 'ReplicationRunId', ], ], ], 'StopAppReplicationRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], ], ], 'StopAppReplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'Subnet' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', ], 'TagValue' => [ 'type' => 'string', ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TemporarilyUnavailableException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, 'fault' => true, ], 'TerminateAppRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], ], ], 'TerminateAppResponse' => [ 'type' => 'structure', 'members' => [], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TotalServerGroups' => [ 'type' => 'integer', ], 'TotalServers' => [ 'type' => 'integer', ], 'UnauthorizedOperationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'UpdateAppRequest' => [ 'type' => 'structure', 'members' => [ 'appId' => [ 'shape' => 'AppId', ], 'name' => [ 'shape' => 'AppName', ], 'description' => [ 'shape' => 'AppDescription', ], 'roleName' => [ 'shape' => 'RoleName', ], 'serverGroups' => [ 'shape' => 'ServerGroups', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'UpdateAppResponse' => [ 'type' => 'structure', 'members' => [ 'appSummary' => [ 'shape' => 'AppSummary', ], 'serverGroups' => [ 'shape' => 'ServerGroups', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'UpdateReplicationJobRequest' => [ 'type' => 'structure', 'required' => [ 'replicationJobId', ], 'members' => [ 'replicationJobId' => [ 'shape' => 'ReplicationJobId', ], 'frequency' => [ 'shape' => 'Frequency', ], 'nextReplicationRunStartTime' => [ 'shape' => 'Timestamp', ], 'licenseType' => [ 'shape' => 'LicenseType', ], 'roleName' => [ 'shape' => 'RoleName', ], 'description' => [ 'shape' => 'Description', ], 'numberOfRecentAmisToKeep' => [ 'shape' => 'NumberOfRecentAmisToKeep', ], 'encrypted' => [ 'shape' => 'Encrypted', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'UpdateReplicationJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'UserData' => [ 'type' => 'structure', 'members' => [ 's3Location' => [ 'shape' => 'S3Location', ], ], ], 'VPC' => [ 'type' => 'string', ], 'VmId' => [ 'type' => 'string', ], 'VmManagerId' => [ 'type' => 'string', ], 'VmManagerName' => [ 'type' => 'string', ], 'VmManagerType' => [ 'type' => 'string', 'enum' => [ 'VSPHERE', 'SCVMM', 'HYPERV-MANAGER', ], ], 'VmName' => [ 'type' => 'string', ], 'VmPath' => [ 'type' => 'string', ], 'VmServer' => [ 'type' => 'structure', 'members' => [ 'vmServerAddress' => [ 'shape' => 'VmServerAddress', ], 'vmName' => [ 'shape' => 'VmName', ], 'vmManagerName' => [ 'shape' => 'VmManagerName', ], 'vmManagerType' => [ 'shape' => 'VmManagerType', ], 'vmPath' => [ 'shape' => 'VmPath', ], ], ], 'VmServerAddress' => [ 'type' => 'structure', 'members' => [ 'vmManagerId' => [ 'shape' => 'VmManagerId', ], 'vmId' => [ 'shape' => 'VmId', ], ], ], 'VmServerAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VmServerAddress', ], ], ],];
