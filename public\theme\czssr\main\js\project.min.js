var $$ = document;
var $$getValue = (elementId) => $$.getElementById(elementId).value;
function initPickerMap(){pickerMap=new google.maps.Map(document.getElementById("ui_picker_map_wrap"),{center:{lat:0,lng:0},disableDefaultUI:!0,mapTypeId:google.maps.MapTypeId.ROADMAP,zoom:15}),pickerMarker=new google.maps.Marker({map:pickerMap,position:{lat:0,lng:0}})}var $pickerLib=$(".ui-picker-lib"),pickerMap,pickerMarker;"undefined"!=typeof google&&initPickerMap(),"undefined"!=typeof jQuery.ui&&($(".ui-picker-draggable-handler").draggable({addClasses:!1,appendTo:"body",cursor:"move",cursorAt:{top:0,left:0},delay:100,helper:function(){return $('<div class="tile tile-brand-accent ui-draggable-helper"><div class="tile-side pull-left"><div class="avatar avatar-sm"><strong>'+$(".ui-picker-selected:first .ui-picker-draggable-avatar strong").html()+'</strong></div></div><div class="tile-inner text-overflow">'+$(".ui-picker-selected:first .ui-picker-info-title").html()+"</div></div>")},start:function(a,b){var c=$(".ui-picker-selected").length;c>1&&$(".ui-draggable-helper").append('<div class="avatar avatar-brand avatar-sm ui-picker-draggable-count">'+c+"</div>")},zIndex:100}),$(".ui-picker-nav .nav a").droppable({accept:".ui-picker-draggable-handler",addClasses:!1,drop:function(a,b){$("body").snackbar({content:'Dropped on "'+$(this).html()+'"'})},hoverClass:"ui-droppable-helper",tolerance:"pointer"}),$pickerLib.selectable({cancel:".ui-picker-draggable-handler",filter:".ui-picker-selectable-handler",selecting:function(a,b){var c=$(b.selecting).parent();c.addClass("tile-brand-accent ui-picker-selected"),$(".ui-picker-info").addClass("ui-picker-info-active").removeClass("ui-picker-info-null"),$(".ui-picker-info-desc-wrap").html(c.find(".ui-picker-info-desc").html()),$(".ui-picker-info-title-wrap").html(c.find(".ui-picker-info-title").html());var d=new google.maps.LatLng(c.find(".ui-picker-map-lat").html(),c.find(".ui-picker-map-lng").html());pickerMap.setCenter(d),pickerMarker.setMap(pickerMap),pickerMarker.setPosition(d)},unselecting:function(a,b){var c=$(b.unselecting).parent();if(c.removeClass("tile-brand-accent ui-picker-selected"),$(".ui-picker-selected").length){var d=$($(".ui-picker-selected")[0]);$(".ui-picker-info-desc-wrap").html(d.find(".ui-picker-info-desc").html()),$(".ui-picker-info-title-wrap").html(d.find(".ui-picker-info-title").html());var e=new google.maps.LatLng(d.find(".ui-picker-map-lat").html(),d.find(".ui-picker-map-lng").html());pickerMap.setCenter(e),pickerMarker.setMap(pickerMap),pickerMarker.setPosition(e)}else $(".ui-picker-info").addClass("ui-picker-info-null"),$(".ui-picker-info-desc-wrap").html(""),$(".ui-picker-info-title-wrap").html(""),pickerMarker.setMap(null)}})),$(document).on("click",".ui-picker-info-close",function(){$(".ui-picker-info").removeClass("ui-picker-info-active")}),$("#ui_datepicker_example_1").pickdate(),$("#ui_datepicker_example_2").pickdate({cancel:"Clear",closeOnCancel:!1,closeOnSelect:!0,container:"",firstDay:1,format:"You selecte!d: dddd, d mm, yy",formatSubmit:"dd/mmmm/yyyy",ok:"Close",onClose:function(){$("body").snackbar({content:"Datepicker closes"})},onOpen:function(){$("body").snackbar({content:"Datepicker opens"})},selectMonths:!0,selectYears:10,today:""}),$("#ui_datepicker_example_3").pickdate({disable:[[2016,0,12],[2016,0,13],[2016,0,14]],today:""}),$("#ui_datepicker_example_4").pickdate({disable:[new Date(2016,0,12),new Date(2016,0,13),new Date(2016,0,14)],today:""}),$("#ui_datepicker_example_5").pickdate({disable:[2,4,6],today:""}),$("#ui_datepicker_example_6").pickdate({disable:[{from:[2016,0,12],to:2}],today:""}),$("#ui_datepicker_example_7").pickdate({disable:[!0,3,[2016,0,13],new Date(2016,0,14)],today:""}),$("#ui_datepicker_example_8").pickdate({disable:[{from:[2016,0,10],to:[2016,0,30]},[2016,0,13,"inverted"],{from:[2016,0,19],to:[2016,0,21],inverted:!0}],today:""}),$("#ui_datepicker_example_9").pickdate({max:[2016,0,30],min:[2016,0,10],today:""}),$("#ui_datepicker_example_10").pickdate({max:new Date(2016,0,30),min:new Date(2016,0,10),today:""}),$("#ui_datepicker_example_11").pickdate({max:!0,min:-10,today:""}),$(".finish-loading").on("click",function(a){a.stopPropagation(),$($(this).attr("data-target")).addClass("el-loading-done")}),$("#ui_el_loading_example_wrap .tile-active-show").each(function(a){var b,c=$(this);c.on("hide.bs.tile",function(a){clearTimeout(b)}),c.on("show.bs.tile",function(a){$(".el-loading",c).hasClass("el-loading-done")||(b=setTimeout(function(){$(".el-loading",c).addClass("el-loading-done"),c.prepend('<div class="tile-sub"><p>Additional information<br><small>Aliquam in pharetra leo. In congue, massa sed elementum dictum, justo quam efficitur risus, in posuere mi orci ultrices diam.</small></p></div>')},6e3))})});var snackbarText=1;$("#ui_snackbar_toggle_1").on("click",function(){$("body").snackbar({content:"Simple snackbar "+snackbarText+" with some text",show:function(){snackbarText++}})}),$("#ui_snackbar_toggle_2").on("click",function(){$("body").snackbar({content:'<a data-dismiss="snackbar">Dismiss</a><div class="snackbar-text">Simple snackbar '+snackbarText+' with some text and a simple <a href="javascript:void(0)">link</a>.</div>',show:function(){snackbarText++}})});

$(function(){
	var dropdownvalarr = $("button[data-toggle=dropdown]");
	for (var i=0;i<dropdownvalarr.length;i++) {
		var dropdownval = $("code[data-default=" + dropdownvalarr[i].id + "]").text();
		dropdownvalarr[i].append(dropdownval);
	}

	$('button[data-toggle=dropdown]').click(function(){
        $(this).parent().addClass('control-highlight-custom');
	});

	$('button[data-toggle=dropdown]').blur(function(){
		if ($(this)[0].innerText=='') {
			$(this).parent().removeClass('control-highlight-custom');
		}
	});

    $('.dropdown-option').click(function(){
        var dropdownID = $(this).attr('data');
		$('#' + dropdownID).text($(this).text());
		$('#' + dropdownID).val($(this).attr('val'));
	}); 

});

  // 传入两个对象，分别为点击的按钮和要弹出的部分
  // 传入两个数组循环调用即可实现多个按钮绑定对应模态框
  function custModal(button,modal) {
    
	let mask;
  
	prepareMask();
	listenOpen();
	listenClose();
  
	function prepareMask() {
	  mask = document.createElement('div'); 
	  mask.classList.add('cust-mask');  //给遮罩附上给定的CSS
	}
  
	function listenOpen() {
	  button.addEventListener('click',()=>{
	    insertMask();
	    showModal();
	  });
	}
  
	function insertMask() {
	  mask.classList.remove('fade-delay')
	  document.body.appendChild(mask);
	  requestAnimationFrame(()=>{
	    mask.classList.add('cust-maskfade');
	  });
	}
  
	function showModal() {
	  modal.style.display = 'unset';
	  requestAnimationFrame(()=>{
	    modal.classList.add('cust-modelin','fade-delay');
	  });
	}
  
	function listenClose() {
	  mask.addEventListener('click',()=>{
	    hideModal();
	    removeMask();
	  });
	}
  
	function hideModal() {
	  modal.classList.remove('fade-delay')
	  modal.classList.remove('cust-modelin');
	  //等动画结束再隐藏
	  setTimeout(()=>{
	    modal.style.display = 'none';
	  },300);
	}
  
	function removeMask() {
	  mask.classList.add('fade-delay');
	  requestAnimationFrame(()=>{
	    mask.classList.remove('cust-maskfade');
	  });
	  //等动画结束再移除
	  setTimeout(()=>{
	    document.body.removeChild(mask);
	  },600);
	}
	
		}
	
	//传入两个对象，分别为下拉按钮和菜单
	function custDropdown(button,dropdownarea) {
		listenDown();
		
		function listenDown() {
      button.addEventListener("click",()=>{
				if (!dropdownarea.classList.contains('dropdown-active')) {
					requestAnimationFrame(()=>{
						dropdownarea.classList.add('dropdown-active');
					});
					dropdownarea.style.height = dropdownarea.scrollHeight + 2*parseInt(window.getComputedStyle(dropdownarea).paddingLeft) + 'px'; //我也不知道为什么少一个padding-bottom的长度
				} else {
					requestAnimationFrame(()=>{
						dropdownarea.classList.remove('dropdown-active');
					})
					dropdownarea.style.height = '0px';
				}		  
			});
		}

	}

	//传入两个对象，分别为按钮和箭头
	function rotatrArrow(button,arrow) {
		button.addEventListener('click',()=>{
				if (!arrow.classList.contains('arrow-rotate')) {
						arrow.classList.add('arrow-rotate');
	} else {
		arrow.classList.remove('arrow-rotate');
	}
});
}

//buttonID为触发切换按钮的ID值，nodeToHide为需要隐藏的元素，nodeToShow为需要显示的元素，display为需要显示的元素的display属性的值,defaultUIname为保存的默认UI的名称
class UIswitch {
	constructor (buttonID,nodeToHide,nodeToShow,display,tempName) {
		this.nodeToHide = nodeToHide;
		this.nodeToShow =nodeToShow;
		this.display = display;
		this.buttonID = buttonID;
		this.tempName = tempName
	}
	
	listenSwitch() {
		$$.getElementById(this.buttonID).addEventListener('click',()=>{

			this.nodeToHide.classList.add('node-fade');

			setTimeout(()=>{
				this.nodeToShow.style.display = this.display;
				this.nodeToHide.style.display = 'none';
			},250);

			setTimeout(()=>{
				this.nodeToShow.classList.remove('node-fade');
			},270);
			
			this.saveTemp();
	
		});
	}

	saveTemp() {
			let defaultUI = {
				defaultType: this.buttonID
			}
			
			defaultUI = JSON.stringify(defaultUI);
			localStorage.setItem(this.tempName,defaultUI);
	}
	
	setDefault() {
			let nodeDefaultUI = localStorage.getItem(this.tempName);
	
			nodeDefaultUI = JSON.parse(nodeDefaultUI);

			if (window.getComputedStyle(this.nodeToShow).display === 'none' && (window.getComputedStyle(this.nodeToHide).display === 'none')) {
				this.nodeToShow.style.display = this.display;
			} 

			if (nodeDefaultUI && nodeDefaultUI.defaultType === this.buttonID) {
				this.nodeToShow.style.display = this.display;
				this.nodeToShow.classList.remove('node-fade');
				this.nodeToHide.style.display = 'none';
				this.nodeToHide.classList.add('node-fade');
			}	
	}

}

	
    
	