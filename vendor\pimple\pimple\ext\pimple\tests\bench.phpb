<?php

if (!class_exists('Pimple\Container')) {
    require_once __DIR__ . '/../../../src/Pimple/Container.php';
} else {
    echo "pimple-c extension detected, using...\n\n";
}

$time = microtime(true);

function foo() { }
$factory = function () { };

for ($i=0; $i<10000; $i++) {

$p = new Pimple\Container;

$p['foo'] = 'bar';

if (!isset($p[3])) {
    $p[3] = $p['foo'];
    $p[]  = 'bar';
}

$p[2] = 42;

if (isset($p[2])) {
	unset($p[2]);
}

$p[42] = $p['foo'];

$p['cb'] = function($arg) { };

$p[] = $p['cb'];

echo $p['cb'];
echo $p['cb'];
echo $p['cb'];

//$p->factory($factory);

$p['factory'] = $factory;

echo $p['factory'];
echo $p['factory'];
echo $p['factory'];

}

echo microtime(true)  - $time;
