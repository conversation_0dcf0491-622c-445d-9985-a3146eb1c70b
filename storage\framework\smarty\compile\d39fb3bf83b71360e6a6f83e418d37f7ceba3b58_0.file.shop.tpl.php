<?php
/* Smarty version 3.1.33, created on 2022-07-17 17:50:56
  from '/www/wwwroot/www.shadowingy.com/resources/views/czssr/user/shop.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62d3db809c1835_60397343',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'd39fb3bf83b71360e6a6f83e418d37f7ceba3b58' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.com/resources/views/czssr/user/shop.tpl',
      1 => 1657973085,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:user/main.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:user/footer.tpl' => 1,
    'file:user/sspanel_pay_shop.tpl' => 1,
  ),
),false)) {
function content_62d3db809c1835_60397343 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:user/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Shops</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">商店</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <button id="buy_traf" class="btn btn-sm btn-neutral">购买流量包</button>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row card-wrapper">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">购买说明</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
                      <?php if ($_smarty_tpl->tpl_vars['config']->value['shop_free'] != null && $_smarty_tpl->tpl_vars['isBuy']->value == 1) {?>
                      <a href="javascript:void(0);" class="btn btn-primary btn-sm mb-3" onclick="buy('<?php echo $_smarty_tpl->tpl_vars['shop_free']->value->id;?>
',<?php echo $_smarty_tpl->tpl_vars['shop_free']->value->auto_renew;?>
)"><i class="fa fa-arrow-right"></i>  免费订阅计划</a>
                      <?php }?>
                      <?php if ($_smarty_tpl->tpl_vars['config']->value['shop_odm'] != null) {?>
                      <a href="javascript:void(0);" class="btn btn-primary btn-sm mb-3" onclick="buy('<?php echo $_smarty_tpl->tpl_vars['shop_odm']->value->id;?>
',<?php echo $_smarty_tpl->tpl_vars['shop_odm']->value->auto_renew;?>
)"><i class="fa fa-arrow-right"></i>  定制订阅计划</a>
                      <?php }?>
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>你购买的套餐等级决定了你可以用<a href="/user/node" target="_blank">哪些等级的节点</a>.</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>用户等级：<?php echo $_smarty_tpl->tpl_vars['levels']->value;?>
</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>流量包商品、同等级(如：你现在的等级是<code>铂金</code>你购买铂金套餐.)商品可以叠加.</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>高等级购买低等级套餐(如：你现在的等级是<code>王者</code>你购买铂金套餐)会导致降级,但流量会叠加.</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>低等级购买高等级套餐(如：你现在的等级是<code>铂金</code>你购买王者套餐)会导致本身的套餐被商品的套餐替代，原有套餐清空.</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>升级补差价请开<a href="/user/ticket" target="_blank">工单</a>处理.</p>
						<p>
							<i class="ni ni-card ni-lg icon-ver"></i>当前余额：<font color="#399AF2" size="3"><?php echo $_smarty_tpl->tpl_vars['user']->value->money;?>
</font> 火箭币&nbsp;&nbsp;&nbsp;&nbsp;<a class="btn btn-success btn-sm" href="/user/code" >点此充值</a>
						</p>
                        <p>
							<i class="ni ni-card ni-lg icon-ver"></i>当前套餐等级：<font color="#399AF2" size="3"><?php echo $_smarty_tpl->tpl_vars['level']->value->name;?>
</font> &nbsp;&nbsp;
							
                        </p>
                        <p>
                          到期日: <font color="#399AF2" size="3"><?php echo $_smarty_tpl->tpl_vars['user']->value->class_expire;?>
</font>&nbsp;&nbsp;剩余价值: <font color="#399AF2" size="3"><?php echo $_smarty_tpl->tpl_vars['get_money']->value;?>
 </font>火箭币
						</p>
						<?php if ($_smarty_tpl->tpl_vars['config']->value["refund"] == "true") {?>
							<p>
								此功能为退掉当前套餐折算成余额,然后新购套餐用&nbsp;&nbsp;<button id="shop_ref" class="btn btn-danger btn-sm <?php if ($_smarty_tpl->tpl_vars['get_money']->value == 0) {?>disabled<?php }?>" href="/user/code" >折算套餐剩余价值</button>
							</p>
						<?php }?>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
		</div>
	  </div>	
	  <div id="mytab" class="row">
		<div class="col">
			<div class="card">
			<div class="card-body">
				<div class="nav-wrapper">
					<ul class="nav nav-pills nav-fill flex-column flex-md-row" id="tabs-icons-text" role="tablist">
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0 active" id="tabs-icons-text-1-tab" data-toggle="tab" href="#tabs-icons-text-1" role="tab" aria-controls="tabs-icons-text-1" aria-selected="true"><img src="/theme/czssr/main/images/hot.gif"></i> 铂银黄金线路套餐A</a>
						</li>
						<?php if (count($_smarty_tpl->tpl_vars['shops_p_c2']->value) != 0) {?>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-2-tab" data-toggle="tab" href="#tabs-icons-text-2" role="tab" aria-controls="tabs-icons-text-2" aria-selected="false"><img src="/theme/czssr/main/images/hot.gif"></i>铂金钻石线路套餐B</a>
						</li>
						<?php }?>
						<?php if (count($_smarty_tpl->tpl_vars['shops_p_c3']->value) != 0) {?>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-3-tab" data-toggle="tab" href="#tabs-icons-text-3" role="tab" aria-controls="tabs-icons-text-3" aria-selected="false"><i><img src="/theme/czssr/main/images/hot.gif"></i> 钻石永久线路套餐C</a>
						</li>
						<?php }?>
						<?php if (count($_smarty_tpl->tpl_vars['shops_p_c4']->value) != 0) {?>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-4-tab" data-toggle="tab" href="#tabs-icons-text-4" role="tab" aria-controls="tabs-icons-text-4" aria-selected="false"><i><img src="/theme/czssr/main/images/hot.gif"></i> IEPL专线套餐D</a>
						</li>
						<?php }?>
					</ul>
				</div>
			</div>
			</div>
		</div>
	  </div>
	  <div class="tab-content" id="myTabContent">
		<div class="tab-pane fade show active" id="tabs-icons-text-1" role="tabpanel" aria-labelledby="tabs-icons-text-1-tab">

			<div class="row card-wrapper">
			<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['shops_p_c1']->value, 'shop');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['shop']->value) {
?>
			<div class="col-lg-4 col-md-6 col-sm-6">
			 <!-- Pricing card -->
				<div class="card card-pricing border-0 text-center mb-4">
					<div class="card-header bg-transparent">
						<h4 class="text-uppercase ls-1 text-primary py-3 mb-0"><?php echo $_smarty_tpl->tpl_vars['shop']->value->name;?>
</h4>
					</div>
					<div class="card-body px-lg-7">
						<div class="font-size">火箭币:<?php echo $_smarty_tpl->tpl_vars['shop']->value->price;?>
</div>
						<span class=" text-muted">每次续费(充值比1:1)</span>
						<ul class="list-unstyled my-4">
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm">内含 <?php echo $_smarty_tpl->tpl_vars['shop']->value->bandwidth();?>
G流量可用</span>
							</div>
							</div>
						</li>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm">拥有 <?php echo $_smarty_tpl->tpl_vars['shop']->value->user_class();?>
 等级节点群组可用</span>
							</div>
							</div>
						</li>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm"><?php ob_start();
echo $_smarty_tpl->tpl_vars['shop']->value->connector();
$_prefixVariable1 = ob_get_clean();
if ($_prefixVariable1 == '0') {?>无限制 在线客户端<?php } else {
echo $_smarty_tpl->tpl_vars['shop']->value->connector();?>
 个在线客户端可用<?php }?></span>
							</div>
							</div>
						</li>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm">账号有效期增加 <?php echo $_smarty_tpl->tpl_vars['shop']->value->expire();?>
 天</span>
							</div>
							</div>
						</li>
                       <?php if ($_smarty_tpl->tpl_vars['shop']->value->content_extra() != null) {?>
						<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['shop']->value->content_extra(), 'service');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['service']->value) {
?>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
									<i class="fa fa-<?php echo $_smarty_tpl->tpl_vars['service']->value[0];?>
"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm"><?php echo $_smarty_tpl->tpl_vars['service']->value[1];?>
</span>
							</div>
							</div>
						</li>
						<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                       <?php }?>   
						</ul>
						
					</div>
					<div class="card-footer bg-primary">
						<a href="javascript:void(0);" class="text-white" onclick="buy('<?php echo $_smarty_tpl->tpl_vars['shop']->value->id;?>
',<?php echo $_smarty_tpl->tpl_vars['shop']->value->auto_renew;?>
)"><i class="fa fa-arrow-right"></i>  订阅计划</a>
					</div>
				</div>
			</div>
			<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
			</div>
			
		</div><!--row-->
		<?php if (count($_smarty_tpl->tpl_vars['shops_p_c2']->value) != 0) {?>
		<div class="tab-pane fade" id="tabs-icons-text-2" role="tabpanel" aria-labelledby="tabs-icons-text-2-tab">

			<div class="row card-wrapper">
			<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['shops_p_c2']->value, 'shop');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['shop']->value) {
?>
			<div class="col-lg-4 col-md-6 col-sm-6">
			 <!-- Pricing card -->
				<div class="card card-pricing border-0 text-center mb-4">
					<div class="card-header bg-transparent">
						<h4 class="text-uppercase ls-1 text-primary py-3 mb-0"><?php echo $_smarty_tpl->tpl_vars['shop']->value->name;?>
</h4>
					</div>
					<div class="card-body px-lg-7">
						<div class="font-size">火箭币:<?php echo $_smarty_tpl->tpl_vars['shop']->value->price;?>
</div>
						<span class=" text-muted">每次续费(充值比1:1)</span>
						<ul class="list-unstyled my-4">
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm">内含 <?php echo $_smarty_tpl->tpl_vars['shop']->value->bandwidth();?>
G流量可用</span>
							</div>
							</div>
						</li>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm">拥有 <?php echo $_smarty_tpl->tpl_vars['shop']->value->user_class();?>
 等级节点群组可用</span>
							</div>
							</div>
						</li>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm"><?php ob_start();
echo $_smarty_tpl->tpl_vars['shop']->value->connector();
$_prefixVariable2 = ob_get_clean();
if ($_prefixVariable2 == '0') {?>无限制 在线客户端<?php } else {
echo $_smarty_tpl->tpl_vars['shop']->value->connector();?>
 个在线客户端可用<?php }?></span>
							</div>
							</div>
						</li>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm">账号有效期增加 <?php echo $_smarty_tpl->tpl_vars['shop']->value->expire();?>
 天</span>
							</div>
							</div>
						</li>
                       <?php if ($_smarty_tpl->tpl_vars['shop']->value->content_extra() != null) {?>
						<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['shop']->value->content_extra(), 'service');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['service']->value) {
?>
						<li>
							<div class="d-flex align-items-center">
							  <div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
									<i class="fa fa-<?php echo $_smarty_tpl->tpl_vars['service']->value[0];?>
"></i>
								</div>
							  </div>
							<div>
								<span class="pl-2 text-sm"><?php echo $_smarty_tpl->tpl_vars['service']->value[1];?>
</span>
							</div>
							</div>
						</li>
						<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                       <?php }?>   
						</ul>
						
					</div>
					<div class="card-footer bg-primary">
						<a href="javascript:void(0);" class="text-white" onclick="buy('<?php echo $_smarty_tpl->tpl_vars['shop']->value->id;?>
',<?php echo $_smarty_tpl->tpl_vars['shop']->value->auto_renew;?>
)"><i class="fa fa-arrow-right"></i>  订阅计划</a>
					</div>
				</div>
			</div>
			<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
			</div>
			
		</div><!--row-->
		<?php }?>
		<?php if (count($_smarty_tpl->tpl_vars['shops_p_c3']->value) != 0) {?>
		<div class="tab-pane fade" id="tabs-icons-text-3" role="tabpanel" aria-labelledby="tabs-icons-text-3-tab">

			<div class="row card-wrapper">
			<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['shops_p_c3']->value, 'shop');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['shop']->value) {
?>
			<div class="col-lg-4 col-md-6 col-sm-6">
			 <!-- Pricing card -->
				<div class="card card-pricing border-0 text-center mb-4">
					<div class="card-header bg-transparent">
						<h4 class="text-uppercase ls-1 text-primary py-3 mb-0"><?php echo $_smarty_tpl->tpl_vars['shop']->value->name;?>
</h4>
					</div>
					<div class="card-body px-lg-7">
						<div class="font-size">火箭币:<?php echo $_smarty_tpl->tpl_vars['shop']->value->price;?>
</div>
						<span class=" text-muted">每次续费(充值比1:1)</span>
						<ul class="list-unstyled my-4">
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm">内含 <?php echo $_smarty_tpl->tpl_vars['shop']->value->bandwidth();?>
G流量可用</span>
							</div>
							</div>
						</li>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm">拥有 <?php echo $_smarty_tpl->tpl_vars['shop']->value->user_class();?>
 等级节点群组可用</span>
							</div>
							</div>
						</li>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm"><?php ob_start();
echo $_smarty_tpl->tpl_vars['shop']->value->connector();
$_prefixVariable3 = ob_get_clean();
if ($_prefixVariable3 == '0') {?>无限制 在线客户端<?php } else {
echo $_smarty_tpl->tpl_vars['shop']->value->connector();?>
 个在线客户端可用<?php }?></span>
							</div>
							</div>
						</li>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm">账号有效期增加 <?php echo $_smarty_tpl->tpl_vars['shop']->value->expire();?>
 天</span>
							</div>
							</div>
						</li>
                       <?php if ($_smarty_tpl->tpl_vars['shop']->value->content_extra() != null) {?>
						<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['shop']->value->content_extra(), 'service');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['service']->value) {
?>
						<li>
							<div class="d-flex align-items-center">
						      <div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								    <i class="fa fa-<?php echo $_smarty_tpl->tpl_vars['service']->value[0];?>
"></i>
								</div>
					          </div>
							<div>
								<span class="pl-2 text-sm"><?php echo $_smarty_tpl->tpl_vars['service']->value[1];?>
</span>
							</div>
							</div>
						</li>
						<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                       <?php }?>   
						</ul>
						
					</div>
					<div class="card-footer bg-primary">
						<a href="javascript:void(0);" class="text-white" onclick="buy('<?php echo $_smarty_tpl->tpl_vars['shop']->value->id;?>
',<?php echo $_smarty_tpl->tpl_vars['shop']->value->auto_renew;?>
)"><i class="fa fa-arrow-right"></i>  订阅计划</a>
					</div>
				</div>
			</div>
			<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
			</div>
			
		</div><!--row-->
		<?php }?>
		<?php if (count($_smarty_tpl->tpl_vars['shops_p_c4']->value) != 0) {?>
		<div class="tab-pane fade" id="tabs-icons-text-4" role="tabpanel" aria-labelledby="tabs-icons-text-4-tab">

			<div class="row card-wrapper">
			<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['shops_p_c4']->value, 'shop');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['shop']->value) {
?>
			<div class="col-lg-4 col-md-6 col-sm-6">
			 <!-- Pricing card -->
				<div class="card card-pricing border-0 text-center mb-4">
					<div class="card-header bg-transparent">
						<h4 class="text-uppercase ls-1 text-primary py-3 mb-0"><?php echo $_smarty_tpl->tpl_vars['shop']->value->name;?>
</h4>
					</div>
					<div class="card-body px-lg-7">
						<div class="font-size">火箭币:<?php echo $_smarty_tpl->tpl_vars['shop']->value->price;?>
</div>
						<span class=" text-muted">每次续费(充值比1:1)</span>
						<ul class="list-unstyled my-4">
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm">内含 <?php echo $_smarty_tpl->tpl_vars['shop']->value->bandwidth();?>
G流量可用</span>
							</div>
							</div>
						</li>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm">拥有 <?php echo $_smarty_tpl->tpl_vars['shop']->value->user_class();?>
 等级节点群组可用</span>
							</div>
							</div>
						</li>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm"><?php ob_start();
echo $_smarty_tpl->tpl_vars['shop']->value->connector();
$_prefixVariable4 = ob_get_clean();
if ($_prefixVariable4 == '0') {?>无限制 在线客户端<?php } else {
echo $_smarty_tpl->tpl_vars['shop']->value->connector();?>
 个在线客户端可用<?php }?></span>
							</div>
							</div>
						</li>
						<li>
							<div class="d-flex align-items-center">
							<div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								<i class="fa fa-check"></i>
								</div>
							</div>
							<div>
								<span class="pl-2 text-sm">账号有效期增加 <?php echo $_smarty_tpl->tpl_vars['shop']->value->expire();?>
 天</span>
							</div>
							</div>
						</li>
                       <?php if ($_smarty_tpl->tpl_vars['shop']->value->content_extra() != null) {?>
						<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['shop']->value->content_extra(), 'service');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['service']->value) {
?>
						<li>
							<div class="d-flex align-items-center">
							  <div>
								<div class="icon icon-xs icon-shape bg-gradient-primary text-white shadow rounded-circle">
								
								<i class="fa fa-<?php echo $_smarty_tpl->tpl_vars['service']->value[0];?>
"></i>
								
								</div>
							  </div>
							<div>
								<span class="pl-2 text-sm"><?php echo $_smarty_tpl->tpl_vars['service']->value[1];?>
</span>
							</div>
							</div>
						</li>
						<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                       <?php }?>   
						</ul>
						
					</div>
					<div class="card-footer bg-primary">
						<a href="javascript:void(0);" class="text-white" onclick="buy('<?php echo $_smarty_tpl->tpl_vars['shop']->value->id;?>
',<?php echo $_smarty_tpl->tpl_vars['shop']->value->auto_renew;?>
)"><i class="fa fa-arrow-right"></i>  订阅计划</a>
					</div>
				</div>
			</div>
			<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
			</div>
			
		</div><!--row-->
		<?php }?>
	  </div>
	  <div id="paycontent" class="row" style="display:none">
		<div class="col col-lg-5 col-md-5">
		  <div class="card">
			<!-- Card header -->
            <div class="card-header">
			  <h3 class="mb-0">
			  <a href="#black" onclick="backToShop()">
                <i class="fa fa-arrow-left"></i></a>
              &nbsp;支付订单</h3>
            </div>
              <!-- Card body -->
			<div class="card bg-gradient-Secondary">
				<div class="card-body">
					<blockquote class="blockquote mb-0">
					    <p id="name" class="description badge-dot mr-4"><i class="bg-warning"></i>商品名称:</p>
					    <p id="credit" class="description badge-dot mr-4"><i class="bg-warning"></i>优惠额度:</p>
					    <p id="total" class="description badge-dot mr-4"><i class="bg-warning"></i>总金额:</p>
					    <input type="hidden" id="amount" value="">
						<div class="custom-control custom-checkbox mb-2">
							<input id="disableothers" type="checkbox" class="custom-control-input" checked>
							<label class="custom-control-label" for="disableothers">关闭旧套餐自动续费</label>
						</div>
						<div id="autor" class="custom-control custom-checkbox mb-2">
							<input id="autorenew" type="checkbox" class="custom-control-input">
							<label class="custom-control-label" for="autorenew">到期自动续费</label>
						</div>
						<div class="custom-control custom-radio mb-2">
							<input type="hidden" id="user_money" value="<?php echo $_smarty_tpl->tpl_vars['user']->value->money;?>
">
							<input type="radio" id="pay_code" name="customRadio" class="custom-control-input">
							<label id="pay_text" class="custom-control-label" for="pay_code">余额支付: <?php echo $_smarty_tpl->tpl_vars['user']->value->money;?>
</label>
						</div>
						<div class="custom-control custom-radio mb-2">
							<input type="radio" id="pay_online" name="customRadio" class="custom-control-input">
							<label class="custom-control-label" for="pay_online"><i><img src="/images/alipay.jpg" width="18" height="18"></i>&nbsp;支付宝:(推荐)</label>
						</div>
						<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] != null) {?>
						<div class="custom-control custom-radio mb-2">
							<input type="radio" id="wx_pay" name="customRadio" class="custom-control-input">
							<label class="custom-control-label" for="wx_pay"><i><img src="/images/weixin.jpg" width="18" height="18"></i>&nbsp;微信:(暂停支付)</label>
						</div>
						<?php }?>
					</blockquote>
					<div class="modal-footer">
						<button id="order_input" type="button" class="btn btn-primary">确认提交</button>
					</div>
				</div>
			</div>
		  </div>
		</div>
	  </div>
	  
	  <!--buy_traffic modal-->
	    <div class="modal fade" id="buy_traffic" tabindex="-1" role="dialog" aria-labelledby="buytrafficModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="buytrafficModalLabel" class="ls-2">请选择你需要的流量包</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
                <div class="card-body">
				<!-- Simple slider -->
                  <p class="description">购买单位: GB (最小单位50GB)</p>
					<div class="input-slider-container">
						<div id="slider-snap" class="input-slider"></div>
						<!-- Input slider values -->
						<div class="row mt-3">
							<div class="col-6">
							<span id="input-slider-value" class="range-slider-value"></span>
							<input id="traffics" type="hidden" value="">
							</div>
						</div>
					</div>
					<div class="form-group mt-3">
						<label class="form-control-label">购买价格(元)&nbsp;:&nbsp;</label>
                        <input id="traffic_p" type="hidden" value="<?php echo $_smarty_tpl->tpl_vars['config']->value['shop_traffic'];?>
">
						<input id="traffic_price" class="form-control form-control-sm" type="num" disabled required>
					</div>
					<div class="custom-control custom-radio mb-2">
						<input type="hidden" id="user_money1" value="<?php echo $_smarty_tpl->tpl_vars['user']->value->money;?>
">
						<input type="radio" id="pay_code1" name="customRadio" class="custom-control-input">
						<label id="pay_text1" class="custom-control-label" for="pay_code1">余额支付: <?php echo $_smarty_tpl->tpl_vars['user']->value->money;?>
</label>
					</div>
					<i class="fa fa-chrome fa-spin" style="font-size:18px;color:royalblue"></i>重要提示！<br />请先购买套餐后,流量不够再买流量包。否则单买流量包不能使用！</label>
				</div>	 
		      </div>
			  <div class="modal-footer">
                    <button id="buy_tra1" type="button" class="btn btn-primary">确认购买</button>
               </div>
		    </div>
		  </div>
		</div>
	  <!--coupon modal-->
	    <div class="modal fade" id="coupon_modal" tabindex="-1" role="dialog" aria-labelledby="couponModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="couponModalLabel" class="ls-2">你有优惠码吗?</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description mt-3">有的话, 请在这里输入; 没有的话, 直接确定吧.</p>
					<div class="form-group mt-3">
						<label class="form-control-label">输入你的优惠码&nbsp;:&nbsp;</label>
						<input id="coupon" class="form-control form-control-sm" type="text">
					</div>
				</div>	 
		      </div>
			  <div class="modal-footer">
                    <button id="coupon_input" type="button" class="btn btn-primary">确认提交</button>
               </div>
		    </div>
		  </div>
		</div>

	  <!--qr_code modal-->
	   <div class="modal fade" id="qr_code" tabindex="-1" role="dialog" aria-labelledby="qrcodeModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="qrcodeModalLabel" class="text-danger ls-2">请扫码完成支付</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p id="ali_qrarea"> </p>
		        </div>
		      </div>
		   </div>
		 </div>
	   </div>
	   <!-- Modal -->
		<div class="modal fade" id="shop_re" tabindex="-1" role="dialog" aria-labelledby="shoprefundModal" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="shoprefundModal" class="text-danger">请确定你的折算订单</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
		        <p class="h4 mb-3 text-black-hint">将为你退订当前套餐到余额,以新购套餐用(无法逆转).</p>
		      </div>
			  <div class="modal-footer">
				<button id="shop_refund" type="button" class="btn btn-primary" data-dismiss="modal">确定</button>
		      </div>
		    </div>
		  </div>
		</div>
		<div class="modal fade" id="readytopay" tabindex="-1" role="dialog" aria-labelledby="resultModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="resultModalLabel">正在连接支付网关</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
		        <p class="h6 mb-3 text-black-hint" id="title"> 感谢您对我们的支持，请耐心等待^_^</p>
		      </div>
		    </div>
		  </div>
		</div>
      <!--payresult-->
		<div class="modal fade" tabindex="-1" role="dialog" id="payresult" aria-labelledby="payresultModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h4 id="payresultModalLabel">充值进行时</h4>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<p class="h6 mb-3 text-black-hint" id="title"> 请在新打开的页面完成支付^_^请勿关闭本页面</p>
				</div>
				<div class="modal-footer">
				    <button id="rehrefdown" type="button" class="btn btn-primary" data-dismiss="modal">已完成充值</button>
					<button id="rehrefup" type="button" class="btn btn-secondary" data-dismiss="modal">遇到问题</button>
		        </div>
			</div>
		  </div>
		</div>
	  	  <?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php $_smarty_tpl->_subTemplateRender('file:user/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php echo '<script'; ?>
 src="/theme/czssr/main/js/qrcode.min.js"><?php echo '</script'; ?>
>
	  <?php echo '<script'; ?>
 src="/theme/czssr/main/js/nouislider.min.js"><?php echo '</script'; ?>
>
     <?php $_smarty_tpl->_subTemplateRender('file:user/sspanel_pay_shop.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?> 
	 
<?php echo '<script'; ?>
>
//优惠码确认
var pid = 0;
function buy(id, auto) {
    if (auto == 0) {
        document.getElementById('autor').style.display = "none";
    } else {
        document.getElementById('autor').style.display = "";
    }
    shop = id;
    $("#coupon_modal").modal();
}

function backToShop() {
	document.getElementById('mytab').style.display = "";
	document.getElementById('myTabContent').style.display = "";
	document.getElementById('paycontent').style.display = "none";
}

$('#shop_ref').on('click', function(){
   $("#shop_re").modal();
});
<?php echo '</script'; ?>
>
<?php echo '<script'; ?>
>
//流量条
var snapSlider = document.getElementById('slider-snap');
noUiSlider.create(snapSlider, {
	start: [1, 50],
	snap: true,
	connect: true,
	range: {
		'min': 50,
		'10%': 50,
		'20%': 100,
		'30%': 150,
		'40%': 200,
		'50%': 250,
		'60%': 300,
		'70%': 350,
		'80%': 400,
		'90%': 450,
		'max': 500
	}
});
snapSlider.noUiSlider.on('update', function (values, handle) {
	document.getElementById('input-slider-value').innerHTML = values[handle];
	document.getElementById('traffics').value = Number(values[handle])
	var e = $('#traffic_p').val();
	document.getElementById('traffic_price').value = Number(values[handle]) * e;
	var b = Number($("#traffic_price").val());
	if (b > $('#user_money1').val()) {
			document.getElementById("pay_code1").disabled=true;
			$('#pay_text1').html("<del>余额不足 <a href='/user/code'>请先充值</a>: <?php echo $_smarty_tpl->tpl_vars['user']->value->money;?>
</del>");
			document.getElementById("buy_tra1").className="btn btn-primary disabled";
	}else {
			document.getElementById("pay_code1").disabled=false;
			$('#pay_text1').html("余额支付: <?php echo $_smarty_tpl->tpl_vars['user']->value->money;?>
");
			document.getElementById("pay_code1").checked=true;
			document.getElementById("buy_tra1").className="btn btn-primary";
	}
	
});
$("#buy_traf").on("click", function() {
	$('#buy_traffic').modal();

});
<?php echo '</script'; ?>
><?php }
}
