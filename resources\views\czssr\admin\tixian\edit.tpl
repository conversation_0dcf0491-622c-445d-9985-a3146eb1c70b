{include file='admin/main.tpl'}
    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Check take money</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/tixian">提现管理</a></li>
                  <li class="breadcrumb-item active" aria-current="page">查看提现</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/user" class="btn btn-sm btn-neutral">用户</a>
            </div>
          </div>
        </div>
      </div>
    </div>
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">审核提现 #{$Tixian->id}</h3>
              </div>
              <!-- Card body -->
				<div class="bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">提现用户ID：{$Tixian->user_id}</label>
						</div>
						<div class="form-group">
							<label class="form-control-label">账号类型：{if $Tixian->ref_accid == 1}支付宝{else}微信{/if}</label>
						</div>
						<div class="form-group">
							<label class="form-control-label">提现至：{$Tixian->ref_acc}</label>
						</div>
                        <div class="form-group">
							<label class="form-control-label">收款码：</label>
                            <p><img src="{if $Tixian->tx_image != null}{$config['baseUrl']}/upload/invite/{$Tixian->tx_image}{/if}"></p>
						</div>
						<div class="form-group">
							<label id="tx_money" class="form-control-label">提现金额：{$Tixian->tx_money}</label>
						</div>
						<div class="form-group">
							<label class="form-control-label">申请日期：{$Tixian->tx_time}</label>
						</div>
						<div class="form-group">
							<label class="floating-label" for="ref_tx">审核结果:</label>
							<select id="ref_tx" class="form-control form-control-sm">
								<option value="1">确认通过</option>
								<option value="2">拒绝提现</option>
							</select>
						</div>
                       <div class="form-group">
							<p class="description badge-dot"><i class="bg-warning"></i>提交后会执行发送邮件，请耐心等待不要关闭页面</p>
						</div>
					</div>
					<div class="modal-footer">
						
						<button id="submit" type="submit" class="btn btn-primary">确认提交</button>
					</div>
				</div>
				
			</div>
        </div>
      </div><!--row-->

	{include file='dialog.tpl'}
	{include file='admin/footer.tpl'}

<script>
    window.addEventListener('load', () => {
        function submit() {
            $.ajax({
                type: "PUT",
                url: "/admin/tixian/{$Tixian->id}",
                dataType: "json",
                data: {
                    tx_money: {$Tixian->tx_money},
					ref_tx: $("#ref_tx").val()
                },
                success: data => {
                    if (data.ret) {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                    } else {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                    }
                  window.setTimeout("location.href=top.document.referrer", 2000);
                },
                error: jqXHR => {
                    $("#result").modal();
                    $('#msg').html("发生错误："+jqXHR.status);
                }
            });
        }

        $("#submit").click(function () {
            submit();
        });

    })
</script>