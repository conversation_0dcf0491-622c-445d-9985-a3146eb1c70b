<?php
/**
 * Created by PhpStorm.
 * User: tonyzou
 * Date: 2018/9/27
 * Time: 8:16 AM
 */

namespace App\Services\Gateway;
use App\Services\Auth;
use App\Services\Config;
use App\Models\User;
use App\Models\Code;
use App\Models\Paylist;
use App\Services\View;

class Mycode extends AbstractPayment
{

    function isHTTPS()
    {
        define('HTTPS', false);
        if (defined('HTTPS') && HTTPS) return true;
        if (!isset($_SERVER)) return FALSE;
        if (!isset($_SERVER['HTTPS'])) return FALSE;
        if ($_SERVER['HTTPS'] === 1) {  //Apache
            return TRUE;
        } elseif ($_SERVER['HTTPS'] === 'on') { //IIS
            return TRUE;
        } elseif ($_SERVER['SERVER_PORT'] == 443) { //其他
            return TRUE;
        }
        return FALSE;
    }

    public function purchase($request, $response, $args)
    {

        $codepay_id = Config::get('mycode_id');//这里改成码支付ID
        $codepay_key = Config::get('mycode_key'); //这是您的通讯密钥
        
        $user = Auth::getUser();
		$type = $request->getParsedBodyParam('type');
        $price = $request->getParam('price');
        $method = $request->getParam('method');
        if ($price <= 0) {
			$return['ret'] = 0;
			$return['msg'] = "非法的金额";
            return json_encode($return);
        }

        $pl = new Paylist();
        $pl->userid = $user->id;
        $pl->total = $price;
        $pl->tradeno = (string)time()."UID".(string)$user->id;
        $pl->save();
        
        $url_notify = Config::get('apiUrl');
        $url_return = (self::isHTTPS() ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'];
        if ($type == "alipay") {
            $payment_x = "/payment_a";
        }elseif($type == "wxpay"){
            $payment_x = "/payment_b";
        }

        $data = array(
            "pid" => $codepay_id,
            "type" => $type,
            "out_trade_no" => $pl->tradeno,
            "notify_url" => $url_notify . $payment_x . '/notify',//通知地址
            "return_url" => $url_return . '/user/code',//跳转地址
            "name"  => '日用百货',
            "money" => $price,
            "sitename"  => 'ANN'
        ); //构造需要传递的参数

        ksort($data); //重新排序$data数组
        reset($data); //内部指针指向数组中的第一个元素

        $sign = ''; //初始化需要签名的字符为空
        $urls = ''; //初始化URL参数为空

        foreach ($data AS $key => $val) { //遍历需要传递的参数
            if ($val == '' || $key == 'sign') continue; //跳过这些不参数签名
            if ($sign != '') { //后面追加&拼接URL
                $sign .= "&";
                $urls .= "&";
            }
            $sign .= "$key=$val"; //拼接为url参数形式
            $urls .= "$key=" . urlencode($val); //拼接为url参数形式并URL编码参数值

        }
   
        $query = $urls . '&sign=' . md5($sign . $codepay_key); //创建订单所需的参数
        /*if(isset($method)){
            $url = "http://pay.jiangh.top/qrcode.php?" . $query; //支付页面
            $result=self::curl_get($url);
            $res=json_decode($result,true);
            if($res['code']==1 || $res['code']==100){
                $return['ret'] = 1;
                $return['qrcode'] = $res['code_url'];
                $return['method'] = 'qr_code';
                $return['price'] = $pl->total;
                $return['pid'] = $pl->tradeno;
            }else{
		        $return['ret'] = 0;
                $return['msg'] = $res['msg'];
                $return['method'] = 'qr_code';
            }*/
      
            $url = "http://spay.accos.tk/submit.php?" . $query; //支付页面
            if(isset($method)){
                $result = $url;
            }else{
                $result = '<script>window.location.href="'.$url.'";</script>';
            }
		    $return['ret'] = 1;
            $return['url'] = $result;
            $return['pid'] = $pl->tradeno;
            $return['method'] = 'url';
       
        /*$result=self::curl_get($url);
        $res=json_decode($result,true);
        if($res['code']){
            $return['ret'] = 1;
            $return['qrcode'] = $res['code_url'];
            $return['method'] = 'qr_code';
            $return['price'] = $pl->total;
            $return['pid'] = $pl->tradeno;
        }else{
            $return['ret'] = 0;
            $return['msg'] = $res['msg'];
            $return['method'] = 'qr_code';
        }*/
		//$test='alipays://platformapi/startapp?appId=********&actionType=toAccount&goBack=NO&amount=0.28&userId=****************&memo=INTL';
		
        return json_encode($return);
		
    }
    public function XwPublic($request, $response, $args)
    {
        $type = $_GET['type'];
        $price = $_GET['price'];
		$tradeno = $_GET['tradeno'];
        $pl = Paylist::where("tradeno", $tradeno)->where("status", "<>", 1)->first();
		if ($pl == null) {
			exit("订单不存在, 请重新发起支付!");
		}
        $codepay_id = Config::get('mycode_id');//这里改成码支付ID
        $codepay_key = Config::get('mycode_key'); //这是您的通讯密钥
        $user = Auth::getUser();
        
        if ($price <= 0) {
            $return['ret'] = 0;
            $return['msg'] = "非法的金额";
            return json_encode($return);
        }
        
		$url_notify = Config::get('apiUrl');
        $url_return = (self::isHTTPS() ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'];
        if ($type == "alipay") {
            $payment_x = "/payment_a";
        }elseif($type == "wxpay"){
            $payment_x = "/payment_b";
        }

        $data = array(
            "pid" => $codepay_id,
            "type" => $type,
            "out_trade_no" => $pl->tradeno,
            "notify_url" => $url_notify . $payment_x . '/notify',//通知地址
            "return_url" => $url_return . '/user/code',//跳转地址
            "name"  => 'Shouk',
            "money" => $price,
            "sitename"  => 'ANN',
            "method" => 'qrcode'
        ); //构造需要传递的参数

        ksort($data); //重新排序$data数组
        reset($data); //内部指针指向数组中的第一个元素

        $sign = ''; //初始化需要签名的字符为空
        $urls = ''; //初始化URL参数为空

        foreach ($data AS $key => $val) { //遍历需要传递的参数
            if ($val == '' || $key == 'sign' || $key=='method') continue; //跳过这些不参数签名
            if ($sign != '') { //后面追加&拼接URL
                $sign .= "&";
                $urls .= "&";
            }
            $sign .= "$key=$val"; //拼接为url参数形式
            $urls .= "$key=" . urlencode($val); //拼接为url参数形式并URL编码参数值

        }
   
        $query = $urls . '&sign=' . md5($sign . $codepay_key); //创建订单所需的参数
        
        $url = "//spay.accos.tk/submit.php?" . $query; //支付页面
        $result=self::curl_get($url);
        $res=json_decode($result,true);
		
		$return['ret'] = 1;
        $return['url'] = $res['code_url'];
        
        //$url = '<script>window.location.href="'.$url.'";</script>';
		//$return['ret'] = 1;
        //$return['url'] = $url;
        $return['price'] = $pl->total;
        $return['pid'] = $pl->tradeno;
        return View::getSmarty()->assign('result', $url)->fetch('user/idtpay.tpl');
      
    }
    function curl_get($url)
    {
    	$ch = curl_init($url);
    	$httpheader[] = 'Accept:*/*';
    	$httpheader[] = 'Accept-Language:zh-CN,zh;q=0.8';
    	$httpheader[] = 'Connection:close';
    	curl_setopt($ch, CURLOPT_HTTPHEADER, $httpheader);
    	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    	curl_setopt($ch, CURLOPT_HEADER, 0);
    	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    	curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Linux; U; Android 4.4.1; zh-cn; R815T Build/JOP40D) AppleWebKit/533.1 (KHTML, like Gecko)Version/4.0 MQQBrowser/4.5 Mobile Safari/533.1');
    	curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    	curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    	$content = curl_exec($ch);
    	curl_close($ch);
    	return $content;
    }
    public function notify($request, $response, $args)
    {
        
        ksort($_GET); //排序post参数
        reset($_GET); //内部指针指向数组中的第一个元素
        $codepay_key=Config::get('mycode_key'); //这是您的密钥
        $sign = '';//初始化
        foreach ($_GET AS $key => $val) { //遍历POST参数
            if ($val == '' || $key == 'sign' || $key=='sign_type') continue; //跳过这些不签名
            if ($sign) $sign .= '&'; //第一个字符串签名不加& 其他加&连接起来参数
            $sign .= "$key=$val"; //拼接为url参数形式
        }
        if ($_GET['type'] == "alipay") {
            $type = "支付宝";
        }elseif($_GET['type'] == "wxpay") {
            $type = "微信支付";
        }
        if (!$_GET['trade_no'] || md5($sign . $codepay_key) != $_GET['sign']) { //不合法的数据
            exit('fail'); //返回失败，等待下次回调
        } else { //合法的数据
            //业务处理
            $pay_id = $_GET['out_trade_no']; //需要充值的ID 或订单号 或用户名
            $pay_no = $_GET['pay_no']; //流水号
            self::postPayment($pay_id, $type);
        }
      
        exit('success'); //返回成功 不要删除哦
        return;
    }


    public function getPurchaseHTML()
    {
        return '
                        <div class="card-inner">
                        <p class="card-heading">请输入充值金额</p>
                        <form class="codepay" name="codepay" action="/user/code/codepay" method="get">
                            <input class="form-control maxwidth-edit" id="price" name="price" placeholder="输入充值金额后，点击你要付款的应用图标即可" autofocus="autofocus" type="number" min="0.01" max="1000" step="0.01" required="required">
                            <br>
                            <button class="btn btn-flat waves-attach" id="btnSubmit" type="submit" name="type" value="1" ><img src="/images/alipay.jpg" width="50px" height="50px" /></button>
                            <button class="btn btn-flat waves-attach" id="btnSubmit" type="submit" name="type" value="2" ><img src="/images/qqpay.jpg" width="50px" height="50px" /></button>
                            <button class="btn btn-flat waves-attach" id="btnSubmit" type="submit" name="type" value="3" ><img src="/images/weixin.jpg" width="50px" height="50px" /></button>

                        </form>
                        </div>
';
    }

    public function getReturnHTML($request, $response, $args)
    {
        // TODO: Implement getReturnHTML() method.
    }

    public function getStatus($request, $response, $args)
    {
        $p = Paylist::where('tradeno', $_POST['pid'])->first();
        $return['ret'] = 1;
        $return['result'] = $p->status;
        if($p->status == 1){
            $return['msg'] = '订单成功!';
        }
        return json_encode($return);
    }
}