<?php
/* Smarty version 3.1.33, created on 2022-02-04 15:37:36
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/table/js_1.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_61fcd7c044bf49_15433912',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'd3ac6841926ba318e2423e46b39875ad98043dc5' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/table/js_1.tpl',
      1 => 1548128136,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_61fcd7c044bf49_15433912 (Smarty_Internal_Template $_smarty_tpl) {
?>function modify_table_visible(id, key) {
	if(document.getElementById(id).checked)
	{
		table_1.columns( '.' + key ).visible( true );
		localStorage.setItem(window.location.href + '-haschecked-' + id, true);
	}
	else
	{
		table_1.columns( '.' + key ).visible( false );
		localStorage.setItem(window.location.href + '-haschecked-' + id, false);
	}
}
<?php }
}
