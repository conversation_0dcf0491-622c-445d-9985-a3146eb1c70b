<?php
namespace Aws\IoTThingsGraph;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS IoT Things Graph** service.
 * @method \Aws\Result associateEntityToThing(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateEntityToThingAsync(array $args = [])
 * @method \Aws\Result createFlowTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFlowTemplateAsync(array $args = [])
 * @method \Aws\Result createSystemInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSystemInstanceAsync(array $args = [])
 * @method \Aws\Result createSystemTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSystemTemplateAsync(array $args = [])
 * @method \Aws\Result deleteFlowTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFlowTemplateAsync(array $args = [])
 * @method \Aws\Result deleteNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteNamespaceAsync(array $args = [])
 * @method \Aws\Result deleteSystemInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSystemInstanceAsync(array $args = [])
 * @method \Aws\Result deleteSystemTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSystemTemplateAsync(array $args = [])
 * @method \Aws\Result deploySystemInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deploySystemInstanceAsync(array $args = [])
 * @method \Aws\Result deprecateFlowTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deprecateFlowTemplateAsync(array $args = [])
 * @method \Aws\Result deprecateSystemTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deprecateSystemTemplateAsync(array $args = [])
 * @method \Aws\Result describeNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeNamespaceAsync(array $args = [])
 * @method \Aws\Result dissociateEntityFromThing(array $args = [])
 * @method \GuzzleHttp\Promise\Promise dissociateEntityFromThingAsync(array $args = [])
 * @method \Aws\Result getEntities(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEntitiesAsync(array $args = [])
 * @method \Aws\Result getFlowTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getFlowTemplateAsync(array $args = [])
 * @method \Aws\Result getFlowTemplateRevisions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getFlowTemplateRevisionsAsync(array $args = [])
 * @method \Aws\Result getNamespaceDeletionStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getNamespaceDeletionStatusAsync(array $args = [])
 * @method \Aws\Result getSystemInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSystemInstanceAsync(array $args = [])
 * @method \Aws\Result getSystemTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSystemTemplateAsync(array $args = [])
 * @method \Aws\Result getSystemTemplateRevisions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSystemTemplateRevisionsAsync(array $args = [])
 * @method \Aws\Result getUploadStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getUploadStatusAsync(array $args = [])
 * @method \Aws\Result listFlowExecutionMessages(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFlowExecutionMessagesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result searchEntities(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchEntitiesAsync(array $args = [])
 * @method \Aws\Result searchFlowExecutions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchFlowExecutionsAsync(array $args = [])
 * @method \Aws\Result searchFlowTemplates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchFlowTemplatesAsync(array $args = [])
 * @method \Aws\Result searchSystemInstances(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchSystemInstancesAsync(array $args = [])
 * @method \Aws\Result searchSystemTemplates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchSystemTemplatesAsync(array $args = [])
 * @method \Aws\Result searchThings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchThingsAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result undeploySystemInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise undeploySystemInstanceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateFlowTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFlowTemplateAsync(array $args = [])
 * @method \Aws\Result updateSystemTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSystemTemplateAsync(array $args = [])
 * @method \Aws\Result uploadEntityDefinitions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise uploadEntityDefinitionsAsync(array $args = [])
 */
class IoTThingsGraphClient extends AwsClient {}
