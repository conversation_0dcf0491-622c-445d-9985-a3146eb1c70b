<?php
/* Smarty version 3.1.33, created on 2022-02-07 14:15:11
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/detect/add.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_6200b8ef93fc72_51037438',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '38cae8c1cfc6aa4c095a2ebb5d3140ff98f7c2f5' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/detect/add.tpl',
      1 => 1571978278,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/main.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:admin/footer.tpl' => 1,
    'file:table/js_1.tpl' => 1,
  ),
),false)) {
function content_6200b8ef93fc72_51037438 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Create Detect rule</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/detect">审计系统</a></li>
                  <li class="breadcrumb-item active" aria-current="page">审计规则添加</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/user" class="btn btn-sm btn-neutral">用户</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">审计规则添加</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">规则名称:</label>
							<input id="name" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">规则描述:</label>
							<input id="text" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">规则正则表达式:</label>
							<input id="regex" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">规则类型:</label>
							<select id="type" class="form-control form-control-sm" name="type">
							    <option value="1">数据包明文匹配</option>
                                <option value="2">数据包 hex 匹配</option>
                            </select>
						</div>
					</div>
					<div class="modal-footer">
						<button id="submit" type="button" class="btn btn-primary">确认提交</button>
					</div>
				</div>
			</div>
			
        </div>
      </div><!--row-->
       
	 
		<?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
       
	  <?php $_smarty_tpl->_subTemplateRender('file:admin/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  
<?php echo '<script'; ?>
>
    <?php $_smarty_tpl->_subTemplateRender('file:table/js_1.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

    function delete_modal_show(id) {
        deleteid = id;
        $("#delete_modal").modal();
    }


    window.addEventListener('load', () => {

        function submit() {
             $.ajax({
                type: "POST",
                url: "/admin/detect",
                dataType: "json",
                data: {
                    name: $$getValue("name"),
                    text: $$getValue("text"),
                    regex: $$getValue("regex"),
                    type: $$getValue("type")
                },
                success: data => {
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $('#msg').html("发生错误："+jqXHR.status);
                }
            });
        }

        $("#submit").on("click", submit);

    })

<?php echo '</script'; ?>
>

<?php }
}
