/* Copyright 2019 Google Inc. All Rights Reserved. */
.goog-te-menu-frame {
	z-index: 10000002;
	position: fixed;
	border: none;
	-moz-box-shadow: 0 3px 8px 2px #999999;
	-webkit-box-shadow: 0 3px 8px 2px #999999;
	box-shadow: 0 3px 8px 2px #999999;
	_position: absolute
}

.goog-te-ftab-frame {
	z-index: 10000000;
	border: none;
	margin: 0
}

.goog-te-gadget {
	font-family: arial;
	font-size: 11px;
	color: #666;
	white-space: nowrap
}

.goog-te-gadget img {
	vertical-align: middle;
	border: none
}

.goog-te-gadget-simple {
	
    display: block;
    font-size: .8rem;
    width: 100%;
    padding: .5rem .38rem;
    transition: all .15s cubic-bezier(.68, -.55, .265, 1.55);
    color: #8898aa;
    border: 1px solid #dee2e6;
    border-radius: .25rem;
    background-color: #fff;
    background-clip: padding-box;
    box-shadow: 0 3px 2px rgba(233, 236, 239, .05);
}
.goog-te-gadget-simple:focus {

    color: #8898aa;
    border-color: #4535b7;
    outline: 0;
    background-color: #fff;
    box-shadow: 0 3px 9px rgba(50, 50, 9, 0), 3px 4px 8px rgba(94, 114, 228, .1);
	
}

.goog-te-gadget-icon {
	margin-left: 2px;
	margin-right: 2px;
	width: 19px;
	height: 19px;
	border: none;
	vertical-align: middle
}

.goog-te-combo {
	margin-left: 4px;
	margin-right: 4px;
	vertical-align: baseline;
	*vertical-align: middle
}

.goog-te-gadget .goog-te-combo {
	margin: 4px 0
}

.goog-logo-link,.goog-logo-link:link,.goog-logo-link:visited,.goog-logo-link:hover,.goog-logo-link:active {
	font-size: 12px;
	font-weight: bold;
	color: #444;
	text-decoration: none
}

.goog-te-banner .goog-logo-link,.goog-close-link {
	display: block;
	margin: 0px 10px
}

.goog-te-banner .goog-logo-link {
	padding-top: 2px;
	padding-left: 4px
}

.goog-te-combo,.goog-te-banner *,.goog-te-ftab *,.goog-te-menu *,.goog-te-menu2 *,.goog-te-balloon * {
	font-family: arial;
	font-size: 10pt
}

.goog-te-banner {
	margin: 0;
	background-color: #e4effb;
	overflow: hidden
}

.goog-te-banner img {
	border: none
}

.goog-te-banner-content {
	color: #000
}

.goog-te-banner-content img {
	vertical-align: middle
}

.goog-te-banner-info {
	color: #666;
	vertical-align: top;
	margin-top: 0px;
	font-size: 7pt
}

.goog-te-banner-margin {
	width: 8px
}

.goog-te-button {
	border-color: #e7e7e7;
	border-style: none solid solid none;
	border-width: 0 1px 1px 0
}

.goog-te-button div {
	border-color: #cccccc #999999 #999999 #cccccc;
	border-right: 1px solid #999999;
	border-style: solid;
	border-width: 1px;
	height: 20px
}

.goog-te-button button {
	background: transparent;
	border: none;
	cursor: pointer;
	height: 20px;
	overflow: hidden;
	margin: 0;
	vertical-align: top;
	white-space: nowrap
}

.goog-te-button button:active {
	background: none repeat scroll 0 0 #cccccc
}

.goog-te-ftab {
	margin: 0px;
	background-color: #fff;
	white-space: nowrap
}

.goog-te-ftab-link {
	text-decoration: none;
	font-weight: bold;
	font-size: 10pt;
	border: 1px outset #888;
	padding: 6px 10px;
	white-space: nowrap;
	position: absolute;
	left: 0px;
	top: 0px
}

.goog-te-ftab-link img {
	margin-left: 2px;
	margin-right: 2px;
	width: 19px;
	height: 19px;
	border: none;
	vertical-align: middle
}

.goog-te-ftab-link span {
	text-decoration: underline;
	margin-left: 2px;
	margin-right: 2px;
	vertical-align: middle
}

.goog-float-top .goog-te-ftab-link {
	padding: 2px 2px;
	border-top-width: 0px
}

.goog-float-bottom .goog-te-ftab-link {
	padding: 2px 2px;
	border-bottom-width: 0px
}

.goog-te-menu-value {
	text-decoration: none;
	color: #0000cc;
	white-space: nowrap;
	margin-left: 4px;
	margin-right: 4px
}

.goog-te-menu-value span {
	text-decoration: underline
}

.goog-te-menu-value img {
	margin-left: 2px;
	margin-right: 2px
}

.goog-te-gadget-simple .goog-te-menu-value {
	color: #000
}

.goog-te-gadget-simple .goog-te-menu-value span {
	text-decoration: none
}

.goog-te-menu {
	background-color: #ffffff;
	text-decoration: none;
	border: 2px solid #c3d9ff;
	overflow-y: scroll;
	overflow-x: hidden;
	position: absolute;
	left: 0;
	top: 0
}

.goog-te-menu-item {
	padding: 3px;
	text-decoration: none
}

.goog-te-menu-item,.goog-te-menu-item:link {
	color: #0000cc;
	background: #ffffff
}

.goog-te-menu-item:visited {
	color: #551a8b
}

.goog-te-menu-item:hover {
	background: #c3d9ff
}

.goog-te-menu-item:active {
	color: #0000cc
}

.goog-te-menu2 {
    padding: 1rem;
    animation: show-navbar-collapse .2s ease forwards;
    border-radius: .25rem;
    background: #fff;
    box-shadow: 0 50px 100px rgba(50, 50, 93, .1), 0 15px 35px rgba(50, 50, 93, .15), 0 5px 15px rgba(0, 0, 0, .1);
}

.goog-te-menu2-colpad {
	width: 16px
}

.goog-te-menu2-separator {
	margin: 6px 0;
	height: 1px;
	background-color: #aaa;
	overflow: hidden
}

.goog-te-menu2-item div,.goog-te-menu2-item-selected div {
	padding: 4px
}

.goog-te-menu2-item .indicator {
	display: none
}

.goog-te-menu2-item-selected .indicator {
	display: auto
}

.goog-te-menu2-item-selected .text {
	padding-left: 4px;
	padding-right: 4px
}

.goog-te-menu2-item,.goog-te-menu2-item-selected {
	text-decoration: none
}

.goog-te-menu2-item div,.goog-te-menu2-item:link div,.goog-te-menu2-item:visited div,.goog-te-menu2-item:active div {
	color: #0000cc;
	background: #ffffff
}

.goog-te-menu2-item:hover div {
	color: #ffffff;
	background: #3366cc
}

.goog-te-menu2-item-selected div,.goog-te-menu2-item-selected:link div,.goog-te-menu2-item-selected:visited div,.goog-te-menu2-item-selected:hover div,.goog-te-menu2-item-selected:active div {
	color: #000;
	font-weight: bold
}

.goog-te-balloon {
	background-color: #ffffff;
	overflow: hidden;
	padding: 8px;
	border: none;
	-moz-border-radius: 10px;
	border-radius: 10px
}

.goog-te-balloon-frame {
	background-color: #ffffff;
	border: 1px solid #6b90da;
	-moz-box-shadow: 0 3px 8px 2px #999999;
	-webkit-box-shadow: 0 3px 8px 2px #999999;
	box-shadow: 0 3px 8px 2px #999999;
	-moz-border-radius: 8px;
	border-radius: 8px
}

.goog-te-balloon img {
	border: none
}

.goog-te-balloon-text {
	margin-top: 6px
}

.goog-te-balloon-zippy {
	margin-top: 6px;
	white-space: nowrap
}

.goog-te-balloon-zippy * {
	vertical-align: middle
}

.goog-te-balloon-zippy .minus {
	background-image: url(https://www.google.com/images/zippy_minus_sm.gif)
}

.goog-te-balloon-zippy .plus {
	background-image: url(https://www.google.com/images/zippy_plus_sm.gif)
}

.goog-te-balloon-zippy span {
	color: #00c;
	text-decoration: underline;
	cursor: pointer;
	margin: 0 4px
}

.goog-te-balloon-form {
	margin: 6px 0 0 0
}

.goog-te-balloon-form form {
	margin: 0
}

.goog-te-balloon-form form textarea {
	margin-bottom: 4px;
	width: 100%
}

.goog-te-balloon-footer {
	margin: 6px 0 4px 0
}

.goog-te-spinner-pos {
	z-index: 1000;
	position: fixed;
	transition-delay: 0.6s;
	left: -1000px;
	top: -1000px
}

.goog-te-spinner-animation {
	background: #ccc;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 104px;
	height: 104px;
	border-radius: 50px;
	background: #fff url(https://www.gstatic.com/images/branding/product/2x/translate_24dp.png) 50% 50% no-repeat;
	transition: all 0.6s ease-in-out;
	transform: scale(0.4);
	opacity: 0
}

.goog-te-spinner-animation-show {
	transform: scale(0.5);
	opacity: 1
}

.goog-te-spinner {
	margin: 2px 0 0 2px;
	animation: goog-te-spinner-rotator 1.4s linear infinite
}

@keyframes goog-te-spinner-rotator {
	0% {
		transform: rotate(0deg)
	}

	100% {
		transform: rotate(270deg)
	}
}

.goog-te-spinner-path {
	stroke-dasharray: 187;
	stroke-dashoffset: 0;
	stroke: #4285f4;
	transform-origin: center;
	animation: goog-te-spinner-dash 1.4s ease-in-out infinite
}

@keyframes goog-te-spinner-dash {
	0% {
		stroke-dashoffset: 187
	}

	50% {
		stroke-dashoffset: 46.75;
		transform: rotate(135deg)
	}

	100% {
		stroke-dashoffset: 187;
		transform: rotate(450deg)
	}
}

#goog-gt-tt html,#goog-gt-tt body,#goog-gt-tt div,#goog-gt-tt span,#goog-gt-tt iframe,#goog-gt-tt h1,#goog-gt-tt h2,#goog-gt-tt h3,#goog-gt-tt h4,#goog-gt-tt h5,#goog-gt-tt h6,#goog-gt-tt p,#goog-gt-tt a,#goog-gt-tt img,#goog-gt-tt ol,#goog-gt-tt ul,#goog-gt-tt li,#goog-gt-tt table,#goog-gt-tt form,#goog-gt-tt tbody,#goog-gt-tt tr,#goog-gt-tt td {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
	text-align: left;
	line-height: normal
}

#goog-gt-tt ol,#goog-gt-tt ul {
	list-style: none
}

#goog-gt-tt table {
	border-collapse: collapse;
	border-spacing: 0
}

#goog-gt-tt caption,#goog-gt-tt th,#goog-gt-tt td {
	text-align: left;
	font-weight: normal
}

#goog-gt-tt input::-moz-focus-inner {
	border: 0
}

div#goog-gt-tt {
	padding: 10px 14px
}

#goog-gt-tt {
	color: #222;
	background-color: #ffffff;
	border: 1px solid #eee;
	box-shadow: 0 4px 16px rgba(0,0,0,.2);
	-moz-box-shadow: 0 4px 16px rgba(0,0,0,.2);
	-webkit-box-shadow: 0 4px 16px rgba(0,0,0,.2);
	display: none;
	font-family: arial;
	font-size: 10pt;
	width: 420px;
	padding: 12px;
	position: absolute;
	z-index: 10000
}

#goog-gt-tt .original-text,.gt-hl-layer {
	clear: both;
	font-size: 10pt;
	position: relative;
	text-align: justify;
	width: 100%
}

#goog-gt-tt .title {
	color: #999;
	font-family: arial,sans-serif;
	margin: 4px 0;
	text-align: left
}

#goog-gt-tt .close-button {
	display: none
}

#goog-gt-tt .logo {
	float: left;
	margin: 0px
}

#goog-gt-tt .activity-links {
	display: inline-block
}

#goog-gt-tt .started-activity-container {
	display: none;
	width: 100%
}

#goog-gt-tt .activity-root {
	margin-top: 20px
}

#goog-gt-tt .left {
	float: left
}

#goog-gt-tt .right {
	float: right
}

#goog-gt-tt .bottom {
	min-height: 15px;
	position: relative;
	height: 1%
}

#goog-gt-tt .status-message {
	background: -moz-linear-gradient(top,#29910d 0%,#20af0e 100%);
	background: -webkit-linear-gradient(top,#29910d 0%,#20af0e 100%);
	background: -o-linear-gradient(top,#29910d 0%,#20af0e 100%);
	background: -ms-linear-gradient(top,#29910d 0%,#20af0e 100%);
	background: linear-gradient(top,#29910d 0%,#20af0e 100%);
	background: #29910d;
	border-radius: 4px;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
	box-shadow: inset 0px 2px 2px #1e6609;
	-moz-box-shadow: inset 0px 2px 2px #1e6609;
	-webkit-box-shadow: inset 0px 2px 2px #1e6609;
	color: white;
	font-size: 9pt;
	font-weight: bolder;
	margin-top: 12px;
	padding: 6px;
	text-shadow: 1px 1px 1px #1e6609
}

#goog-gt-tt .activity-link {
	color: #1155cc;
	cursor: pointer;
	font-family: arial;
	font-size: 11px;
	margin-right: 15px;
	text-decoration: none
}

#goog-gt-tt textarea {
	font-family: arial;
	resize: vertical;
	width: 100%;
	margin-bottom: 10px;
	-webkit-border-radius: 1px;
	-moz-border-radius: 1px;
	border-radius: 1px;
	border: 1px solid #d9d9d9;
	border-top: 1px solid silver;
	font-size: 13px;
	height: auto;
	overflow-y: auto;
	padding: 1px
}

#goog-gt-tt textarea:focus {
	-webkit-box-shadow: inset 0 1px 2px rgba(0,0,0,0.3);
	-moz-box-shadow: inset 0 1px 2px rgba(0,0,0,0.3);
	box-shadow: inset 0 1px 2px rgba(0,0,0,0.3);
	border: 1px solid #4d90fe;
	outline: none
}

#goog-gt-tt .activity-cancel {
	margin-right: 10px
}

#goog-gt-tt .translate-form {
	min-height: 25px;
	vertical-align: middle;
	padding-top: 8px
}

#goog-gt-tt .translate-form .activity-form {
	margin-bottom: 5px;
	margin-bottom: 0px
}

#goog-gt-tt .translate-form .activity-form input {
	display: inline-block;
	min-width: 54px;
	*min-width: 70px;
	border: 1px solid #dcdcdc;
	border: 1px solid rgba(0,0,0,0.1);
	text-align: center;
	color: #444;
	font-size: 11px;
	font-weight: bold;
	height: 27px;
	outline: 0;
	padding: 0 8px;
	vertical-align: middle;
	line-height: 27px;
	margin: 0 16px 0 0;
	box-shadow: 0 1px 2px rgba(0,0,0,.1);
	-moz-box-shadow: 0 1px 2px rgba(0,0,0,.1);
	-webkit-box-shadow: 0 1px 2px rgba(0,0,0,.1);
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	-webkit-transition: all 0.218s;
	-moz-transition: all 0.218s;
	-o-transition: all 0.218s;
	transition: all 0.218s;
	background-color: #f5f5f5;
	background-image: -webkit-gradient(linear,left top,left bottom,from(#f5f5f5),to(#f1f1f1));
	background-image: -webkit-linear-gradient(top,#f5f5f5,#f1f1f1);
	background-image: -moz-linear-gradient(top,#f5f5f5,#f1f1f1);
	background-image: -ms-linear-gradient(top,#f5f5f5,#f1f1f1);
	background-image: -o-linear-gradient(top,#f5f5f5,#f1f1f1);
	background-image: linear-gradient(top,#f5f5f5,#f1f1f1);
	-webkit-user-select: none;
	-moz-user-select: none;
	cursor: default
}

#goog-gt-tt .translate-form .activity-form input:hover {
	border: 1px solid #c6c6c6;
	color: #222;
	-webkit-transition: all 0.0s;
	-moz-transition: all 0.0s;
	-o-transition: all 0.0s;
	transition: all 0.0s;
	background-color: #f8f8f8;
	background-image: -webkit-gradient(linear,left top,left bottom,from(#f8f8f8),to(#f1f1f1));
	background-image: -webkit-linear-gradient(top,#f8f8f8,#f1f1f1);
	background-image: -moz-linear-gradient(top,#f8f8f8,#f1f1f1);
	background-image: -ms-linear-gradient(top,#f8f8f8,#f1f1f1);
	background-image: -o-linear-gradient(top,#f8f8f8,#f1f1f1);
	background-image: linear-gradient(top,#f8f8f8,#f1f1f1)
}

#goog-gt-tt .translate-form .activity-form input:active {
	border: 1px solid #c6c6c6;
	color: #333;
	background-color: #f6f6f6;
	background-image: -webkit-gradient(linear,left top,left bottom,from(#f6f6f6),to(#f1f1f1));
	background-image: -webkit-linear-gradient(top,#f6f6f6,#f1f1f1);
	background-image: -moz-linear-gradient(top,#f6f6f6,#f1f1f1);
	background-image: -ms-linear-gradient(top,#f6f6f6,#f1f1f1);
	background-image: -o-linear-gradient(top,#f6f6f6,#f1f1f1);
	background-image: linear-gradient(top,#f6f6f6,#f1f1f1)
}

#goog-gt-tt .translate-form .activity-form input:focus #goog-gt-tt .translate-form .activity-form input.focus #goog-gt-tt .translate-form .activity-form input:active,#goog-gt-tt .translate-form .activity-form input:focus:active,#goog-gt-tt .translate-form .activity-form input:.focus:active {
	box-shadow: inset 0 0 0 1px rgba(255,255,255,0.5);
	-webkit-box-shadow: inset 0 0 0 1px rgba(255,255,255,0.5);
	-moz-box-shadow: inset 0 0 0 1px rgba(255,255,255,0.5)
}

#goog-gt-tt .translate-form .activity-form input:focus,#goog-gt-tt .translate-form .activity-form input.focus {
	outline: none;
	border: 1px solid #4d90fe;
	z-index: 4!important
}

#goog-gt-tt .translate-form .activity-form input.selected {
	background-color: #eeeeee;
	background-image: -webkit-gradient(linear,left top,left bottom,from(#eeeeee),to(#e0e0e0));
	background-image: -webkit-linear-gradient(top,#eeeeee,#e0e0e0);
	background-image: -moz-linear-gradient(top,#eeeeee,#e0e0e0);
	background-image: -ms-linear-gradient(top,#eeeeee,#e0e0e0);
	background-image: -o-linear-gradient(top,#eeeeee,#e0e0e0);
	background-image: linear-gradient(top,#eeeeee,#e0e0e0);
	-webkit-box-shadow: inset 0px 1px 2px rgba(0,0,0,0.1);
	-moz-box-shadow: inset 0px 1px 2px rgba(0,0,0,0.1);
	box-shadow: inset 0px 1px 2px rgba(0,0,0,0.1);
	border: 1px solid #ccc;
	color: #333
}

#goog-gt-tt .translate-form .activity-form input.activity-submit {
	color: white;
	border-color: #3079ed;
	background-color: #4d90fe;
	background-image: -webkit-gradient(linear,left top,left bottom,from(#4d90fe),to(#4787ed));
	background-image: -webkit-linear-gradient(top,#4d90fe,#4787ed);
	background-image: -moz-linear-gradient(top,#4d90fe,#4787ed);
	background-image: -ms-linear-gradient(top,#4d90fe,#4787ed);
	background-image: -o-linear-gradient(top,#4d90fe,#4787ed);
	background-image: linear-gradient(top,#4d90fe,#4787ed)
}

#goog-gt-tt .translate-form .activity-form input.activity-submit:hover #goog-gt-tt .translate-form .activity-form input.activity-submit:focus,#goog-gt-tt .translate-form .activity-form input.activity-submit.focus #goog-gt-tt .translate-form .activity-form input.activity-submit:active {
	border-color: #3079ed;
	background-color: #357ae8;
	background-image: -webkit-gradient(linear,left top,left bottom,from(#4d90fe),to(#357ae8));
	background-image: -webkit-linear-gradient(top,#4d90fe,#357ae8);
	background-image: -moz-linear-gradient(top,#4d90fe,#357ae8);
	background-image: -ms-linear-gradient(top,#4d90fe,#357ae8);
	background-image: -o-linear-gradient(top,#4d90fe,#357ae8);
	background-image: linear-gradient(top,#4d90fe,#357ae8)
}

#goog-gt-tt .translate-form .activity-form input.activity-submit:hover {
	box-shadow: inset 0 0 0 1px #fff,0px 1px 1px rgba(0,0,0,0.1);
	-webkit-box-shadow: inset 0 0 0 1px #fff,0px 1px 1px rgba(0,0,0,0.1);
	-moz-box-shadow: inset 0 0 0 1px #fff,0px 1px 1px rgba(0,0,0,0.1)
}

#goog-gt-tt .translate-form .activity-form input:focus,#goog-gt-tt .translate-form .activity-form input.focus,#goog-gt-tt .translate-form .activity-form input:active,#goog-gt-tt .translate-form .activity-form input:hover,#goog-gt-tt .translate-form .activity-form input.activity-submit:focus,#goog-gt-tt .translate-form .activity-form input.activity-submit.focus,#goog-gt-tt .translate-form .activity-form input.activity-submit:active,#goog-gt-tt .translate-form .activity-form input.activity-submit:hover {
	border-color: #3079ed
}

#goog-gt-tt .gray {
	color: #999;
	font-family: arial,sans-serif
}

#goog-gt-tt .alt-helper-text {
	color: #999;
	font-size: 11px;
	font-family: arial,sans-serif;
	margin: 15px 0px 5px 0px
}

#goog-gt-tt .alt-error-text {
	color: #800;
	display: none;
	font-size: 9pt
}

.goog-text-highlight {
	background-color: #c9d7f1;
	-webkit-box-shadow: 2px 2px 4px #9999aa;
	-moz-box-shadow: 2px 2px 4px #9999aa;
	box-shadow: 2px 2px 4px #9999aa;
	box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	position: relative
}

#goog-gt-tt .alt-menu.goog-menu {
	background: #ffffff;
	border: 1px solid #dddddd;
	-webkit-box-shadow: 0px 3px 3px #888;
	-moz-box-shadow: 0px 2px 20px #888;
	box-shadow: 0px 2px 4px #99a;
	min-width: 0;
	outline: none;
	padding: 0;
	position: absolute;
	z-index: 2000
}

#goog-gt-tt .alt-menu .goog-menuitem {
	cursor: pointer;
	padding: 2px 5px 5px;
	margin-right: 0px;
	border-style: none
}

#goog-gt-tt .alt-menu div.goog-menuitem:hover {
	background: #ddd
}

#goog-gt-tt .alt-menu .goog-menuitem h1 {
	font-size: 100%;
	font-weight: bold;
	margin: 4px 0px
}

#goog-gt-tt .alt-menu .goog-menuitem strong {
	color: #345aad
}

#goog-gt-tt .goog-submenu-arrow {
	text-align: right;
	position: absolute;
	right: 0;
	left: auto
}

#goog-gt-tt .goog-menuitem-rtl .goog-submenu-arrow {
	text-align: left;
	position: absolute;
	left: 0;
	right: auto
}

#goog-gt-tt .gt-hl-text,#goog-gt-tt .trans-target-highlight {
	background-color: #f1ea00;
	border-radius: 4px;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	-moz-box-shadow: rgba(0,0,0,.5) 3px 3px 4px;
	-webkit-box-shadow: rgba(0,0,0,.5) 3px 3px 4px;
	box-shadow: rgba(0,0,0,.5) 3px 3px 4px;
	box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	color: #f1ea00;
	cursor: pointer;
	margin: -2px -2px -2px -3px;
	padding: 2px 2px 2px 3px;
	position: relative
}

#goog-gt-tt .trans-target-highlight {
	color: #222
}

#goog-gt-tt .gt-hl-layer {
	color: white;
	position: absolute!important
}

#goog-gt-tt .trans-target,#goog-gt-tt .trans-target .trans-target-highlight {
	background-color: #c9d7f1;
	border-radius: 4px 4px 0px 0px;
	-webkit-border-radius: 4px 4px 0px 0px;
	-moz-border-radius: 4px 4px 0px 0px;
	-moz-box-shadow: rgba(0,0,0,.5) 3px 3px 4px;
	-webkit-box-shadow: rgba(0,0,0,.5) 3px 3px 4px;
	box-shadow: rgba(0,0,0,.5) 3px 3px 4px;
	box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	cursor: pointer;
	margin: -2px -2px -2px -3px;
	padding: 2px 2px 3px 3px;
	position: relative
}

#goog-gt-tt span:focus {
	outline: none
}

#goog-gt-tt .trans-edit {
	background-color: transparent;
	border: 1px solid #4d90fe;
	border-radius: 0em;
	-webkit-border-radius: 0em;
	-moz-border-radius: 0em;
	margin: -2px;
	padding: 1px
}

#goog-gt-tt .gt-trans-highlight-l {
	border-left: 2px solid red;
	margin-left: -2px
}

#goog-gt-tt .gt-trans-highlight-r {
	border-right: 2px solid red;
	margin-right: -2px
}

#goog-gt-tt #alt-input {
	padding: 2px
}

#goog-gt-tt #alt-input-text {
	font-size: 11px;
	padding: 2px 2px 3px;
	margin: 0;
	background-color: #fff;
	color: #333;
	border: 1px solid #d9d9d9;
	border-top: 1px solid #c0c0c0;
	display: inline-block;
	vertical-align: top;
	height: 21px;
	box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-border-radius: 1px
}

#goog-gt-tt #alt-input-text:hover {
	border: 1px solid #b9b9b9;
	border-top: 1px solid #a0a0a0;
	-webkit-box-shadow: inset 0px 1px 2px rgba(0,0,0,0.1);
	-moz-box-shadow: inset 0px 1px 2px rgba(0,0,0,0.1);
	box-shadow: inset 0px 1px 2px rgba(0,0,0,0.1)
}

#goog-gt-tt #alt-input-text:focus {
	-webkit-box-shadow: inset 0px 1px 2px rgba(0,0,0,0.3);
	-moz-box-shadow: inset 0px 1px 2px rgba(0,0,0,0.3);
	box-shadow: inset 0px 1px 2px rgba(0,0,0,0.3);
	outline: none;
	border: 1px solid #4d90fe
}

#goog-gt-tt #alt-input-submit {
	font-size: 11px;
	padding: 2px 6px 3px;
	margin: 0 0 0 2px;
	height: 21px
}