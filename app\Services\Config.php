<?php

namespace App\Services;

class Config
{
    public static function get($key)
    {
        global $System_Config;
        return $System_Config[$key];
    }

    public static function set($key, $value)
    {
        global $System_Config;
        $System_Config[$key] = $value;
    }

    public static function getPublicConfig()
    {
        return [
            "appName" => self::get("appName"),
		  "description" => self::get("description"),
          "keywords" => self::get("keywords"),
		  "site-description" => self::get("site-description"),
          "engname" => self::get("engname"),
          "verify" => self::get("verify"),
          	"telegram_channel_name" => self::get("telegram_channel_name"),
          	"telegram_channel_link" => self::get("telegram_channel_link"),
          	"telegram_group_name" => self::get("telegram_group_name"),
          	"telegram_group_link" => self::get("telegram_group_link"),
          	"notice_dialog" => self::get("notice_dialog"),
          	"notice_massages" => self::get("notice_massages"),
            "version" => VERSION,
          "CloseSite" => self::get("CloseSite"),
          "enable_tg" => self::get("enable_tg"),
            "baseUrl" => self::get("baseUrl"),
          "stop_subUrl" => self::get("stop_subUrl"),
          "stop_apiUrl" => self::get("stop_apiUrl"),
          "subUrl" => self::get("subUrl"),
		  "newtheme_date" => self::get("newtheme_date"),
          "subscribeLog" => self::get("subscribeLog"),
          "subscribeLog_keep_days" => self::get("subscribeLog_keep_days"),
			"min_port" => self::get("min_port"),
			"max_port" => self::get("max_port"),
            "checkinMin" => self::get("checkinMin"),
            "checkinMax" => self::get("checkinMax"),
			"invite_price"=>self::get("invite_price"),
			"invite_get_money"=>self::get("invite_get_money"),
          "invite_get_traffic"=>self::get("invite_get_traffic"),
            "code_payback" => self::get("code_payback"),
			"invite_gift"=>self::get("invite_gift"),
          "invite_money"=>self::get("invite_money"),
          "invite_class"=>self::get("invite_class"),
          "shop_traffic"=>self::get("shop_traffic"),
          "show_free_nodes"=>self::get("show_free_nodes"),
          "shop_free" => self::get("shop_free"),
          "shop_odm" => self::get("shop_odm"),
          "refund" => self::get("refund"),
          "refund_rate" => self::get("refund_rate"),
			"port_price" => self::get("port_price"),
			"port_price_specify" => self::get("port_price_specify"),
            "jump_delay" => self::get("jump_delay"),
            "enable_analytics_code" => self::get("enable_analytics_code"),
            "enable_donate" => self::get("enable_donate"),
            "enable_telegram" => self::get("enable_telegram"),
            "payment_system" => self::get("payment_system"),
          "payment_system_A" => self::get("payment_system_A"),
          "payment_system_B" => self::get("payment_system_B"),
          "bitpay_secret" => self::get("bitpay_secret"),
          "Clientid" => self::get("Clientid"),
          "serverName" => self::get('serverName'),
          	"enable_crisp" => self::get("enable_crisp"),
          	"crisp_id" => self::get("crisp_id"),
          	"enable_ticket"=> self::get("enable_ticket"),
          "pay_ticket_price"=> self::get("pay_ticket_price"),
			"enable_admin_contact" => self::get("enable_admin_contact"),
			"admin_contact1" => self::get("admin_contact1"),
			"admin_contact2" => self::get("admin_contact2"),
			"admin_contact3" => self::get("admin_contact3"),
          "admin_qq_bottom" => self::get("admin_qq_bottom"),
			"register_mode" => self::get("register_mode"),
          "defaultTraffic" => self::get("defaultTraffic"),
            "enable_flag" => self::get("enable_flag"),
            "enable_kill" => self::get("enable_kill"),
            "custom_invite_price" => self::get("custom_invite_price"),
            "telegram_grouplink" => self::get("telegram_grouplink"),
            "captcha_provider" => self::get("captcha_provider"),
			"CaptchaDX_AppId" => self::get("CaptchaDX_AppId"),
            "enable_email_verify" => self::get("enable_email_verify"),
          "enable_auto_detect_ban" => self::get("enable_auto_detect_ban"),
          "auto_detect_ban_type" => self::get("auto_detect_ban_type"),
          "auto_detect_ban_number" => self::get("auto_detect_ban_number"),
          "auto_detect_ban_time" => self::get("auto_detect_ban_time"),
          "auto_detect_ban" => self::get("auto_detect_ban"),
          "subscribe_client" => self::get("subscribe_client"),
          "nolocaltions" => self::get("nolocaltions")
         ];
    }

    public static function getDbConfig()
    {
        return [
            'driver'    => self::get('db_driver'),
            'host'      => self::get('db_host'),
            'database'  => self::get('db_database'),
            'username'  => self::get('db_username'),
            'password'  => self::get('db_password'),
            'charset'   => self::get('db_charset'),
            'collation' => self::get('db_collation'),
            'prefix'    => self::get('db_prefix')
        ];
    }

    public static function getRadiusDbConfig()
    {
        return [
            'driver'    => self::get('db_driver'),
            'host'      => self::get('radius_db_host'),
            'database'  => self::get('radius_db_database'),
            'username'  => self::get('radius_db_user'),
            'password'  => self::get('radius_db_password'),
            'charset'   => self::get('db_charset'),
            'collation' => self::get('db_collation')
        ];
    }

    public static function getMuKey()
    {
        global $System_Config;
        $muKeyList = array_key_exists('muKeyList', $System_Config) ? $System_Config['muKeyList'] : ['　'];
        return array_merge(explode(',', $System_Config['muKey']), $muKeyList);
    }

    public static function getSupportParam($type)
    {
        switch ($type) {
            case 'obfs':
                $list = array(
                    'plain',
                    'http_simple',
                    'http_simple_compatible',
                    'http_post',
                    'http_post_compatible',
                    'tls1.2_ticket_auth',
                    'tls1.2_ticket_auth_compatible',
                    'tls1.2_ticket_fastauth',
                    'tls1.2_ticket_fastauth_compatible',
                    'simple_obfs_http',
                    'simple_obfs_http_compatible',
                    'simple_obfs_tls',
                    'simple_obfs_tls_compatible'
                );
                return $list;
            case 'protocol':
                $list = array(
                    'origin',
                    'verify_deflate',
                    'auth_sha1_v4',
                    'auth_sha1_v4_compatible',
                    'auth_aes128_sha1',
                    'auth_aes128_md5',
                    'auth_chain_a',
                    'auth_chain_b',
                    'auth_chain_c',
                    'auth_chain_d',
                    'auth_chain_e',
                    'auth_chain_f'
                );
                return $list;
            case 'allow_none_protocol':
                $list = array(
                    'auth_chain_a',
                    'auth_chain_b',
                    'auth_chain_c',
                    'auth_chain_d',
                    'auth_chain_e',
                    'auth_chain_f'
                );
                return $list;
            case 'relay_able_protocol':
                $list = array(
                    'auth_aes128_md5',
                    'auth_aes128_sha1',
                    'auth_chain_a',
                    'auth_chain_b',
                    'auth_chain_c',
                    'auth_chain_d',
                    'auth_chain_e',
                    'auth_chain_f'
                );
                return $list;
            case 'ss_aead_method':
                $list = array(
                    'aes-128-gcm',
                    'aes-192-gcm',
                    'aes-256-gcm',
                    'chacha20-ietf-poly1305',
                    'xchacha20-ietf-poly1305'
                );
                return $list;
            case 'ss_obfs':
                $list = array(
                    'simple_obfs_http',
                    'simple_obfs_http_compatible',
                    'simple_obfs_tls',
                    'simple_obfs_tls_compatible'
                );
                return $list;
            default:
                $list = array(
                    'rc4-md5',
                    'rc4-md5-6',
                    'aes-128-cfb',
                    'aes-192-cfb',
                    'aes-256-cfb',
                    'aes-128-ctr',
                    'aes-192-ctr',
                    'aes-256-ctr',
                    'camellia-128-cfb',
                    'camellia-192-cfb',
                    'camellia-256-cfb',
                    'bf-cfb',
                    'cast5-cfb',
                    'des-cfb',
                    'des-ede3-cfb',
                    'idea-cfb',
                    'rc2-cfb',
                    'seed-cfb',
                    'salsa20',
                    'chacha20',
                    'xsalsa20',
                    'chacha20-ietf',
                    'aes-128-gcm',
                    'aes-192-gcm',
                    'none',
                    'aes-256-gcm',
                    'chacha20-ietf-poly1305',
                    'xchacha20-ietf-poly1305'
                );
                return $list;
        }
    }
}