<?php
/* Smarty version 3.1.33, created on 2022-02-18 12:15:16
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/ticket/ticket_view.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_620f1d540d5c36_72818851',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '7f865d5e87f0ec9fc20a6255a27e132e0c3e7a7f' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/ticket/ticket_view.tpl',
      1 => 1588141002,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:user/main.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:user/footer.tpl' => 1,
  ),
),false)) {
function content_620f1d540d5c36_72818851 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:user/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<link rel="stylesheet" href="/theme/czssr/main/css/quill.core.css" type="text/css">
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Tickets</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
				  <li class="breadcrumb-item"><a href="/user/ticket">工单系统</a></li>
                  <li class="breadcrumb-item active" aria-current="page">查看工单</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/shop" class="btn btn-sm btn-neutral">商店购买</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">查看工单</h3>
              </div>
              <!-- Card body -->
				<div class="bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group mt-3">
							<label class="form-control-label">内容(点击上传图片后会自动上传比较慢等缓存,别重复点)&nbsp;:&nbsp;</label>
							<div id="editor" data-toggle="quill">
			
							</div>
						</div>
                      <div class="modal-footer">
						<button id="submit" type="button" class="btn btn-primary">添加</button>
						<button id="close" type="button" class="btn btn-Secondary">添加并关闭</button>
						<button id="close_directly" type="button" class="btn btn-Secondary">直接关闭</button>
					  </div>
					</div>
					<div class="card-body">
					<?php echo $_smarty_tpl->tpl_vars['ticketset']->value->render();?>

						<div class="accordion" id="accordionExample">
						<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['ticketset']->value, 'ticket');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['ticket']->value) {
?>
							<div class="card">
								<div class="card-header" id="heading" data-toggle="collapse" data-target="#collapse<?php echo $_smarty_tpl->tpl_vars['ticket']->value->id;?>
" aria-expanded="false" aria-controls="collapseOne">
								  <?php if ($_smarty_tpl->tpl_vars['ticketset_nums']->value+1 == $_smarty_tpl->tpl_vars['ticket']->value->id) {?>
                                  <h5 class="mb-0 badge-dot mr-4"><i class="bg-success"></i> <?php echo $_smarty_tpl->tpl_vars['ticket']->value->datetime();?>
 # <?php echo $_smarty_tpl->tpl_vars['ticket']->value->User()->user_name;?>
</h5>
                                  <?php } else { ?>
                                  <h5 class="mb-0"> <?php echo $_smarty_tpl->tpl_vars['ticket']->value->datetime();?>
 # <?php echo $_smarty_tpl->tpl_vars['ticket']->value->User()->user_name;?>
</h5>
                                  <?php }?>
								</div>
								<div id="collapse<?php echo $_smarty_tpl->tpl_vars['ticket']->value->id;?>
" class="collapse" aria-labelledby="heading" data-parent="#accordionExample">
									<div class="card-body col-md-auto">
									<?php echo $_smarty_tpl->tpl_vars['ticket']->value->content;?>

									</div>
								</div>
							</div>
						<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
						</div>
					<?php echo $_smarty_tpl->tpl_vars['ticketset']->value->render();?>
	
					</div>
				</div>
			</div>
		
        </div>
      </div><!--row-->
	  
	<?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	<?php $_smarty_tpl->_subTemplateRender('file:user/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
echo '<script'; ?>
 src="/theme/czssr/main/js/quill.min.js"><?php echo '</script'; ?>
>	  

<?php echo '<script'; ?>
>
// Quill.js

'use strict';

var QuillEditor = (function() {

	// Variables
	var $quill = $('[data-toggle="quill"]');

	// Methods

	function init($this) {

		// Get placeholder
		var placeholder = $this.data('quill-placeholder');

		// Init editor
		var quill = new Quill('#editor', {
			modules: {
				toolbar: [
					['bold', 'italic'],
					['link', 'blockquote', 'code', 'image'],
					[{
						'list': 'ordered'
					}, {
						'list': 'bullet'
					}]
				]
			},
			placeholder: placeholder,
			theme: 'snow'
		});

	}
        
	// Events

	if ($quill.length) {
		$quill.each(function() {
			init($(this));
		});
	}

})();
  
<?php echo '</script'; ?>
>
		
<?php echo '<script'; ?>
>
    $(document).ready(function () {
        function submit() {
		
            $("#result").modal();
            $$.getElementById('msg').innerHTML = '正在提交';
			
			var quill_html = document.querySelector('#editor').children[0].innerHTML;
			
            $.ajax({
                type: "PUT",
                url: "/user/ticket/<?php echo $_smarty_tpl->tpl_vars['id']->value;?>
",
                dataType: "json",
                data: {
                    content: quill_html,
                    status
                },
                success: function(data){
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href='/user/ticket'", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: function(jqXHR) {
                    $("#result").modal();
                    $("#msg").html(jqXHR+"  发生了错误。");
                }
            });
        }

        $("#submit").click(function () {
            status = 1;
            submit();
        });
		$("#close").click(function () {
            status = 0;
            submit();
        });
		$("#close_directly").click(function () {
            status = 0;
            $("#result").modal();
            $$.getElementById('msg').innerHTML = '正在提交';
            $.ajax({
                type: "PUT",
                url: "/user/ticket/<?php echo $_smarty_tpl->tpl_vars['id']->value;?>
",
                dataType: "json",
                data: {
                    content: '这条工单已被关闭',
                    status
                },
                success: function(data){
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href='/user/ticket'", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: function(jqXHR) {
                    $("#result").modal();
                    $("#msg").html(jqXHR+"  发生了错误。");
                }
            });
        });
    })
<?php echo '</script'; ?>
><?php }
}
