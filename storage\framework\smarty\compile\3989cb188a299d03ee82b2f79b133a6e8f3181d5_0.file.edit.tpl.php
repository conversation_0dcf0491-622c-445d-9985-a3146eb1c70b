<?php
/* Smarty version 3.1.33, created on 2022-02-07 02:51:18
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/user/edit.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_620018a6f3f552_81446579',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '3989cb188a299d03ee82b2f79b133a6e8f3181d5' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/user/edit.tpl',
      1 => 1577770614,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/main.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:admin/footer.tpl' => 1,
  ),
),false)) {
function content_620018a6f3f552_81446579 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Edit User</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/user">用户列表</a></li>
				  <li class="breadcrumb-item active">用户编辑</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/node" class="btn btn-sm btn-neutral">节点</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">用户编辑 #<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->id;?>
</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">邮箱:</label>
							<input id="email" class="form-control form-control-sm" type="email" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->email;?>
">
						</div>
						<div class="form-group">
							<label class="form-control-label">备注(仅对管理员可见):</label>
							<input id="remark" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->remark;?>
">
						</div>
						<div class="form-group">
							<label class="form-control-label">密码(不修改请留空):</label>
							<input id="pass" class="form-control form-control-sm" type="password">
						</div>
						<div class="form-group">
							<label class="custom-toggle icon-ver" for="is_admin">
								<input id="is_admin" type="checkbox" <?php if ($_smarty_tpl->tpl_vars['edit_user']->value->is_admin == 1) {?>checked<?php }?>>
								<span class="custom-toggle-slider rounded-circle" data-label-off="No" data-label-on="Yes"></span>
							</label>&nbsp;&nbsp;是否管理员
						</div>
						<div class="form-group">
							<label class="custom-toggle icon-ver" for="enable">
								<input id="enable" type="checkbox" <?php if ($_smarty_tpl->tpl_vars['edit_user']->value->enable == 1) {?>checked<?php }?>>
								<span class="custom-toggle-slider rounded-circle" data-label-off="No" data-label-on="Yes"></span>
							</label>&nbsp;&nbsp;用户启用
						</div>
						<div class="form-group">
							<label class="custom-toggle icon-ver" for="is_agent">
								<input id="is_agent" type="checkbox" <?php if ($_smarty_tpl->tpl_vars['edit_user']->value->is_agent == 1) {?>checked<?php }?>>
								<span class="custom-toggle-slider rounded-circle" data-label-off="No" data-label-on="Yes"></span>
							</label>&nbsp;&nbsp;是否代理商
						</div>
						<div class="form-group">
							<label class="custom-toggle icon-ver" for="ga_enable">
								<input id="ga_enable" type="checkbox" <?php if ($_smarty_tpl->tpl_vars['edit_user']->value->ga_enable == 1) {?>checked<?php }?>>
								<span class="custom-toggle-slider rounded-circle" data-label-off="No" data-label-on="Yes"></span>
							</label>&nbsp;&nbsp;是否开启二次验证
						</div>
						<div class="form-group">
							<label class="form-control-label">代理商折扣(55代表打4.5折，90代表打1折，99代表打0.1折):</label>
							<input id="creta" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->creta;?>
">
						</div>
						<div class="form-group">
							<label class="form-control-label">邀请返利百分比:</label>
							<input id="payback_code" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->payback_code;?>
">
						</div>
						<div class="form-group">
							<label class="form-control-label">金钱:</label>
							<input id="money" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->money;?>
">
						</div>
						<label class="form-control-label" for="sort">单端口多用户承载端口:</label>
						<select id="is_multi_user" class="form-control form-control-sm" name="is_multi_user">
                            <option value="0" <?php if ($_smarty_tpl->tpl_vars['edit_user']->value->is_multi_user == 0) {?>selected<?php }?>>非单端口多用户承载端口</option>
                            <option value="1" <?php if ($_smarty_tpl->tpl_vars['edit_user']->value->is_multi_user == 1) {?>selected<?php }?>>混淆式单端口多用户承载端口</option>
                            <option value="2" <?php if ($_smarty_tpl->tpl_vars['edit_user']->value->is_multi_user == 2) {?>selected<?php }?>>协议式单端口多用户承载端口</option>
                        </select>
					</div>
				</div>
			
			<div class="card bg-gradient-Secondary">
				<div class="card-body">
					<div class="form-group">
						<label class="form-control-label">连接端口:</label>
						<input id="port" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->port;?>
">
					</div>
					<div class="form-group">
						<label class="form-control-label">连接密码:</label>
						<input id="passwd" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->passwd;?>
">
					</div>
					<div class="form-group">
						<label class="form-control-label">自定义加密:</label>
						<input id="method" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->method;?>
">
					</div>
					<div class="form-group">
						<label class="form-control-label">自定义协议:</label>
						<input id="protocol" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->protocol;?>
">
					</div>
					<div class="form-group">
						<label class="form-control-label">自定义协议参数:</label>
						<input id="protocol_param" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->protocol_param;?>
">
					</div>
					<div class="form-group">
						<label class="form-control-label">自定义混淆方式:</label>
						<input id="obfs" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->obfs;?>
">
					</div>
					<div class="form-group">
						<label class="form-control-label">自定义混淆参数:</label>
						<input id="obfs_param" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->obfs_param;?>
">
					</div>
				</div>
			</div>		
            <div class="card bg-gradient-Secondary">
				<div class="card-body">
					<div class="form-group">
						<label class="form-control-label">总流量GB:</label>
						<input id="transfer_enable" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->enableTrafficInGB();?>
">
					</div>
					<div class="form-group">
						<label class="form-control-label">已用流量:</label>
						<input id="usedTraffic" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->usedTraffic();?>
">
					</div>
					<div class="form-group">
						<label class="form-control-label">自动重置流量日:</label>
						<input id="auto_reset_day" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->auto_reset_day;?>
">
					</div>
					<div class="form-group">
						<label class="form-control-label">重置流量值(GB):</label>
						<input id="auto_reset_bandwidth" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->auto_reset_bandwidth;?>
">
					</div>
				</div>
			</div><!--card-->
			<div class="card bg-gradient-Secondary">
				<div class="card-body">
					<div class="form-group">
						<label class="form-control-label">可用邀请数量:</label>
						<input id="invite_num" class="form-control form-control-sm" type="number" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->invite_num;?>
">
					</div>
					<div class="form-group">
						<label class="form-control-label">邀请人ID:</label>
						<input id="ref_by" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->ref_by;?>
">
					</div>
                  <div class="form-group">
					<label class="form-control-label" for="class">用户等级:</label>
					<select id="class" class="form-control form-control-sm" name="class">
                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['levelList']->value, 'level');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['level']->value) {
?>
						<option value="<?php echo $_smarty_tpl->tpl_vars['level']->value->level;?>
" <?php if ($_smarty_tpl->tpl_vars['level']->value->level == $_smarty_tpl->tpl_vars['edit_user']->value->class) {?>selected<?php }?>><?php echo $_smarty_tpl->tpl_vars['level']->value->name;?>
</option>
                        <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>	
                    </select>
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>用户只能访问到组别等于这个数字或0的节点</p>
				   </div>
                  
					<div class="form-group">
					<label class="form-control-label" for="group">用户群组(等于节点群组):</label>
					<select id="group" class="form-control form-control-sm" name="group">
                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['groupList']->value, 'group');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['group']->value) {
?>
							<option value="<?php echo $_smarty_tpl->tpl_vars['group']->value->level;?>
" <?php if ($_smarty_tpl->tpl_vars['group']->value->level == $_smarty_tpl->tpl_vars['edit_user']->value->node_group) {?>selected<?php }?>><?php echo $_smarty_tpl->tpl_vars['group']->value->name;?>
</option>
						<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>	
                    </select>
					</div>
                  
					<div class="form-group">
						<label class="form-control-label" for="class_expire">用户等级过期时间</label>
						<input class="form-control form-control-sm" id="class_expire" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->class_expire;?>
">
						<p class="description badge-dot mr-4"><i class="bg-warning"></i>不过期就请不要动</p>
					</div>

					<div class="form-group">
						<label class="form-control-label" for="expire_in">用户账户过期时间</label>
						<input class="form-control form-control-sm" id="expire_in" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->expire_in;?>
">
						<p class="description badge-dot mr-4"><i class="bg-warning"></i>显示不过期就请不要动</p>
					</div>
				</div>
			</div>
			<div class="card bg-gradient-Secondary">
				<div class="card-body">
				    <div class="form-group">
						<label class="custom-toggle icon-ver" for="detect_ban">
							<input id="detect_ban" type="checkbox" <?php if ($_smarty_tpl->tpl_vars['edit_user']->value->detect_ban == 1) {?>checked<?php }?>>
							<span class="custom-toggle-slider rounded-circle" data-label-off="No" data-label-on="Yes"></span>
						</label>&nbsp;&nbsp;审计已封禁
					</div>
					<div class="form-group">
						<label class="form-control-label" for="ban_time">手动封禁时长(分钟),不封禁不要修改</label>
						<input class="form-control form-control-sm" id="ban_time" type="text" value="0">
					</div>
					<div class="form-group">
						<label class="form-control-label" for="last_detect_ban_time">最后一次被封禁的时间</label>
						<input class="form-control form-control-sm" id="last_detect_ban_time" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->last_detect_ban_time();?>
" readonly>
					</div>
					<div class="form-group">
						<label class="form-control-label" for="relieve_time">当前解封时间</label>
						<input class="form-control form-control-sm" id="relieve_time" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->relieve_time();?>
" readonly>
					</div>
					<div class="form-group">
						<label class="form-control-label" for="detect_ban_number">累计封禁次数</label>
						<input class="form-control form-control-sm" id="detect_ban_number" type="text" value="<?php if ($_smarty_tpl->tpl_vars['edit_user']->value->detect_ban_number() == 0) {?>好耶！标杆用户，没有被审计封禁过耶<?php } else { ?>太坏了，这位用户累计被封禁过 <?php echo $_smarty_tpl->tpl_vars['edit_user']->value->detect_ban_number();?>
 次呢<?php }?>" readonly>
					</div>
					<div class="form-group">
						<label class="form-control-label" for="all_detect_number">累计违规次数</label>
						<input class="form-control form-control-sm" id="all_detect_number" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->all_detect_number;?>
" readonly>
					</div>
				</div>
			</div>	
			<div class="card bg-gradient-Secondary">
				<div class="card-body">
					<div class="form-group">
						<label class="form-control-label" for="node_speedlimit">用户限速，用户在每个节点所享受到的速度(Mbps)</label>
						<input class="form-control form-control-sm" id="node_speedlimit" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->node_speedlimit;?>
">
						<p class="description badge-dot mr-4"><i class="bg-warning"></i>0 为不限制</p>
					</div>
					<div class="form-group">
						<label class="form-control-label" for="node_connector">用户同时连接 IP 数</label>
						<input class="form-control form-control-sm" id="node_connector" type="text" value="<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->node_connector;?>
">
						<p class="description badge-dot mr-4"><i class="bg-warning"></i>0 为不限制</p>
					</div>
					<div class="form-group">
                        <label class="form-control-label" for="node_speedlimit">禁止用户访问的IP，一行一个</label>
                        <textarea class="form-control" id="forbidden_ip" rows="8"><?php echo $_smarty_tpl->tpl_vars['edit_user']->value->get_forbidden_ip();?>
</textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-control-label" for="node_speedlimit">禁止用户访问的端口，一行一个</label>
                         <textarea class="form-control" id="forbidden_port" rows="8"><?php echo $_smarty_tpl->tpl_vars['edit_user']->value->get_forbidden_port();?>
</textarea>
                    </div>
				</div>
			</div>
			
			<div class="modal-footer">
                <button id="submit" type="button" class="btn btn-primary">确认提交</button>
			</div>
		  </div><!--card-->
        </div>
      </div><!--row-->
	  
		 <?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php $_smarty_tpl->_subTemplateRender('file:admin/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php echo '<script'; ?>
>

    window.addEventListener('load', () => {
    function submit() {
        if (document.getElementById('is_admin').checked) {
            var is_admin = 1;
        } else {
            var is_admin = 0;
        }
        if (document.getElementById('is_agent').checked) {
            var is_agent = 1;
        } else {
            var is_agent = 0;
        }
        if (document.getElementById('enable').checked) {
            var enable = 1;
        } else {
            var enable = 0;
        }

        if (document.getElementById('ga_enable').checked) {
            var ga_enable = 1;
        } else {
            var ga_enable=0;
        }
		
		if(document.getElementById('detect_ban').checked) {
			var detect_ban=1;
		} else {
			var detect_ban=0;
		}

        $.ajax({
            type: "PUT",
            url: "/admin/user/<?php echo $_smarty_tpl->tpl_vars['edit_user']->value->id;?>
",
            dataType: "json",
            data: {
                email: $$getValue('email'),
                pass: $$getValue('pass'),
                auto_reset_day: $$getValue('auto_reset_day'),
                auto_reset_bandwidth: $$getValue('auto_reset_bandwidth'),
                is_multi_user: $$getValue('is_multi_user'),
                port: $$getValue('port'),
                group: $$getValue('group'),
                passwd: $$getValue('passwd'),
                transfer_enable: $$getValue('transfer_enable'),
                invite_num: $$getValue('invite_num'),
                node_speedlimit: $$getValue('node_speedlimit'),
                method: $$getValue('method'),
                remark: $$getValue('remark'),
				payback_code: $$getValue('payback_code'),
                money: $$getValue('money'),
                creta: $$getValue('creta'),
                enable,
                is_admin,
             	is_agent,
                ga_enable,
				detect_ban,
				ban_time: $$getValue('ban_time'),
                ref_by: $$getValue('ref_by'),
                forbidden_ip: $$getValue('forbidden_ip'),
                forbidden_port: $$getValue('forbidden_port'),
                class: $$getValue('class'),
                class_expire: $$getValue('class_expire'),
                expire_in: $$getValue('expire_in'),
                node_connector: $$getValue('node_connector'),
                protocol: $$getValue('protocol'),
                protocol_param: $$getValue('protocol_param'),
                obfs: $$getValue('obfs'),
                obfs_param: $$getValue('obfs_param'),
            },
            success: data => {
                if (data.ret) {
                    $("#result").modal();
                    $('#msg').html(data.msg);
                    window.setTimeout("location.href=top.document.referrer", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                } else {
                    $("#result").modal();
                    $('#msg').html(data.msg);
                }
            },
            error: jqXHR => {
				$("#result").modal();
                $('#msg').html("发生错误："+jqXHR.status);
            }
        });
    }

    $("#submit").on("click", submit);

})
<?php echo '</script'; ?>
><?php }
}
