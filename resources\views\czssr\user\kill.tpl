{include file='user/main.tpl'}
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Delete</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">删除账号</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/ticket/create" class="btn btn-sm btn-neutral">工单</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">删除账号说明</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-danger"></i>帐号删除后，您的所有数据都会被<b>真实地</b>删除.</p>
							<p class="description badge-dot mr-4"><i class="bg-danger"></i>如果想重新使用本网站提供的服务, 您需要重新注册.</p>
							<p class="description badge-dot mr-4"><i class="bg-danger"></i>你是遇到什么麻烦了吗? 为什么要想不开呢? 有问题右上角提交工单先.</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
						<div class="form-group mt-3">
							<label class="form-control-label">输入当前密码以验证身份&nbsp;:&nbsp;</label>
							<input id="passwd" class="form-control form-control-sm" type="password">
						</div>
					</div>
					<div class="modal-footer">
						<button id="kill" type="button" class="btn btn-danger">确认提交</button>
					</div>
				</div>
			</div>
		</div>
	  </div><!--row-->
	  
	  {include file='dialog.tpl'}
	  {include file='user/footer.tpl'}

	  