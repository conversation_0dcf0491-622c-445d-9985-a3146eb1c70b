<?php
namespace App\Controllers\Admin;

use App\Models\User;
use App\Models\Level;
use App\Models\Node;
use App\Models\NodeGroup;
use App\Controllers\AdminController;
use Ozdemir\Datatables\Datatables;
use App\Utils\DatatablesHelper;

class LevelController extends AdminController
{	
    public function index($request, $response, $args)
    {
        $table_config['total_column'] = array("op" => "操作", "id" => "ID",
                              "name" => "等级名称", "level" => "等级级别", "status" => "占用");
        $table_config['default_show_column'] = array("op", "id", "name", "level", "status");
													
        $table_config['ajax_url'] = 'level/ajax';
        return $this->view()->assign('table_config', $table_config)->display('admin/level/index.tpl');
		
    }

    public function create($request, $response, $args)
    {
        return $this->view()->display('admin/level/create.tpl');
    }
	
    public function add($request, $response, $args)
    {	
        $level = new Level();
		$level->name = $request->getParam('name');
        $level->level = $request->getParam('level');
		$level->status = 1;
		$nlevel = Level::where('level', $request->getParam('level'))->where('status', 1)->first();
            if ($nlevel != null) {
                $rs['ret'] = 0;
                $rs['msg'] = "添加失败，目标等级已存在";
                return $response->getBody()->write(json_encode($rs));
            }
            if (!$level->save()) {
                $rs['ret'] = 0;
                $rs['msg'] = "添加失败";
                return $response->getBody()->write(json_encode($rs));
            }
				$rs['ret'] = 1;
				$rs['msg'] = "添加成功, 请在套餐商品/节点分组/节点/用户管理中单独赋予权限。";
			return $response->getBody()->write(json_encode($rs));
	} 
    public function edit($request, $response, $args)
    {
        $id = $args['id'];
        $level = Level::find($id);

        return $this->view()->assign('level', $level)->display('admin/level/edit.tpl');
    }

    public function update($request, $response, $args)
    {
        $id = $args['id'];
        $level = Level::find($id);
        $level->name =  $request->getParam('name');
        $nlevel = $request->getParam('level');
		$level->status = 1;
	    $nglevel = Level::where('level', $request->getParam('level'))->where('status', 1)->first();
      if ($level->level != $request->getParam('level') && $nglevel != null) {
                $rs['ret'] = 0;
                $rs['msg'] = "修改失败，目标等级已存在";
                return $response->getBody()->write(json_encode($rs));
      }
        NodeGroup::where('level', $level->level)->update(['level' => $nlevel]);
		Node::where('node_class', $level->level)->update(['node_class' => $nlevel]);
        User::where('class', $level->level)->update(['class' => $nlevel]);

		$level->level =  $request->getParam('level');
        if (!$level->save()) {
            $rs['ret'] = 0;
            $rs['msg'] = "修改失败";
            return $response->getBody()->write(json_encode($rs));
        }
			$rs['ret'] = 1;
			$rs['msg'] = "修改成功, 请手动修改对应等级的套餐商品。";
        return $response->getBody()->write(json_encode($rs));
    }
	
    public function delete($request, $response, $args)
    {
        $id = $request->getParam('id');
        $level = Level::find($id);
        $nlevel = $level->level;
      
        NodeGroup::where('level', $nlevel)->update(['level' => 0]);
		Node::where('node_class', $nlevel)->update(['node_class' => 0]);
        User::where('class', $nlevel)->update(['class' => 0]);
      
        if (!$level->delete()) {
            $rs['ret'] = 0;
            $rs['msg'] = "删除失败";
            return $response->getBody()->write(json_encode($rs));
        }
        $rs['ret'] = 1;
        $rs['msg'] = "删除成功, 请手动修改对应等级的套餐商品。";
        return $response->getBody()->write(json_encode($rs));
    }

    public function ajax($request, $response, $args)
    {
        $datatables = new Datatables(new DatatablesHelper());
        $datatables->query('Select id as id,name,level,status from ss_level');


		$datatables->edit('op', function ($data) {
            return '<a class="btn btn-brand" href="/admin/level/'.$data['id'].'/edit">编辑</a>
                    <a class="btn btn-brand-accent" '.($data['id'] == 1 ? 'disabled' : 'id="delete" value="'.$data['id'].'" href="javascript:void(0);" onClick="delete_modal_show(\''.$data['id'].'\')"').'>删除</a>';
        });
        $datatables->edit('DT_RowId', function ($data) {
            return 'row_1_'.$data['id'];
        });


        $body = $response->getBody();
        $body->write($datatables->generate());
    }
}
