<!DOCTYPE html>
{if $config['CloseSite'] == 'true'}
<script>window.location.href='{$config["baseUrl"]}/404';</script>
{/if}
<html>
<head>
  <meta charset="UTF-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
  <meta name="author" content="Creative Tim">
  <title>{$config["appName"]}</title>
  <meta name="keywords" content="{$config['keywords']}">
  <meta name="description" content="{$config['description']}">
  <meta name="author" content="hencework"/>
  <!-- Favicon -->
  <link href="/favicon.png" rel="icon" type="image/png">
  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet">
  <link href="/theme/czssr/main/css/font-awesome/css/font-awesome.min.css" rel="stylesheet">
  <!-- Icons -->
  <link rel="stylesheet" href="/theme/czssr/main/css/nucleo.css" type="text/css">
  <link rel="stylesheet" href="/theme/czssr/main/css/sweetalert2.min.css" type="text/css">
  <link type="text/css" href="/theme/czssr/main/css/czssr-index.css?v=1.1.0" rel="stylesheet">
  
<style>

.goog-te-banner-frame{
	display:none
}
  .engname1{
   color: #d1cde5;
   font-size:1.5rem;
   font-weight: bold;
   vertical-align: middle;
   text-transform: capitalize;
  }
  .engname2{
    font-size: 3rem;
    font-weight: bold;
    vertical-align: middle;
    text-transform: capitalize;
  }
  .engname3{
    font-size: 1rem;
    font-weight: bold;
    vertical-align: middle;
    text-transform: capitalize;
  }
</style>
</head>
