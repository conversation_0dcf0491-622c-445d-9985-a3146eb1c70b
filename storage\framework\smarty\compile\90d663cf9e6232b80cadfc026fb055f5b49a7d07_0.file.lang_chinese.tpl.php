<?php
/* Smarty version 3.1.33, created on 2022-02-04 15:37:36
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/table/lang_chinese.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_61fcd7c044ea67_83336018',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '90d663cf9e6232b80cadfc026fb055f5b49a7d07' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/table/lang_chinese.tpl',
      1 => 1571114558,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_61fcd7c044ea67_83336018 (Smarty_Internal_Template $_smarty_tpl) {
?>language: {
        "sProcessing": "处理中...",
        "sLengthMenu": "显示 _MENU_ 项结果",
        "sZeroRecords": "没有匹配结果",
        "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
        "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
        "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
        "sInfoPostFix": "",
        "sSearch": "搜索:",
        "sUrl": "",
        "sEmptyTable": "表中数据为空",
        "sLoadingRecords": "载入中...",
        "sInfoThousands": ",",
        "oPaginate": {
            "sFirst": "首页",
            "sPrevious": "<",
            "sNext": ">",
            "sLast": "末页"
        },
        "oAria": {
            "sSortAscending": ": 以升序排列此列",
            "sSortDescending": ": 以降序排列此列"
        }
    }
<?php }
}
