{include file='user/main.tpl'}
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Payresult</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">充值结果</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/shop" class="btn btn-sm btn-neutral">商店购买</a>
              <a href="/user/node" class="btn btn-sm btn-neutral disable">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">充值结果</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<blockquote class="blockquote mb-0">
						    {if ($success == 1)}
							<p class="description badge-dot mr-4"><i class="bg-success"></i>已充值成功 {$money} 元！请进入 <a href="/user/shop">套餐购买</a> 页面来选购您的套餐.</p>
							{else}
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>正在处理您的支付, 请您稍等. 此页面会自动刷新, 或者您可以选择关闭此页面, 余额将自动到账.</p>
							{/if}
							{if $config["enable_admin_contact"] == 'true'}
							<p class="description badge-dot mr-4"><i class="bg-danger"></i>管理员联系方式:</p>
								{if $config["admin_contact1"]!=null}
								<p class="description badge-dot mr-4"><i class="bg-danger"></i>{$config["admin_contact1"]}</p>
								{/if}
								{if $config["admin_contact2"]!=null}
								<p class="description badge-dot mr-4"><i class="bg-danger"></i>{$config["admin_contact2"]}</p>
								{/if}
								{if $config["admin_contact3"]!=null}
								<p class="description badge-dot mr-4"><i class="bg-danger"></i>{$config["admin_contact3"]}</p>
								{/if}
							{/if}
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
		</div>
	  </div><!--row-->

	  {include file='user/footer.tpl'}
	  
	  