<?php
namespace App\Controllers\Admin;

use App\Models\Node;
use App\Models\NodeGroup;
use App\Models\User;
use App\Controllers\AdminController;
use Ozdemir\Datatables\Datatables;
use App\Utils\DatatablesHelper;

class NodeGroupController extends AdminController
{	
    public function index($request, $response, $args)
    {
        $table_config['total_column'] = array("op" => "操作", "id" => "ID",
                              "name" => "分组名称", "level" => "分组级别", "date" => "时间");
        $table_config['default_show_column'] = array("op", "id",
                                                    "name", "level", "date");
													
        $table_config['ajax_url'] = 'nodegroup/ajax';
        return $this->view()->assign('table_config', $table_config)->display('admin/nodegroup/index.tpl');
		
    }

    public function create($request, $response, $args)
    {
        return $this->view()->display('admin/nodegroup/create.tpl');
    }
	
    public function add($request, $response, $args)
    {	
        $ngg = new NodeGroup();
		$ngg->name = $request->getParam('name');
        $nlevel = $request->getParam('level');
		$ngg->date =  date("Y-m-d H:i:s");
		$nglevel = NodeGroup::where('level', $nlevel)->first();
            if ($nglevel != null) {
                $rs['ret'] = 0;
                $rs['msg'] = "添加失败，目标等级已存在";
                return $response->getBody()->write(json_encode($rs));
            }
      $ngg->level = $request->getParam('level');
            if (!$ngg->save()) {
                $rs['ret'] = 0;
                $rs['msg'] = "添加失败";
                return $response->getBody()->write(json_encode($rs));
            }
				$rs['ret'] = 1;
				$rs['msg'] = "添加成功";
			
			return $response->getBody()->write(json_encode($rs));
	} 
    public function edit($request, $response, $args)
    {
        $id = $args['id'];
        $ngg = NodeGroup::find($id);

        return $this->view()->assign('ngg', $ngg)->display('admin/nodegroup/edit.tpl');
    }

    public function update($request, $response, $args)
    {
        $id = $args['id'];
        $ngg = NodeGroup::find($id);
        $ngg->name =  $request->getParam('name');
		$ngg->date =  date("Y-m-d H:i:s");
        $nlevel = $request->getParam('level');
		$nglevel = NodeGroup::where('level', $nlevel)->first();
            if ($ngg->level != $nlevel && $nglevel != null) {
                $rs['ret'] = 0;
                $rs['msg'] = "修改失败，目标等级已存在";
                return $response->getBody()->write(json_encode($rs));
            }
	//	Node::where('group_id',$id)->update(['node_class' => $request->getParam('level')]);
        
		$ngg->level =  $request->getParam('level');
        if (!$ngg->save()) {
            $rs['ret'] = 0;
            $rs['msg'] = "修改失败";
            return $response->getBody()->write(json_encode($rs));
        }
        Node::where('node_group', $ngg->level)->update(['node_group' => $nlevel]);
        User::where('node_group', $ngg->level)->update(['node_group' => $nlevel]);
      
			$rs['ret'] = 1;
			$rs['msg'] = "修改成功,请手动修改对应群组的套餐商品";
      
        return $response->getBody()->write(json_encode($rs));
    }
	
    public function delete($request, $response, $args)
    {
        $id = $request->getParam('id');
        $ngg = NodeGroup::find($id);
        if (!$ngg->delete()) {
            $rs['ret'] = 0;
            $rs['msg'] = "删除失败";
            return $response->getBody()->write(json_encode($rs));
        }
        Node::where('node_group', $ngg->level)->update(['node_group' => 0]);
        User::where('node_group', $ngg->level)->update(['node_group' => 0]);
        $rs['ret'] = 1;
        $rs['msg'] = "删除成功,请手动修改对应群组的套餐商品";
        return $response->getBody()->write(json_encode($rs));
    }

    public function ajax($request, $response, $args)
    {
        $datatables = new Datatables(new DatatablesHelper());
        $datatables->query('Select id as id,date,name,level from ss_group');


		$datatables->edit('op', function ($data) {
            return '<a class="btn btn-primary btn-sm" href="/admin/nodegroup/'.$data['id'].'/edit">编辑</a>
                    <a class="btn btn-secondary btn-sm" '.($data['id'] == 1 ? 'disabled' : 'id="delete" value="'.$data['id'].'" href="javascript:void(0);" onClick="delete_modal_show(\''.$data['id'].'\')"').'>删除</a>';
        });
        $datatables->edit('DT_RowId', function ($data) {
            return 'row_1_'.$data['id'];
        });


        $body = $response->getBody();
        $body->write($datatables->generate());
    }
}
