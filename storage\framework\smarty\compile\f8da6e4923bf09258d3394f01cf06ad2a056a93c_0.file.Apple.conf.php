<?php
/* Smarty version 3.1.33, created on 2022-02-25 00:19:52
  from '/www/wwwroot/www.shadowingy.cf/resources/conf/rule/Apple.conf' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_6217b028deca94_20361954',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'f8da6e4923bf09258d3394f01cf06ad2a056a93c' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/conf/rule/Apple.conf',
      1 => 1574356666,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_6217b028deca94_20361954 (Smarty_Internal_Template $_smarty_tpl) {
?># Apple

URL-REGEX,*apple.com/cn,🍎 Only

PROCESS-NAME,storedownloadd,🍎 Only

USER-AGENT,%E5%9C%B0%E5%9B%BE*,🍎 Only
USER-AGENT,%E8%AE%BE%E7%BD%AE*,🍎 Only
USER-AGENT,*com.apple.mobileme.fmip1,🍎 Only
USER-AGENT,*WeatherFoundation*,🍎 Only
USER-AGENT,*AssistantServices*,🍎 Only
USER-AGENT,MobileAsset*,🍎 Only
USER-AGENT,Siri*,🍎 Only

USER-AGENT,cloudd*,🍎 Only
USER-AGENT,com.apple.appstored*,🍎 Only
USER-AGENT,com.apple.geod*,🍎 Only
USER-AGENT,com.apple.Maps,🍎 Only
USER-AGENT,FindMyFriends*,🍎 Only
USER-AGENT,FindMyiPhone*,🍎 Only
USER-AGENT,FMDClient*,🍎 Only
USER-AGENT,FMFD*,🍎 Only
USER-AGENT,fmflocatord*,🍎 Only
USER-AGENT,geod*,🍎 Only
USER-AGENT,locationd*,🍎 Only
USER-AGENT,Maps*,🍎 Only

DOMAIN,guzzoni.apple.com,🍎 Only
DOMAIN-SUFFIX,aaplimg.com,🍎 Only
DOMAIN-SUFFIX,apple.co,🍎 Only
DOMAIN-SUFFIX,apple.com,🍎 Only
DOMAIN-SUFFIX,apple-cloudkit.com,🍎 Only
DOMAIN-SUFFIX,cdn-apple.com,🍎 Only
DOMAIN-SUFFIX,icloud.com,🍎 Only
DOMAIN-SUFFIX,icloud-content.com,🍎 Only
DOMAIN-SUFFIX,itunes.apple.com,🍎 Only
DOMAIN-SUFFIX,itunes.com,🍎 Only
DOMAIN-SUFFIX,lookup-api.apple.com,🍎 Only
DOMAIN-SUFFIX,me.com,🍎 Only
DOMAIN-SUFFIX,mzstatic.com,🍎 Only
<?php }
}
