<?php
/* Smarty version 3.1.33, created on 2022-07-06 00:00:25
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/code.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62c460199f53b7_77690172',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'b408d996f5c65c6eba4cce17149fea719514ff4f' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/code.tpl',
      1 => 1657036811,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:user/main.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:user/footer.tpl' => 1,
  ),
),false)) {
function content_62c460199f53b7_77690172 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:user/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Wallet</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">我的钱包</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/ticket/create" class="btn btn-sm btn-neutral">工单</a>
              <a href="/user/shop" class="btn btn-sm btn-neutral">前往商店</a>
            </div>
          </div>
          <!-- Card stats -->
          <div class="row">
			<div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">账户余额</h5>
                      <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['user']->value->money;?>
&nbsp;<small>火箭币</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-green text-white rounded-circle shadow">
                        <i class="ni ni-money-coins"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="#code"><i class="ni ni-credit-card icon-ver"></i>&nbsp;账户充值</a></span>
                  </p>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">邀请账户余额</h5>
					  <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['user']->value->ref_money;?>
&nbsp;<small>火箭币</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-orange text-white rounded-circle shadow">
                        <i class="ni ni-chart-pie-35"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/user/invite"><i class="ni ni-laptop icon-ver"></i>&nbsp;前往邀请中心</a></span>
                  </p>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">当前使用套餐</h5>
					  <span class="h2 font-weight-bold mb-0"><small><?php if ($_smarty_tpl->tpl_vars['user']->value->class > 0) {
echo $_smarty_tpl->tpl_vars['shopname']->value->name;
} else { ?>免费订阅计划<?php }?></small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-red text-white rounded-circle shadow">
                        <i class="ni ni-credit-card"></i>
                      </div>
                    </div>
                  </div>
				    <p class="mt-3 mb-0 text-sm">
						<span class="text-nowrap"><a href="#"><i class="fa fa-calendar icon-ver"></i>&nbsp;<?php echo $_smarty_tpl->tpl_vars['user']->value->class_expire;?>
</a></span>
                    </p>
                </div>
              </div>
            </div>
			<div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">我的优惠卷</h5>
					  <span class="h2 font-weight-bold mb-0"><?php echo $_smarty_tpl->tpl_vars['coups']->value;?>
&nbsp;<small>张</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                        <i class="ni ni-folder-17"></i>
                      </div>
                    </div>
                  </div>
				    <p class="mt-3 mb-0 text-sm">
						<span class="text-nowrap"><a href="javascript:void(0);" data-toggle="modal" data-target="#coupons"><i class="fa fa-ticket icon-ver"></i>&nbsp;点此查看优惠卷</a></span>
                    </p>
                </div>
              </div>
            </div>
          </div><!--row-->

        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
		<!-- Custom form validation -->
            <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">充值中心</h3>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <div class="nav-wrapper">
					<ul class="nav nav-pills nav-fill flex-column flex-md-row" id="tabs-icons-text" role="tablist">
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0 active" id="tabs-icons-text-1-tab" data-toggle="tab" href="#tabs-icons-text-1" role="tab" aria-controls="tabs-icons-text-1" aria-selected="true"><i class="icon-ver"><img src="/images/alipay.png" width="32" height="32"></i> 支付宝支付</a>
						</li>
						<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] != null) {?>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-2-tab" data-toggle="tab" href="#tabs-icons-text-2" role="tab" aria-controls="tabs-icons-text-2" aria-selected="false"><i class="icon-ver"><img src="/images/wxpay.png" width="32" height="32"></i> 微信支付</a>
						</li>
						<?php }?>
						<?php if ($_smarty_tpl->tpl_vars['config']->value['Clientid'] != null) {?>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-3-tab" data-toggle="tab" href="#tabs-icons-text-3" role="tab" aria-controls="tabs-icons-text-3" aria-selected="false"><i class="icon-ver"><img src="/images/timg.png" width="32" height="32"></i> Paypal支付</a>
						</li>
						<?php }?>
						<?php if ($_smarty_tpl->tpl_vars['config']->value['bitpay_secret'] != null) {?>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-4-tab" data-toggle="tab" href="#tabs-icons-text-4" role="tab" aria-controls="tabs-icons-text-4" aria-selected="false"><i class="icon-ver"><img src="/images/timg.png" width="32" height="32"></i> 数字货币支付</a>
						</li>
						<?php }?>
						<li class="nav-item">
							<a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-5-tab" data-toggle="tab" href="#tabs-icons-text-5" role="tab" aria-controls="tabs-icons-text-5" aria-selected="false"><i class="icon-ver"><img src="/images/sss.png" width="32" height="32"></i> 充值码</a>
						</li>
					</ul>
				</div>
                <div class="tab-content" id="myTabContent">
					<div class="tab-pane fade show active" id="tabs-icons-text-1" role="tabpanel" aria-labelledby="tabs-icons-text-1-tab">
						<p class="py-2">
							<i class="ni ni-air-baloon"></i> 当前选择支付方式：支付宝
						</p>
						<div class="form-row">
							<div class="col-md-6 mb-3">
							<label class="form-control-label" for="validationCustom03">Price</label>
							<input type="hidden" class="getId" name="id">
								<input type="number" class="form-control" id="amount" placeholder="￥请填入金额或点击下面金额" required>
								<div class="card-body">
									<div class="quick_amount">
										<p class="col-xxxs" data-item='5'>5</p>
										<p class="col-xxxs" data-item='10'>10</p>
										<p class="col-xxxs" data-item='10'>15</p>
										<p class="col-xxxs" data-item='20'>20</p>
										<p class="col-xxxs" data-item='50'>50</p>
										<p class="col-xxxs" data-item='100'>100</p>
										<p class="col-xxxs" data-item='200'>200</p>
										<p class="col-xxxs" data-item='200'>500</p>
									</div>
								</div>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "f2fpay") {?>
								<button id="ali_pay" class="btn btn-primary" type="button">支付宝充值</button>
								<?php }?>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "tomatopay") {?>
								<button id="ali_pay_to" class="btn btn-primary" type="button">支付宝充值</button>
								<?php }?>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "codepay") {?>
								<button id="ali_pay_ma" class="btn btn-primary" type="button">支付宝充值</button>
								<?php }?>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "flyfoxpay") {?>
								<button id="ali_pay_fly" class="btn btn-primary" type="button">支付宝充值</button>
								<?php }?>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "idtpay") {?>
								<button id="ali_pay_idt" class="btn btn-primary" type="button">支付宝充值</button>
								<?php }?>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "mycode") {?>
								<button id="ali_pay_mycode" class="btn btn-primary" type="button">支付宝充值</button>
								<?php }?>
							</div>
							<div class="col-md-6 mb-3">
								<div class="h5 text-center" id="ali_qrarea"><img src="/theme/czssr/main/images/sticker.png" width="296" height=""></div>
							</div>
						</div>
					</div> 
					<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] != null) {?>
					<div class="tab-pane fade" id="tabs-icons-text-2" role="tabpanel" aria-labelledby="tabs-icons-text-2-tab">
						<p class="py-2">
							<i class="ni ni-air-baloon"></i> 当前选择支付方式：微信支付
						</p>
						<div class="form-row">
							<div class="col-md-6 mb-3">
							<label class="form-control-label" for="validationCustom03">Price</label>
							<input type="hidden" class="getId" name="id">
								<input type="number" class="form-control" id="price" placeholder="￥请填入金额或点击下面金额" required>
								<div class="card-body">
									<div class="quick_amount">
										<p class="col-xxxs" data-item='5'>5</p>
										<p class="col-xxxs" data-item='10'>10</p>
										<p class="col-xxxs" data-item='10'>15</p>
										<p class="col-xxxs" data-item='20'>20</p>
										<p class="col-xxxs" data-item='50'>50</p>
										<p class="col-xxxs" data-item='100'>100</p>
										<p class="col-xxxs" data-item='200'>200</p>
										<p class="col-xxxs" data-item='200'>500</p>
									</div>
								</div>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "umikm") {?>
								<button id="wx_pay_ym" class="btn btn-primary" type="button">微信充值</button>
								<?php }?>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "payjs") {?>
								<button id="wx_payjs" class="btn btn-primary" type="button">微信充值</button>
								<?php }?>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "tomatopay") {?>
								<button id="wx_pay_to" class="btn btn-primary" type="button">微信充值</button>
								<?php }?>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "codepay") {?>
								<button id="wx_pay_ma" class="btn btn-primary" type="button">微信充值</button>
								<?php }?>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_A'] == "flyfoxpay") {?>
								<button id="wx_pay_fly" class="btn btn-primary" type="button">微信充值</button>
								<?php }?>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "idtpay") {?>
								<button id="wx_pay_idt" class="btn btn-primary" type="button">微信充值</button>
								<?php }?>
								<?php if ($_smarty_tpl->tpl_vars['config']->value['payment_system_B'] == "mycode") {?>
								<button id="wx_pay_mycode" class="btn btn-primary" type="button">微信充值</button>
								<?php }?>
							</div>
							<div class="col-md-6 mb-3">
								<div class="h5 text-center" id="wx_qrarea"><img src="/theme/czssr/main/images/sticker.png" width="296" height=""></div>

							</div>
						</div>
					</div>
					<?php }?>
					<?php if ($_smarty_tpl->tpl_vars['config']->value['Clientid'] != null) {?>
					<div class="tab-pane fade" id="tabs-icons-text-3" role="tabpanel" aria-labelledby="tabs-icons-text-3-tab">
					    <p class="py-2">
							<i class="ni ni-air-baloon"></i> 当前选择支付方式：Paypal支付
						</p>
						<div class="form-row">
							<div class="col-md-6 mb-3">
							<label class="form-control-label" for="validationCustom03">Price</label>
							<input type="hidden" class="getId" name="id">
								<input type="number" class="form-control" id="money" placeholder="￥" required>
								<div class="card-body">
									<div class="quick_amount">
										<p class="col-xxxs" data-item='5'>5</p>
										<p class="col-xxxs" data-item='10'>10</p>
										<p class="col-xxxs" data-item='10'>15</p>
										<p class="col-xxxs" data-item='20'>20</p>
										<p class="col-xxxs" data-item='50'>50</p>
										<p class="col-xxxs" data-item='100'>100</p>
										<p class="col-xxxs" data-item='200'>200</p>
										<p class="col-xxxs" data-item='200'>500</p>
									</div>
								</div>
								<button id="paypal" class="btn btn-primary" type="button">Paypal充值</button>
							</div>
							<div class="col-md-6 mb-3">
								<div class="h5 text-center" id="paypal_qrarea"><img src="/theme/czssr/main/images/sticker.png" width="296" height=""></div>
							</div>
						</div>
					</div>
                  <?php }?>
					<?php if ($_smarty_tpl->tpl_vars['config']->value['bitpay_secret'] != null) {?>
					<div class="tab-pane fade" id="tabs-icons-text-4" role="tabpanel" aria-labelledby="tabs-icons-text-4-tab">
					    <p class="py-2">
							<i class="ni ni-air-baloon"></i> 当前选择支付方式：数字货币支付
						</p>
						<div class="form-row">
							<div class="col-md-6 mb-3">
							<label class="form-control-label" for="validationCustom03">Price</label>
							<input type="hidden" class="getId" name="id">
								<input type="number" class="form-control" id="bitpayamount" placeholder="￥" required>
								<div class="card-body">
									<div class="quick_amount">
										<p class="col-xxxs" data-item='5'>5</p>
										<p class="col-xxxs" data-item='10'>10</p>
										<p class="col-xxxs" data-item='10'>15</p>
										<p class="col-xxxs" data-item='20'>20</p>
										<p class="col-xxxs" data-item='50'>50</p>
										<p class="col-xxxs" data-item='100'>100</p>
										<p class="col-xxxs" data-item='200'>200</p>
										<p class="col-xxxs" data-item='200'>500</p>
									</div>
								</div>
								<button id="bitpaySubmit" class="btn btn-primary" type="button">数字货币充值</button>
							</div>
							<div class="col-md-6 mb-3">
								<div class="h5 text-center" id="paypal_qrarea"><img src="/theme/czssr/main/images/sticker.png" width="296" height=""></div>
							</div>
						</div>
					</div>
					<?php }?>
					<div class="tab-pane fade" id="tabs-icons-text-5" role="tabpanel" aria-labelledby="tabs-icons-text-5-tab">
					    <p class="py-2">
							<i class="ni ni-air-baloon"></i> 当前选择支付方式：充值码
						</p>
						<div class="form-row">
							<div class="col-md-6 mb-3">
							<label class="form-control-label" for="validationCustom03">Code</label>
								<input type="text" class="form-control mb-3" id="code" placeholder="这里填入优惠码" required>
								
								<button id="code-update" class="btn btn-primary" type="button">充值码充值</button>
							</div>
						</div>
					</div>
				</div>
              </div>
            </div><!--card-->
			
          <div class="card">
            <!-- Card header -->
            <div class="card-header">
              <h3 class="mb-0">充值记录</h3>
            </div>
		   <div class="card-body">
            <div class="table-responsive">
			 <?php echo $_smarty_tpl->tpl_vars['codes']->value->render();?>

				<table class="table align-items-center table-flush">
                <thead class="thead-light">
                  <tr>
                    <th>ID</th>
                    <th>代码</th>
					<th>类型</th>
					<th>操作</th>
					<th>使用时间</th>
                  </tr>
                </thead>
                
                <tbody>
				<?php if (count($_smarty_tpl->tpl_vars['codes']->value) == 0) {?>
                    <tr>
                      <td colspan="5"><strong>无充值记录</strong></td>
                    </tr>
                <?php } else { ?>
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['codes']->value, 'code');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['code']->value) {
?>
                  <tr>
                    <td class="badge-dot mr-4"><i class="bg-warning"></i>#<?php echo $_smarty_tpl->tpl_vars['code']->value->id;?>
</td>
                    <td><?php echo $_smarty_tpl->tpl_vars['code']->value->code;?>
</td>
                    <?php if ($_smarty_tpl->tpl_vars['code']->value->type == -1) {?>
                        <td>金额充值</td>
                    <?php }?>
                    <?php if ($_smarty_tpl->tpl_vars['code']->value->type == 10001) {?>
                        <td>流量充值</td>
                    <?php }?>
                    <?php if ($_smarty_tpl->tpl_vars['code']->value->type == 10002) {?>
                        <td>用户续期</td>
                    <?php }?>
                    <?php if ($_smarty_tpl->tpl_vars['code']->value->type >= 1 && $_smarty_tpl->tpl_vars['code']->value->type <= 10000) {?> 
					    <td>等级续期 - 等级<?php echo $_smarty_tpl->tpl_vars['code']->value->type;?>
</td>
                     <?php }?>
					<?php if ($_smarty_tpl->tpl_vars['code']->value->type == -1) {?>
                        <td>充值 <?php echo $_smarty_tpl->tpl_vars['code']->value->number;?>
 元</td>
                    <?php }?>
                    <?php if ($_smarty_tpl->tpl_vars['code']->value->type == 10001) {?>
                        <td>充值 <?php echo $_smarty_tpl->tpl_vars['code']->value->number;?>
 GB 流量</td>
                    <?php }?>
                    <?php if ($_smarty_tpl->tpl_vars['code']->value->type == 10002) {?>
                        <td>延长账户有效期 <?php echo $_smarty_tpl->tpl_vars['code']->value->number;?>
 天</td>
                    <?php }?>
                    <?php if ($_smarty_tpl->tpl_vars['code']->value->type >= 1 && $_smarty_tpl->tpl_vars['code']->value->type <= 10000) {?>
					    <td>延长等级有效期 <?php echo $_smarty_tpl->tpl_vars['code']->value->number;?>
 天</td>
                    <?php }?>
                    <td><?php echo $_smarty_tpl->tpl_vars['code']->value->usedatetime;?>
</td>
                  </tr>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
				<?php }?>
                </tbody>
              </table>

            </div>
		   </div>
          </div><!--card-->
		  <div class="card">
            <!-- Card header -->
            <div class="card-header">
              <h3 class="mb-0">购买记录</h3>
            </div>
		 <div class="card-body">	
            <div class="table-responsive">
				<table class="table align-items-center table-flush">
                <thead class="thead-light">
                  <tr>
                    <th>ID</th>
                    <th>商品名称</th>
                    <th>价格</th>
                    <th>购买时间</th>
                    <th>续费时间</th>
                    <th>续费时重置流量</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
				<?php if (count($_smarty_tpl->tpl_vars['shops']->value) == 0) {?>
                    <tr>
                      <td colspan="7"><strong>无购买记录</strong></td>
                    </tr>
                <?php } else { ?>
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['shops']->value, 'shop');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['shop']->value) {
?>
                  <tr>
                    <td class="badge-dot mr-4"><i class="bg-warning"></i>#<?php echo $_smarty_tpl->tpl_vars['shop']->value->id;?>
</td>
                    <?php if ($_smarty_tpl->tpl_vars['shop']->value->shop()->id == 0) {?>
                    <td><?php echo $_smarty_tpl->tpl_vars['shop']->value->coupon;?>
</td>
                    <?php } else { ?>
                    <td><?php echo $_smarty_tpl->tpl_vars['shop']->value->shop()->name;?>
</td>
                    <?php }?>
                    <td>¥<?php echo $_smarty_tpl->tpl_vars['shop']->value->price;?>
</td>
                    <td><?php echo date('Y-m-d H:i:s',$_smarty_tpl->tpl_vars['shop']->value->datetime);?>
</td>
                    <?php if ($_smarty_tpl->tpl_vars['shop']->value->renew == 0) {?>
                        <td>不自动续费</td>
                    <?php } else { ?>
                        <td>在 <?php echo $_smarty_tpl->tpl_vars['shop']->value->renew_date();?>
 续费</td>
                    <?php }?>

                    <?php if ($_smarty_tpl->tpl_vars['shop']->value->shop()->auto_reset_bandwidth == 0) {?>
                        <td>不自动重置</td>
                    <?php } else { ?>
                        <td>自动重置</td>
                    <?php }?>
                    <td>
                        <?php if ($_smarty_tpl->tpl_vars['shop']->value->renew == 0) {?>
                            无操作
                        <?php } else { ?>
                            <a href="javascript:void(0);" onClick="delete_modal_show('<?php echo $_smarty_tpl->tpl_vars['shop']->value->id;?>
')" class="btn btn-sm btn-primary">关闭自动续费</a>
                         <?php }?>
                    </td>
                  </tr>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
				<?php }?>
                </tbody>
              </table>
            </div>
		   </div>	
          </div><!--card-->
		  
        </div>
      </div><!--row-->
		<!-- Modal -->
		<div class="modal fade" id="readytopay" tabindex="-1" role="dialog" aria-labelledby="resultModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="resultModalLabel">正在连接支付网关</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
		        <p class="h6 mb-3 text-black-hint" id="title"> 感谢您对我们的支持，请耐心等待^_^</p>
		      </div>
		    </div>
		  </div>
		</div>
		<!--删除modal-->
		<div class="modal fade" id="delete_modal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="deleteModalLabel" class="text-danger">确认要关闭自动续费?</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>请您确认?</p>
				</div>	 
		      </div>
			    <div class="modal-footer">
                    <button id="delete_input" type="button" class="btn btn-primary">确认提交</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
		    </div>
		  </div>
		</div>
		<!-- coupon  Modal -->
		<div class="modal fade" id="coupons" tabindex="-1" role="dialog" aria-labelledby="couponModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="couponModalLabel">部分码子有次数限制,请抓紧时间使用.</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
			  <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['coupons']->value, 'coupon');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['coupon']->value) {
?>
		         <p class="description">
					<span class="mb-3">码子:  <?php echo $_smarty_tpl->tpl_vars['coupon']->value->code;?>
&nbsp;&nbsp;&nbsp;&nbsp;折扣:  <?php echo (100-$_smarty_tpl->tpl_vars['coupon']->value->credit)/10;?>
&nbsp;&nbsp;&nbsp;&nbsp;到期:  <?php echo date('Y-m-d',$_smarty_tpl->tpl_vars['coupon']->value->expire);?>
</spam>
				 </p>
			  <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>	 
		      </div>
		      <div class="modal-footer">
		        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
		      </div>
		    </div>
		  </div>
		</div>
		<!--payresult-->
		<div class="modal fade" tabindex="-1" role="dialog" id="payresult" aria-labelledby="payresultModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h4 id="payresultModalLabel">充值进行时</h4>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<p class="h6 mb-3 text-black-hint" id="title"> 请在新打开的页面完成支付^_^</p>
				</div>
				<div class="modal-footer">
				    <button id="rehrefdown" type="button" class="btn btn-primary" data-dismiss="modal">已完成充值</button>
					<button id="rehrefup" type="button" class="btn btn-secondary" data-dismiss="modal">遇到问题</button>
		        </div>
			</div>
		  </div>
		</div>
	<?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	<?php $_smarty_tpl->_subTemplateRender('file:user/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

	<?php echo '<script'; ?>
 src="/theme/czssr/main/js/qrcode.min.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
>
var $amountInput = $('[type="number"]');
var amount = '';
var price = '';
var money = '';
var $getId = $('[type="hidden"]');
var getparse=ParaMeter();
$getId.val(getparse.id);
$(".quick_amount p").off("click").on("click", function () {
  amount = $(this).text();
  price = $(this).text();
  money = $(this).text();
  if (!$(this).hasClass('active')) {
    $(this).addClass('active').siblings().removeClass('active');
    $amountInput.val(amount);
	$amountInput.val(price);
	$amountInput.val(money);
  } else {
    $(this).removeClass('active');
    $amountInput.val('');
  }
})

function ParaMeter()
{
  var obj={};
  var arr=location.href.substring(location.href.lastIndexOf('?')+1).split("&");
  for(var i=0;i < arr.length;i++){
  var aa=arr[i].split("=");
  obj[aa[0]]=aa[1];
}
  return obj;
}
<?php echo '</script'; ?>
>


<?php echo '<script'; ?>
>
$("#ali_pay_mycode").on("click", function () {
    if($("#amount").val() <=0){
       $("#result").modal();
       $("#msg").html("非法金额");
       return false;
    }
	$.ajax({
		type: "POST",
		url: "/user/payment_a/purchase",
		dataType: "json",
		data: {
			price: $("#amount").val(),
			type: 'alipay'
		},
		success: function (data) {
			if (data.ret) {
				pid = data.pid;
				if(data.method == "qr_code"){
					$("#ali_qrarea").html('<div class="text-center"><p>请使用APP扫描二维码支付</p><a id="qrcode" style="padding-top:10px;display:inline-block"></a><p>手机截图保存相册扫码</p></div>');
						$("#qr_code").modal();
                    new QRCode("qrcode", {
                        render: "canvas",
                        width: 200,
                        height: 200,
			        	text: data.qrcode,
                        correctLevel: QRCode.CorrectLevel.Q
                    });
                    $('#qrcode').attr('href',data.qrcode);
                    setTimeout(status, 1000);
				}else{
					if(data.type=="wxpay"){
						$("#result").modal();
						$("#msg").html("正在跳转到微信..."+data.url);
					}else if(data.type=="alipay"){
						$("#result").modal();
						$("#msg").html("正在跳转到支付宝..."+data.url);
					}else{
						$("#result").modal();
						$("#msg").html("正在跳转到支付网关..."+data.url);
					}
				}
			} else {
				swal('Oops...',data.msg,'error');
			}
		},
		
		error: function (jqXHR) {
			$("#result").modal();
            $("#msg").html("发生错误了: " + jqXHR.status);
		}
	});
});
function status(){
    $.ajax({
        type: "POST",
        url: "/payment_a/status",
        dataType: "json",
        data: {
            pid:pid
        },
        success: function (data) {
            if (data.result) {
                console.log(data);
                $("#result").modal();
                $("#msg").html(data.msg);
				window.setTimeout("location.href=window.location.href", 2000);
            }else{
            tid = setTimeout(status, 2000); //循环调用触发setTimeout
            }
        },
        error: function (jqXHR) {
            console.log(jqXHR);
        }
    });
}
<?php echo '</script'; ?>
>
<?php echo '<script'; ?>
>
$("#wx_pay_mycode").on("click", function () {
    if($("#price").val() <=0){
       $("#result").modal();
       $("#msg").html("非法金额");
       return false;
    }
	$.ajax({
		type: "POST",
		url: "/user/payment_b/purchase",
		dataType: "json",
		data: {
			price: $("#price").val(),
			type: 'wxpay'
		},
		success: function (data) {
			if (data.ret) {
				pid = data.pid;
				if(data.method == "qr_code"){
					$("#ali_qrarea").html('<div class="text-center"><p>请使用APP扫描二维码支付</p><a id="qrcode" style="padding-top:10px;display:inline-block"></a><p>手机截图保存相册扫码</p></div>');
						$("#qr_code").modal();
                    new QRCode("qrcode", {
                        render: "canvas",
                        width: 200,
                        height: 200,
			        	text: data.qrcode,
                        correctLevel: QRCode.CorrectLevel.Q
                    });
                    $('#qrcode').attr('href',data.qrcode);
                    setTimeout(status, 1000);
				}else{
					if(data.type=="wxpay"){
						$("#result").modal();
						$("#msg").html("正在跳转到微信..."+data.url);
					}else if(data.type=="alipay"){
						$("#result").modal();
						$("#msg").html("正在跳转到支付宝..."+data.url);
					}else{
						$("#result").modal();
						$("#msg").html("正在跳转到支付网关..."+data.url);
					}
				}
			} else {
				swal('Oops...',data.msg,'error');
			}
		},
		error: function (jqXHR) {
			$("#result").modal();
            $("#msg").html("发生错误了: " + jqXHR.status);
		}
	});
});
function status(){
    $.ajax({
        type: "POST",
        url: "/payment_b/status",
        dataType: "json",
        data: {
            pid:pid
        },
        success: function (data) {
            if (data.result) {
                console.log(data);
                $("#result").modal();
                $("#msg").html(data.msg);
				window.setTimeout("location.href=window.location.href", 2000);
            }else{
            tid = setTimeout(status, 2000); //循环调用触发setTimeout
            }
        },
        error: function (jqXHR) {
            console.log(jqXHR);
        }
    });
}
<?php echo '</script'; ?>
>
<?php echo '<script'; ?>
>
function delete_modal_show(id) {
	deleteid=id;
	$("#delete_modal").modal();
}

$(document).ready(function(){
	function delete_id(){
		$.ajax({
			type:"DELETE",
			url:"/user/bought",
			dataType:"json",
			data:{
				id: deleteid
			},
			success:function(data){
				if(data.ret){
                    $("#delete_modal").modal('hide');
					$("#result").modal();
					$("#msg").html(data.msg);
					window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
				}else{
                    $("#delete_modal").modal('hide');
					$("#result").modal();
					$("#msg").html(data.msg);
				}
			},
			error:function(jqXHR){
				$("#result").modal();
			    $("#msg").html(jqXHR+"  发生了错误。");
			}
		});
	}
	$("#delete_input").on('click', delete_id);
})
	
<?php echo '</script'; ?>
>
<?php echo '<script'; ?>
>
   $("#rehrefdown").click(function () {
      window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
   });
   $("#rehrefup").click(function () {
      window.setTimeout("location.href=window.location.href", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
   })
<?php echo '</script'; ?>
>
<?php }
}
