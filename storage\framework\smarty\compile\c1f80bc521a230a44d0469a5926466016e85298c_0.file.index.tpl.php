<?php
/* Smarty version 3.1.33, created on 2022-06-17 21:44:07
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/nodegroup/index.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62ac85276b83c8_67151335',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'c1f80bc521a230a44d0469a5926466016e85298c' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/nodegroup/index.tpl',
      1 => 1571934026,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/main.tpl' => 1,
    'file:table/checkbox.tpl' => 1,
    'file:table/table.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:admin/footer.tpl' => 1,
    'file:table/js_1.tpl' => 1,
    'file:table/js_delete.tpl' => 1,
    'file:table/js_2.tpl' => 1,
  ),
),false)) {
function content_62ac85276b83c8_67151335 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Groups</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">群组设置</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/node" class="btn btn-sm btn-neutral">节点</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">群组设置(节点/用户/商品)</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
                      <a class="btn btn-primary btn-sm mb-3" href="/admin/nodegroup/create">新建群组</a>
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>显示表项: <?php $_smarty_tpl->_subTemplateRender('file:table/checkbox.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?></p>
							
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
			
			<div class="card">
              <!-- Card body -->
			  <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive py-4">
				  <?php $_smarty_tpl->_subTemplateRender('file:table/table.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
					
				</div>
              </div>
            </div><!--card-->
		
        </div>
      </div><!--row-->
	  <!--删除modal-->
		<div class="modal fade" id="delete_modal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="deleteModalLabel" class="text-danger">确认删除吗?</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>请问你确认要删除吗?</p>
				</div>	 
		      </div>
			    <div class="modal-footer">
                    <button id="delete_input" type="button" class="btn btn-primary">确认提交</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
		    </div>
		  </div>
		</div>
		<?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php $_smarty_tpl->_subTemplateRender('file:admin/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
echo '<script'; ?>
 src="//cdn.jsdelivr.net/npm/datatables.net@1.10.19"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="/theme/czssr/main/js/dataTables.material.min.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
>
function delete_modal_show(id) {
    deleteid = id;
	$("#delete_modal").modal();
}
<?php $_smarty_tpl->_subTemplateRender('file:table/js_1.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

window.addEventListener('load', () => {


	function delete_id(){
		$.ajax({
			type:"DELETE",
			url:"/admin/nodegroup",
			dataType:"json",
			data:{
				id: deleteid
			},
			success: data => {
				if (data.ret) {
                    $("#delete_modal").modal("hide");
					$("#result").modal();
                    $('#msg').html(data.msg);
					<?php $_smarty_tpl->_subTemplateRender('file:table/js_delete.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
				}else{
					$("#result").modal();
                    $('#msg').html(data.msg);
				}
			},
			error: jqXHR => {
				$("#result").modal();
                $("#msg").html("发生错误了: " + jqXHR.status);
			}
		});
	}

		$("#delete_input").on("click", delete_id);
		<?php $_smarty_tpl->_subTemplateRender('file:table/js_2.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
})

<?php echo '</script'; ?>
>
<?php }
}
