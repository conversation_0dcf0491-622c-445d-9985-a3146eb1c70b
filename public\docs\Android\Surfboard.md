## 应用概述
## [点击在线观看android视频教程](https://cdn.jsdelivr.net/gh/lxiaoya1003/tuchuang@master/androidssr99.mp4)

<video width="300" height="150" controls="controls">
<source src="https://cdn.jsdelivr.net/gh/lxiaoya1003/tuchuang@master/androidssr99.mp4" type="video/mp4" />
</video>

Surfboard 是在 Android 平台上的客户端软件，支持 Shadowsocks 协议。

目前此应用程序仍然处于测试版，因此界面和功能在未来都可能发生改动。同时，其 [开发商已经宣布](https://twitter.com/getsurfboard/status/1023485591839621120)  Surfboard 未来将是一个「付费应用」，但具体授权方式的价格层暂未公布。

## 应用下载

以下是各平台该应用的下载地址。

- Android：[HockeyAPP](https://rink.hockeyapp.net/recruit/2113783c503645abb0a5ec6317e1a169)
- ...

此处您需要自行操作，参与测试并下载安装应用，完成后打开该应用，登录 HockeyAPP 的账户。

## 获取订阅

此处将显示您的订阅链接，请注意为登录状态：

[cinwell website](/sublink?type=surfboard ':include :type=markdown')

!> 这个 **订阅链接** 非常重要，你应当把它当做密码一样妥善保管。

## 配置 Surfboard

打开 Surfboard 点击底部导航栏的「Profile」进入配置管理页面。

随后点击右下角的加号按钮，在弹出的选项中选择「Download from url」。

![1](https://i.loli.net/2019/01/13/5c3a758911d69.jpeg ':size=600')

在弹出的输入框中粘贴上方 **[获取订阅](#获取订阅)** 中的订阅链接并点击 **Download**，此时 APP 会下载该配置。

随后点击右下角图标保存，在弹出的窗口中输入本站名称并点击 **OK**。

![2](https://i.loli.net/2019/01/13/5c3a7643d132f.jpeg ':size=600')

## 开始使用

点击底部导航栏中第二个「Config」进入策略组页面。

在 🍈 Select 策略组中选择您需要的节点。

随后底部导航栏中第一个「Switch」进入状态页面，点击右下角播放按钮即可。

![3](https://i.loli.net/2019/01/13/5c3a78b91a8bf.png ':size=200')

如操作系统提示添加 VPN 配置，请点击 运行 并验证您的 密码、指纹等。
