{include file='admin/main.tpl'}


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Create Relay</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/relay">中转规则管理</a></li>
                  <li class="breadcrumb-item active" aria-current="page">添加中转规则</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/node" class="btn btn-sm btn-neutral">节点</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">添加中转规则</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">起源节点:</label>
							<select id="source_node" class="form-control form-control-sm" name="source_node">
								<option value="0">请选择起源节点</option>
                                {foreach $source_nodes as $source_node}
                                    <option value="{$source_node->id}">{$source_node->name}</option>
                                {/foreach}
							</select>
						</div>
						<div class="form-group">
							<label class="form-control-label">目标节点:</label>
							<select id="dist_node" class="form-control form-control-sm" name="dist_node">
								<option value="-1">不进行中转</option>
                                {foreach $dist_nodes as $dist_node}
                                    <option value="{$dist_node->id}">{$dist_node->name}</option>
                                {/foreach}
							</select>
						</div>
						<div class="form-group">
							<label class="form-control-label">端口:</label>
							<input id="port" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">优先级(仅数字):</label>
							<input id="priority" class="form-control form-control-sm" type="number">
						</div>
						<div class="form-group">
							<label class="form-control-label">用户ID:</label>
							<input id="user_id" class="form-control form-control-sm" type="text">
						</div>
					</div>
					<div class="modal-footer">
						<button id="submit" type="button" class="btn btn-primary">确认提交</button>
					</div>
				</div>
			</div>
			
			
        </div>
      </div><!--row-->
	  
		
		{include file='dialog.tpl'}
       
	  {include file='admin/footer.tpl'}
	  
<script>


    window.addEventListener('load', () => {

        function submit() {
             $.ajax({
                type: "POST",
                url: "/admin/relay",
                dataType: "json",
                data: {
                    source_node: $$getValue('source_node'),
                    dist_node: $$getValue('dist_node'),
                    port: $$getValue('port'),
                    user_id: $$getValue('user_id'),
                    priority: $$getValue('priority')
                },
                success: data => {
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $('#msg').html("发生错误："+jqXHR.status);
                }
            });
        }
		
			$("#submit").on("click", submit);
		
    })

</script>

