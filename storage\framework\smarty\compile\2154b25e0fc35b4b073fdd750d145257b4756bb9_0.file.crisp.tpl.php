<?php
/* Smarty version 3.1.33, created on 2022-06-28 13:08:23
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/crisp.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62ba8cc707eea1_13245469',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '2154b25e0fc35b4b073fdd750d145257b4756bb9' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/crisp.tpl',
      1 => 1576403616,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_62ba8cc707eea1_13245469 (Smarty_Internal_Template $_smarty_tpl) {
?><literal>
<?php echo '<script'; ?>
 type="text/javascript">
window.$crisp=[];window.CRISP_WEBSITE_ID="<?php echo $_smarty_tpl->tpl_vars['config']->value["crisp_id"];?>
";
$crisp.push(
	["set", "user:nickname", "<?php echo $_smarty_tpl->tpl_vars['user']->value->user_name;?>
"],
	["set", "user:email", "<?php echo $_smarty_tpl->tpl_vars['user']->value->email;?>
"],
	["set", "session:data", 
		[
			[
				["ID","<?php echo $_smarty_tpl->tpl_vars['user']->value->id;?>
"],
				["class","<?php echo $_smarty_tpl->tpl_vars['user']->value->class;?>
"],
				["vip-time","<?php echo $_smarty_tpl->tpl_vars['user']->value->class_expire;?>
"],
				["last-use","<?php echo $_smarty_tpl->tpl_vars['user']->value->lastSsTime();?>
"],
				["traffic","<?php echo $_smarty_tpl->tpl_vars['user']->value->unusedTraffic();?>
"],
				["online-IP","<?php echo $_smarty_tpl->tpl_vars['user']->value->online_ip_count();?>
/<?php echo $_smarty_tpl->tpl_vars['user']->value->node_connector;?>
"],
				["money","<?php echo $_smarty_tpl->tpl_vars['user']->value->money;?>
"]
			]
		]
	]
);
(function(){ 
    d=document;s=d.createElement("script"); 
	s.src="/theme/czssr/main/js/crisp.js";
	s.async=1;d.getElementsByTagName("head")[0].appendChild(s);
})(); 
<?php echo '</script'; ?>
>
</literal><?php }
}
