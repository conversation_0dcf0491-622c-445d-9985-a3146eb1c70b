<?php

namespace App\Controllers\Admin;

use App\Models\Ann;
use App\Services\Auth;
use App\Models\Level;
use App\Controllers\AdminController;
use App\Utils\Telegram;
use App\Services\Config;
use App\Services\Mail;
use App\Models\User;
use Ozdemir\Datatables\Datatables;
use App\Utils\DatatablesHelper;

class AnnController extends AdminController
{
    public function index($request, $response, $args)
    {
        $table_config['total_column'] = array("op" => "操作", "id" => "ID",
                              "date" => "日期", "content" => "内容", "status" => "发信状态");
        $table_config['default_show_column'] = array("op", "id",
                                                    "date", "content");
        $table_config['ajax_url'] = 'announcement/ajax';
        return $this->view()->assign('table_config', $table_config)->display('admin/announcement/index.tpl');
    }

    public function create($request, $response, $args)
    {
        $levelList = Level::select('id','name','level')->orderBy('id')->get(); //获取等级名称的列表
        return $this->view()->assign('levelList', $levelList)->display('admin/announcement/create.tpl');
    }

    public function add($request, $response, $args)
    {
        $issend = $request->getParam('issend');
        $vip = $request->getParam('vip');
        $content = $request->getParam('content');

            $ann = new Ann();
            $ann->date =  date("Y-m-d H:i:s");
            $ann->content =  $content;
            $ann->markdown =  $request->getParam('markdown');
            if($issend == 1){
			    $ann->status = 1;//未发送，保存成功时未发送，执行定时任务时发送中，定时任务执行成功时发送成功
            }else{
            	$ann->status = 0;//不发送
            }
			$ann->issend = $issend;
			$ann->vip = $vip;
            if (!$ann->save()) {
                $rs['ret'] = 0;
                $rs['msg'] = "添加失败";
                return $response->getBody()->write(json_encode($rs));
            }
		Telegram::SendMarkdown("新公告：".PHP_EOL.$request->getParam('markdown'));
            $rs['ret'] = 1;
            $rs['msg'] = "公告添加成功";
        return $response->getBody()->write(json_encode($rs));

    }

    public function edit($request, $response, $args)
    {
        $id = $args['id'];
        $ann = Ann::find($id);
        if ($ann == null) {
        }
        return $this->view()->assign('ann', $ann)->display('admin/announcement/edit.tpl');
    }

    public function update($request, $response, $args)
    {
        $id = $args['id'];
        $ann = Ann::find($id);

        $ann->content =  $request->getParam('content');
        $ann->markdown =  $request->getParam('markdown');
        $ann->date =  date("Y-m-d H:i:s");

        if (!$ann->save()) {
            $rs['ret'] = 0;
            $rs['msg'] = "修改失败";
            return $response->getBody()->write(json_encode($rs));
        }

        Telegram::SendMarkdown("公告更新：".PHP_EOL.$request->getParam('markdown'));

        $rs['ret'] = 1;
        $rs['msg'] = "修改成功";
        return $response->getBody()->write(json_encode($rs));
    }

    public function up_images_a($request, $response, $args)
    {
       $allowedExts = array("gif", "jpeg", "jpg", "png");
       $temp = explode(".", $_FILES["avatar"]["name"]);
       $extension = end($temp);        // 获取文件后缀名

       if ((($_FILES["avatar"]["type"] == "image/gif")
       || ($_FILES["avatar"]["type"] == "image/jpeg")
       || ($_FILES["avatar"]["type"] == "image/jpg")
       || ($_FILES["avatar"]["type"] == "image/pjpeg")
       || ($_FILES["avatar"]["type"] == "image/x-png")
       || ($_FILES["avatar"]["type"] == "image/png"))
       && ($_FILES["avatar"]["size"] <= 1048576)    // 小于等于 1000 kb
       && in_array($extension, $allowedExts))
       {
           if ($_FILES["avatar"]["error"] > 0)
           {
               $res['ret'] = 0;
               $res['msg'] = "提交错误: " . $_FILES["avatar"]["error"] . ", 请重试!";
               return $this->echoJson($response, $res);
           }else {
               $user=Auth::getUser();
               $userid=$user->id;
               $filename = $userid."_".time().".".$temp[1];
			   $filePath = "upload/announcement/" . $filename;    //上传到哪个位置
	           $uploadUrl = move_uploaded_file($_FILES['avatar']['tmp_name'],$filePath);
 
	           $addr = '/'.$filePath;
 
               return $this->echoJson($response, $addr);
		   }
	  
	   } else {
          $res['ret'] = 0;
          $res['msg'] = "非法的文件格式,或文件大于1M, 请检查!";
          return $this->echoJson($response, $res);
       }
    }
    public function delete($request, $response, $args)
    {
        $id = $request->getParam('id');
        $ann = Ann::find($id);
        if (!$ann->delete()) {
            $rs['ret'] = 0;
            $rs['msg'] = "删除失败";
            return $response->getBody()->write(json_encode($rs));
        }
        $rs['ret'] = 1;
        $rs['msg'] = "删除成功";
        return $response->getBody()->write(json_encode($rs));
    }

    public function ajax($request, $response, $args)
    {
        $datatables = new Datatables(new DatatablesHelper());
        $datatables->query('Select id as op,id,date,content,status from announcement');

        $datatables->edit('op', function ($data) {
            return '<a class="btn btn-primary btn-sm" href="/admin/announcement/'.$data['id'].'/edit">编辑</a>
                    <a class="btn btn-secondary btn-sm" id="delete" value="'.$data['id'].'" href="javascript:void(0);" onClick="delete_modal_show(\''.$data['id'].'\')">删除</a>';
        });
		$datatables->edit('status', function ($data) {
			if($data['status'] == 0){
				return "不发信";
			}
		    elseif($data['status'] == 1){
			    return "未发信";
			}elseif($data['status'] == 2){
				return "正在发信";
			}else{
				return "发信完";
			}
		});
        $datatables->edit('DT_RowId', function ($data) {
            return 'row_1_'.$data['id'];
        });

        $body = $response->getBody();
        $body->write($datatables->generate());
    }
}
