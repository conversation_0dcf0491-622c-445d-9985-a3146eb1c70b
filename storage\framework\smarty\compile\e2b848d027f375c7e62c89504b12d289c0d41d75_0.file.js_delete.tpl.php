<?php
/* Smarty version 3.1.33, created on 2022-07-17 19:28:38
  from '/www/wwwroot/www.shadowingy.com/resources/views/czssr/table/js_delete.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62d3f2661824c5_05131353',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'e2b848d027f375c7e62c89504b12d289c0d41d75' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.com/resources/views/czssr/table/js_delete.tpl',
      1 => 1548128136,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_62d3f2661824c5_05131353 (Smarty_Internal_Template $_smarty_tpl) {
?>table_1
    .row('#row_1_' + deleteid)
    .remove()
    .draw();
<?php }
}
