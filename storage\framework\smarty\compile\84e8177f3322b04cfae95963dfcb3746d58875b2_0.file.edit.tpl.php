<?php
/* Smarty version 3.1.33, created on 2022-06-27 14:43:44
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/nodegroup/edit.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62b951a091b8b6_24091015',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '84e8177f3322b04cfae95963dfcb3746d58875b2' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/nodegroup/edit.tpl',
      1 => 1571934134,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/main.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:admin/footer.tpl' => 1,
  ),
),false)) {
function content_62b951a091b8b6_24091015 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Edit Group</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/nodegroup">群组设置</a></li>
                  <li class="breadcrumb-item active" aria-current="page">编辑群组</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/node" class="btn btn-sm btn-neutral">节点</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">群组设置 #<?php echo $_smarty_tpl->tpl_vars['ngg']->value->name;?>
</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">群组名称:</label>
							<input id="name" class="form-control form-control-sm" type="text" value="<?php echo $_smarty_tpl->tpl_vars['ngg']->value->name;?>
">
						</div>
						<div class="form-group">
							<label class="form-control-label">群组等级(仅数字):</label>
							<input id="level" class="form-control form-control-sm" type="number" value="<?php echo $_smarty_tpl->tpl_vars['ngg']->value->level;?>
">
						</div>
					</div>
				<div class="modal-footer">
                    <button id="submit" type="button" class="btn btn-primary">确认提交</button>
				</div>
				</div>
			</div>
			
        </div>
      </div><!--row-->
	  
		<?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php $_smarty_tpl->_subTemplateRender('file:admin/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php echo '<script'; ?>
>

    window.addEventListener('load', () => {
        function submit() {

            $.ajax({
                type: "PUT",
                url: "/admin/nodegroup/<?php echo $_smarty_tpl->tpl_vars['ngg']->value->id;?>
",
                dataType: "json",
                data: {
					name: $$getValue('name'),
					level: $$getValue('level')
                },
                success: data => {
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            });
        }

		$("#submit").on("click", submit);

    });
    
<?php echo '</script'; ?>
>

<?php }
}
