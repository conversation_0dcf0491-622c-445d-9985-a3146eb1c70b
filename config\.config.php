<?php

/**
 * User: 
 * Date: 2019/9/12
 * Time: 8:16 AM
 */

//config迁移附注（由开发者填写本次config迁移后需要注意的地方，站长勿动）
//如需换行，直接换行即可，无需换行符
//【新增/删除】config无需写入迁移附注
$System_Config['config_migrate_notice']=
'node_offline_warn 被改名为 enable_detect_offline
enable_register 和 enable_invite_code 合并为 register_mode
enable_auto_clean_unused 和 enable_auto_clean_unused_days 合并为auto_clean_unused_days
enable_auto_clean_uncheck 和 enable_auto_clean_uncheck_days 合并为 auto_clean_uncheck_days
enable_account_expire_delete 和 enable_account_expire_delete_days 合并为account_expire_delete_days
enable_class_expire_reset_traffic 重命名为 class_expire_reset_traffic
现在，用户账户过期时，流量会被强制重置为0
enable_geetest_* 已变更为 enable_*_captcha

';
$System_Config['version']='1.1.12';	//Cjssr标准运营版1开头//勿动

//搭建成功请注意定时任务是否正常添加，xshell输入  crontab -e查看
//30 22 * * * php /网站目录/xcat sendDiaryMail
//0 0 * * * php -n /网站目录/xcat dailyjob
//*/1 * * * * php /网站目录/xcat checkjob
//*/1 * * * * php /网站目录/xcat syncnode

//基本设置--------------------------------------------------------------------------------------------
$System_Config['CloseSite'] = 'false';							//true关闭前端统一跳转到404，false打开前端正常访问
$System_Config['nolocaltions'] = ['新疆','西藏','内蒙古'];            //禁止访问地区列表只支持国内只支持省份,国外只支持国家如美国,日本,韩国,新加坡,
$System_Config['key'] = '114514191981019907893211098';			//!!! 瞎 jb 修改此key为随机字符串确保网站安全 !!!
$System_Config['debug'] =  'true';								//正式环境请确保为 false
$System_Config['appName'] = 'Shadowrocket官网';							//站点名称
$System_Config['baseUrl'] = 'https://shadowingy.cf';					//站点地址
$System_Config['apiUrl'] = 'https://shadowingy.cf';                 //后端webapi/支付回调的地址
$System_Config['stop_subUrl'] = 'false';	                           //是否禁用订阅域名访问  true ,将返回404错误页面
$System_Config['stop_apiUrl'] = 'false';                           //是否禁用api域名不带参数访问  true ,将返回404错误页面
$System_Config['subUrl'] = 'https://apixhuojian.vercel.app/link/';	      //订阅地址，如需和站点名称相同，请不要修改
$System_Config['muKey'] = 'shxyz21090623333';								//用于校验ss-go mu的请求，可以随意修改，但请保持前后端一致
$System_Config['db_driver'] = 'mysql';							//数据库程序
$System_Config['db_host'] = '**************';						//数据库地址
$System_Config['db_database'] = 'shadowingypro_xy';						//数据库名
$System_Config['db_username'] = 'shadowingypro_xy';							//数据库用户名
$System_Config['db_password'] = 'xFwypG8HzeGje3Tw220302';						//用户名对应的密码
$System_Config['newtheme_date'] = '2020-03-21 06:43:41';           //新主题安装日期; 用于结算AFF的返利开始日期//根据你的实际情况填写
$System_Config['verify'] = '33c7ba754599c5c1428e417583c344f6';    //新主题身份证

//发布页配置----------------------------------------------------------
$System_Config['openpage'] = 'true';    //是否启用发布页
$System_Config['site-description'] = 'Shadowrocket官网';    //一句话描述你的发布页
//网站描述
$System_Config['description'] = '拥有/全/球/诸多/骨/干C/N/2优/化/线/路, 支持Android/IOS/Windows/Mac/网/络/加/,速全球网络中继服务，随时随处尽情使用';
//网站关键词
$System_Config['keywords'] = 'ssr加速,全球节点,质量线路,海外贸易,游戏加速';
//XXXX
$System_Config['engname'] = 'Shadowrocket官网';

// 订阅中的公告信息
// 使用数组形式，将会添加在订阅列表的顶端
// 可用于为用户推送最新地址等信息，尽可能简短且数量不宜太多
$System_Config['sub_message'] = ["新域名 QQ:1593392868"];
//邮件设置--------------------------------------------------------------------------------------------
$System_Config['mailDriver'] = 'smtp';	//发送邮件方式：none / mailgun / smtp / sendgrid 
$System_Config['sendPageLimit']= 50;	//发信分页 解决大站发公告超时问题

# mailgun
$System_Config['mailgun_key'] = '';
$System_Config['mailgun_domain'] = '';
$System_Config['mailgun_sender'] = '';

# smtp
$System_Config['smtp_host'] = 'smtpdm-ap-southeast-1.aliyun.com';
$System_Config['smtp_username'] = '<EMAIL>';
$System_Config['smtp_port'] = '465';
$System_Config['smtp_name'] = '<EMAIL>';
$System_Config['smtp_sender'] = '<EMAIL>';
$System_Config['smtp_passsword'] = 'HY65tgbnmju7890';
$System_Config['smtp_ssl'] = 'true';

# sendgrid
$System_Config['sendgrid_key'] = '';
$System_Config['sendgrid_sender'] = '';


//备份设置--------------------------------------------------------------------------------------------
$System_Config['auto_backup_email']='<EMAIL>';		//接收备份的邮箱
$System_Config['auto_backup_passwd']='123321';	//备份的压缩密码
$System_Config['backup_notify']='';		//备份通知到TG群中
//自动备份需要定时任务
//例1：每20分钟备份1次（若间隔大于60分钟，看例2）：
//*/20 * * * * php -n /网站目录/xcat backup
//例2：每20小时备份1次（若间隔大于24小时，自行Google）：
//0 */20 * * * php -n /网站目录/xcat backup

//用户注册设置-----------------------------------------------------------------------------------------
$System_Config['register_mode'] = 'open';					//注册模式。close：关闭，open：开放，invite：仅限邀请码
$System_Config['defaultTraffic'] = '0';						//用户初始流量 单位GB
$System_Config['user_expire_in_default']='365';			//用户账户过期时间，在注册时设置。（天）
$System_Config['user_class_default']='0';					//用户注册等级，在注册时设置。
$System_Config['user_class_expire_default']='0';		//用户等级过期时间，在注册时设置。（小时）
$System_Config['user_conn']='1';							//用户注册客户端数量限制，0为不限制
$System_Config['user_speedlimit']='100';				//用户注册速度默认限制，0为不限制
$System_Config['reg_auto_reset_day']='0';					//注册时的流量重置日，0为不重置
$System_Config['reg_auto_reset_bandwidth']='0';				//需要重置的流量，0为不重置
$System_Config['ramdom_group']='1';							//注册时随机分组，注册时随机分配到的分组，多个分组请用英文半角逗号分隔。
$System_Config['reg_method']='chacha20-ietf';					//注册时默认加密方式
$System_Config['reg_protocol']='auth_aes128_md5';					//注册时默认协议
$System_Config['reg_protocol_param']='';					//注册时默认协议参数
$System_Config['reg_obfs']='tls1.2_ticket_auth';						//注册时默认混淆方式
$System_Config['reg_obfs_param']='';						//注册时默认混淆参数
$System_Config['reg_forbidden_ip']='*********/8,::1/128';	//注册时默认禁止访问IP列表，半角英文逗号分割
$System_Config['min_port']='10000';							//用户端口池最小值
$System_Config['max_port']='65535';							//用户端口池最大值
$System_Config['reg_forbidden_port']='';					//注册时默认禁止访问端口列表，半角英文逗号分割，支持端口段
$System_Config['mu_suffix']='support.apple.com';				//单端口多用户混淆参数后缀，可以随意修改，但请保持前后端一致//developer.microsoft.com
$System_Config['mu_regex']='%suffix';				//单端口多用户混淆参数表达式，%5m代表取用户特征 md5 的前五位，%id 代表用户id,%suffix 代表上面这个后缀

#邀请链接
$System_Config['inviteNum'] = '10000';			//注册后的邀请链接可用次数
$System_Config['invite_get_money']='1';		//新用户通过私人邀请链接注册时，获得奖励金额(作为初始资金)

$System_Config['invite_get_traffic']='10';	//新用户通过私人邀请链接注册时，获得奖励流量(GB)
$System_Config['invite_price']='-1';		//用户购买邀请码所需要的价格，价格小于0时视为不开放购买
$System_Config['custom_invite_price']='-1';		//用户定制邀请码所需要的价格，价格小于0时视为不开放购买

#邮箱验证
$System_Config['enable_email_verify']='false';		//是否启用注册邮箱验证码
$System_Config['email_verify_ttl']='900';			//邮箱验证码有效期
$System_Config['email_verify_iplimit']='10';		//验证码有效期内，单IP可请求验证码次数


//已注册用户设置---------------------------------------------------------------------------------------
#基础
$System_Config['checkinMin'] = '10';				//用户签到最少流量 单位MB
$System_Config['checkinMax'] = '100';			//用户签到最多流量
$System_Config['checkinexday'] = '1';			//用户签到1次增加多少天账户有效期，等于0为关闭此功能
$System_Config['auto_clean_uncheck_days']='-1';	//自动清理多少天没签到的0级用户，小于等于0时关闭
$System_Config['auto_clean_unused_days']='-1';	//自动清理多少天没使用的0级用户，小于等于0时关闭
$System_Config['auto_clean_min_money']='1';		//余额低于多少的0级用户可以被清理
$System_Config['code_payback']='20';			//新用户充值默认返利百分比 
$System_Config['invite_gift']='2';				//邀请新用户时 获得流量奖励，单位G//
$System_Config['invite_money']='0';				//邀请新用户时获得金钱奖励，单位元//等于0时不开启功能
$System_Config['invite_class']='1';				//邀请新用户时获得VIP累计时长，这里填的是邀请1个新用户累计多少小时(单位:小时)，等于0不开启功能
$System_Config['enable_bought_reset']='false';		//购买时是否重置流量//
$System_Config['port_price']='-1';				//用户随机重置端口所需要的价格，价格小于0时视为不开放购买
$System_Config['port_price_specify']='-1';		//用户指明钦定端口所需要的价格，价格小于0时视为不开放购买

#高级
$System_Config['class_expire_reset_traffic']='0';		//等级到期时重置为的流量值，单位GB，小于0时不重置
$System_Config['account_expire_delete_days']='-1';		//账户到期几天之后会删除账户，小于0时不删除
$System_Config['enable_kill']='false';					//是否允许用户注销账户
$System_Config['notify_limit_mode'] = 'mb';			//false为关闭，per为按照百分比提醒，mb为按照固定剩余流量提醒
$System_Config['notify_limit_value'] = '200';			//当上一项为per时，此处填写百分比；当上一项为mb时，此处填写流量
$System_Config['mergeSub'] = 'true';						//合并订阅设置 可选项 false / true  //已废弃
$System_Config['keep_connect'] = 'false';				//是否开启用户流量耗尽后降低速率至 1Mbps 而不断网

#订阅记录
$System_Config['subscribeLog'] = 'true';			    //是否记录用户订阅日志
$System_Config['subscribeLog_keep_days'] = '30';		    //订阅记录保留天数

//Telegram bot设置------------------------------------------------------------------------------------
$System_Config['enable_telegram']='true';			//是否开启Telegram bot
$System_Config['telegram_token']='**********************************************';				//Telegram bot,bot 的 token ，跟 father bot 申请
$System_Config['telegram_chatid']='-1001219167898';				//Telegram bot,群组会话 ID,把机器人拉进群里之后跟他 /ping 一下即可得到。
$System_Config['enable_tuling']='false';			//是否开启图灵机器人
$System_Config['tuling_apikey']='';					//图灵机器人API Key
$System_Config['tuling_apisecert']='';				//图灵机器人密钥
$System_Config['telegram_bot']='shadowrocket_bot';				//Telegram 机器人账号
$System_Config['telegram_group_quiet']='false';		//Telegram 机器人在群组中不回应
$System_Config['telegram_qrcode']='online';	//二维码解码方式，online，phpzbar，zxing_online，zxing_local
$System_Config['telegram_request_token']='**********************************************';		//Telegram 机器人请求Key，随意设置，由大小写英文和数字组成，更新这个参数之后请 php xcat setTelegram
$System_Config['enable_welcome_message']='true';	//机器人发送欢迎消息
$System_Config['finance_public']='false';			//财务报告是否向Telegram群公开
$System_Config['telegram_grouplink']='https://t.me/Shadowrocket2';           //telegram群邀请链接


//沟通设置--------------------------------------------------------------------------------------------
//客服系统设置，注册地址 https://crisp.chat/  将enable_crisp改为true，填入id
$System_Config['enable_crisp']='true';		//是否开启客服系统
$System_Config['crisp_id']='0c6e76a7-384a-4b5e-9a2f-c336b0e507ce';			//客服系统ID ID查看路径：crisp.chat-->设置-->网站设置-->显示整合-->html

# Server酱  用户提交新工单或者回复工单时用微信提醒机场主 http://sc.ftqq.com/
$System_Config['useScFtqq'] = 'false';		//是否开启工单Server酱提醒--接下面工单系统打开才生效
$System_Config['ScFtqq_SCKEY'] = 'xxxxxx';	//请填写您在Server酱获取的SCKEY  请仔细检查勿粘贴错UserController.php

#工单系统设置
$System_Config['enable_ticket']='true';		//是否开启工单系统
$System_Config['pay_ticket_price']='0';			//工单系统是否收费//每次新建工单所需价格/元/等于0为不开启
$System_Config['mail_ticket']='true';		//是否开启工单邮件提醒

#管理员联系方式设置
$System_Config['enable_admin_contact']='false';			//是否开启管理员联系方式
$System_Config['admin_contact1'] = '客服：点击右下角QQ联络管理员。';		//QQ、邮箱、微信仅用于举例
//$System_Config['admin_contact2'] = 'TG联系：<a href="https://t.me/Fanyum" target="_blank">@Fanyum</a>';	//也可以写电话、tg等其他联系方式
$System_Config['admin_contact3'] = '请合法使用，如有疑问联系客服';		//没有格式要求，想怎么写就怎么写，可留空
$System_Config['admin_qq_bottom'] = '83898421';     //网站footer.tpl底部QQ联系方式，如果留空则不显示.
  
#TG联系方式
$System_Config['telegram_channel_name'] = '加入Shadowrocket-TG频道';   //TG频道名称, 显示在用户中心公告栏，可不填
$System_Config['telegram_channel_link'] = 'https://t.me/Shadowrocket3';	//TG频道链接, 显示在用户中心公告栏，可不填
$System_Config['telegram_group_name'] = '加入Shadowrocket-TG交流群';		//TG群组名称, 显示在用户中心公告栏，可不填
$System_Config['telegram_group_link'] = 'https://t.me/Shadowrocket2';		//TG群组链接, 显示在用户中心公告栏，可不填

#用户中心首页消息弹窗
$System_Config['notice_dialog'] = 'false';      //是否开启消息弹窗，显示在用户中心主页可选 true / false
$System_Config['notice_massages'] = '<b>本站节点使用了SSR+V2ray双端支持全平台设备，请点击左边的下载和教程--软件下载订阅对应的订阅即可使用~!</b>';   //上一项为true，此项才生效。<b>小号字体</b>

#快捷登陆设置
$System_Config['enable_tg'] = 'true';       //telegram登陆设置；//未开启TG功能请把此项关闭 false;

#商店设置
$System_Config['shops_class'] = [
   'class_1' => '1',
   'class_2' => '2',
   'class_3' => '3',
   'class_4' => '4'
];//定义首页的3个套餐ID;

$System_Config['shops_price'] = [
  'class_1' => [
    'c1_price_1'=>'2',
    'c1_price_2'=>'3',
    'c1_price_3'=>'4',
    'c1_price_4'=>'13'
	],
  'class_2' => [
    'c2_price_1'=>'5',
    'c2_price_2'=>'6',
    'c2_price_3'=>'7'
	],
  'class_3' => [
    'c3_price_1'=>'8',
    'c3_price_2'=>'9',
    'c3_price_3'=>'10'
	],
  'class_4' => [
    'c4_price_1'=>'11',
    'c4_price_2'=>'1',
    'c4_price_3'=>'13'
    
	],
]; //前台商店设置4个等级套餐对应的月付,季度,年付套餐ID,(用于获取价格,需要你创建对应的月付,季度,年付套餐)
$System_Config['shop_free'] = '';        //请指定免费体验的商品套餐ID，如留空，前台商店将不显示免费体验的订阅计划 
$System_Config['shop_traffic'] = '0.2';  //请指定流量包价格 请遵循 1G流量=？元的设置
$System_Config['shop_odm'] = '';         //请指定定制版套餐ID,该功能在于你有单独需要定制客户的情况下,新建单独的套餐给对方买下付款用.
$System_Config['refund'] = 'true';       // 是否开启剩余套餐折算退款到余额的功能 用于客户自主升级套餐等
$System_Config['refund_rate'] = '0.6';  // 折算费率,0.9代表 (套餐原价/天数)*剩余天数*0.9*100% = 结算余额,考虑到有优惠码的情况，这里一般填优惠码的费率
$System_Config['resund_list'] = ['1','2','3','4','5','6','7','8','9','10','11','12','13','14'];    // 禁止退款的套餐商品ID，数组形式,隔开。

#节点设置
$System_Config['show_free_nodes'] = 'true';  //是否在前台显示免费节点

  
//验证码设置------------------------------------------------------------------------------------------
// geetest官网：https://www.geetest.com/ 需要申请开通，recaptcha类似
$System_Config['captcha_provider'] = 'geetest'; //取值 recaptcha | geetest(极验) | CaptchaDX(顶象购买QQ2381271904)试用7天,5000次

$System_Config['recaptcha_sitekey'] = ''; //不启用请留空
$System_Config['recaptcha_secret'] = '';  //不启用请留空

$System_Config['geetest_id'] = 'b31335edde91b2f98dacd393f6ae6de8';   //不启用请留空//f1824e014de2d7438b8690ac93533b27  原备份
$System_Config['geetest_key'] = '170d2349acef92b7396c7157eb9d8f47';  //不启用请留空//11c45f05631f7d91ef2caf62abb0312c   原备份

$System_Config['CaptchaDX_AppId'] = '';   //不启用请留空
$System_Config['CaptchaDX_AppSecret'] = '';  //不启用请留空

$System_Config['enable_reg_captcha'] = 'true';  //启用注册验证码
$System_Config['enable_login_captcha'] = 'false';  //启用登录验证码
$System_Config['enable_reset_captcha'] = 'false';  //启用重置密码验证码//建议开启验证.
$System_Config['enable_checkin_captcha'] = 'false';  //启用签到验证码//已取消功能请保持false


//支付系统设置----------------------------------------------------------------------------------------
#取值 none | yftpay | paymentwall | spay | paypal |左侧没对接,右侧已对接 | f2fpay | codepay | umikm 优米支付(微信小微商户) | tomatopay | payjs | flyfoxpay | idtpay
//PS1:  payment_system_A----为支付宝，payment_system_B----为微信支付；
//PS2:  如果单独使用其中一个,请把另外的支付留空；不可2个同时写上2个 payjs/f2fpay/umikm;
//PS3:  例如你选择的是payjs(微信小微商户),请填写payjs 在payment_system_B;
//PS4:  例如你选择的是f2fpay(支付宝面对面),请填写f2fpay 在payment_system_A;
//PS5:  例如你选择的是flyfoxpay/codepay/chenAlipay/tomatopay,请把2个支付都填写上flyfoxpay/codepay/chenAlipay/tomatopay;只要该第四方支付有微信和支付宝共存就这么填;
//PS6:  数字货币为独立支付；可以并存；留空为不开启；

$System_Config['payment_system']=''; //没事别瞎调用//备用支付通道

//新支付通道
$System_Config['payment_system_A']='f2fpay';  //支付宝面对面支付  //这里填的支付宝 参照PS5  PS2
$System_Config['payment_system_B']='mycode';  //优米支付(小微商户)  //这里填的微信 参照PS5  PS2
$System_Config['payment_system_c']='bitpay_secret';  //优米支付(小微商户)  //这里填的微信 参照PS5  PS2

#mycode
$System_Config['mycode_id']='155880808';					//码支付ID
$System_Config['mycode_key']='83898421';		 //码支付通信密钥

#umikm 优米支付(小微商户委托注册地址) https://payuser.umikm.com/Register?code=199164981
$System_Config['umikm_mch_id'] = 'xxxxxx';  //商户号
$System_Config['umikm_appid'] = 'xxxxxx';   //应用id  //应用公钥  //应用私钥
$System_Config['umikm_sysPublicKey'] = 'xxxxxx';  
$System_Config['umikm_privateKey'] = 'xxxxxx';

#codepay码支付
#wiki地址:https://goo.gl/dRwRDi  http://t.cn/RnsWjtB //注册地址：https://codepay.fateqq.com/i/11577
$System_Config['codepay_id']='128711';					//码支付ID
$System_Config['codepay_key']='HsiAWGb0Utop1i9ylUr3FAXyHxc2uYqJ';		 //码支付通信密钥

#alipay,f2fpay //面对面注册地址：https://open.alipay.com
$System_Config['f2fpay_app_id']='2019011963144079';             //2019060321007042213西安淼宝OK
$System_Config['f2fpay_p_id']='2088431068545325';
$System_Config['alipay_public_key']='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAj2ElP53EES0bNs8yYGy3jZbv3QNmLq5vyzhrP5cemHjrXIRIyc1bCYZy/eBXVWzUnzf7booTafCzgXOUDfD09+sJ9/cJjE3zVjLroHMBfmvPcf6aE8RE/7DRnOoFuARwfGrqpVGMedGvof0SeuDS0y1VPv18Qa0QurM33xov1/uHz8QWpC+nrDOVoUNL7jOCfKdPEPtwt4a1OgOwnUfHmOrh1dr5g3sCMb0GTVYhB7kHpBd9n4XnCJFy8lA/njX93LS2g0tLCWs3vCf2Ida/OXS91Fw6Zoyd3nIXuh0+ndna2EXO8tg4Gagz6Z78easEjBWiwyB73JxvBFaB702dTwIDAQAB';
$System_Config['merchant_private_key']='MIIEowIBAAKCAQEAwhzGt9TsNSGAKjZl7lo0uF1XJjCLPGHh8JJOzc+7YTPm79DSo8HGLjZxvzRqA1aPUZjW4bBDsahi3hwiHdG7gvZAF3HcdlXNuOIL/lWQRiwgQiqlsN3cXQtM8KZScXvBdlNJOVji5UURJHC5lCM9p/ujDBD6yBEy25DbMmNFPKTK2qJjJbO0INQJiNjBBYKpwYTYFLaMRfLdUe9aiaf6LpuFYcIaJEksiGTxVCmPmTqkvVM2xgEBdbRIUOz6iBEQcPoAPYTl2AHJZMydpHfzlkCOYC00eo3CpTs0UyMQRbpeHdeDP0QhqBNwY8nk/s2k+dghjredraQXykaXRbJ7bQIDAQABAoIBACfcZOpHLSJT+M+yUAg8OetKL6VF/rGtoAoY4VneOKpjUB8bEMZT+nBu+bi7+LEqy0+ERUwfOCd5XqQhvtfEw+7Gxq6J0S3zvMUkljXqODBQy2cK/u9s/92Tfn9EiFWJhlbYD4dZO269WiHTjChhfD3MrNTDf9wpu/neq0STohormPUXAEoYKllE0zGPQfGPT29xplnXfqczNp65fkcAai8B4Oipj1txIorjwSSDxjHsYMdIMycqy9+rgJxDQMkmpfZbMDybpVrh0gpQ/Wl2ZPh9Bs5Iz0nJ9R92BYdKseWJ+fW5PLi9cxGI4krQl3eBXuVW9tum+wknvJenHtkEgwECgYEA96A2Of10TjwGfagqCej8egtQBC7MMkJWvr034Uc1HFkOj2yHXJmat9pZcP2XFdywBOwbRXMx8pYKUECExeHDPRpaoZnC6R7H6D5uVhp861OvwaSfgndfI3PwDGCrDDbXOoyen0nkx6Oe/BzWdgJqsIcXosxal3pxpqel+7HQSM0CgYEAyK1Ha/rtoeZ+GePBZ4uW8enZkDocQZ1PsSDyHnA9qHgTG0TioSRBw3gXrMTOnCwdT0iyb97tSJP3ued5V/wuRAGl03CDq96QLzbSZVGtwY6UAkYn+RsWmTsMl6iBqBAH7/JnEViDOyUPTDjlUFICH+GdmtL95oGq7the0dOBfSECgYAW+vVNWGoswIFRtAOCu+6CXP5mBPnKSEHIncLL01HoeZNSWB4Px+mF2mumgsI8syharJazUUd3XdlDLVmlC5iU2RdEHALso4Cl2vVeZELni/Kpo99xuiZIIgBLBcWZ8pTyyA4IdlEW/eZJO1vjw8Gm9EKLRy+5ExHtB23t98h+mQKBgG9AyYDOq2F/dFeMEdQC6lsRZ0FzRekGeoDbn/9KAJTehNEwXmgxvpfMg2uP7G9ecZ1b0zj8IRaEmekhr/8cWtXvB+zxyr3E9LCINxpervLIxbuBA3YyE1Q+DVwi2ka/JHddLtDJ/m8Su6lpPZBK9syP+x0zbfmV15r0r5SqKnahAoGBAMkVk93TQMGxnzK/F4Ny/DI7rsNu0el1mY4C2hBpZ8ZVxhytJfiDJ4+BjpuqctSxdy3e45Ly+XqJb1qycdPDZM4nIanGLrEaqXcS3hWRmbu6a9riwdDKlIQmq5XUXcYd7yCn10HvIBa5jfcIbADigQO7tM/qR/WgJA45Qo2mpCZc';
$System_Config['serverName']='Shadowrocket官网';    //面对面支付详情的商家名称,不建议与VPN SSR等违规信息相关.

#payjs  //小微商户委托注册地址：https://payjs.cn
$System_Config['payjs_mchid']='';
$System_Config['payjs_key']='';	

#flyfoxpay // 跃空聚合支付  TG申请: https://t.me/flyfoxpay
$System_Config['flyfoxpay']=[
         'config'=>[
           'hid'=>'xxxxxx',      //商户号
           'key'=>'xxxxxx',   //安全验证码key
           'mail'=>'xxxxxx',    //登录邮箱账号
		   'siteapi' => 'key的前5位数'   //api链接码
         ]
];

#tomatopay番茄云支付
#使用教程:https://swapidc.fanqieui.com/?t/329.html  tg群 https://t.me/fanqiepay
$System_Config['tomatopay'] = [
        'wxpay'=>[
            'mchid' => 'xxxxxx',   // 商户号
            'account' => 'xxxxxx', //您在番茄云支付的登录邮箱
            'token' => "xxxxxx" // 安全验证码
        ],
        'alipay'=>[
            'mchid' => 'xxxxxx',   // 商户号
            'account' => 'xxxxxx', //您在番茄云支付的登录邮箱
            'token' => "xxxxxx" // 安全验证码
        ],
 ];
#idtpay https://t.me/iDTPay_user
$System_Config['idtpay']=[
    'partner' => "", //商户号
    'key' => "C7FJF1N1Ac00", //商户key
    'sign_type' => strtoupper('MD5'),
    'input_charset' => strtolower('utf-8'),
    'subjects' => ['生活用品'],          //商品名称，目前无意义
    'transport' => 'https',                   //访问模式,根据自己的服务器是否支持ssl访问，若支持请选择https；若不支持请选择http
    'appname' => $System_Config['engname'],  //网站英文名
    'apiurl' => 'https://pay.idt.xyz/',     //支付网关 注意结尾的/符号
];
# BitPay 数字货币支付（比特币、以太坊、EOS等） 商户后台获取授权码 https://merchants.mugglepay.com/
#   客服和技术 24x7 在线支持： https://t.me/joinchat/GLKSKhUnE4GvEAPgqtChAQ
$System_Config['bitpay_secret']='1234567890';

#Paypal //暂时没写
$System_Config['Clientid']='';   //Client ID
$System_Config['Secret']='';      //Secret

#PaymentWall
$System_Config['pmw_publickey']='';
$System_Config['pmw_privatekey']='';
$System_Config['pmw_widget']='m2_1';
$System_Config['pmw_height']='350px';

#alipay,spay
$System_Config['alipay_id']='';
$System_Config['alipay_key']='';
$System_Config['amount']=[1,20,50,100,500];		//充值金额选项设定

#yftpay,https://pay.anypay.me/
$System_Config['yft_secret']='';
$System_Config['yft_accesskey']='';
#书生支付配置
$System_Config['payA'] = [
'config'=>[
'payment'=>'f2fpay', 
'images'=>'/theme/czssr/main/images/ali_pay.png', 
'type'=>'alipay' 
]
];
$System_Config['payB'] = [
'config'=>[
'payment'=>'leyupay',
'images'=>'/theme/czssr/main/images/wx_pay.png', 
'type'=>'wxpay' 
]
];
$System_Config['payC'] = [
'config'=>[
'payment'=>'leyupay',
'images'=>'/theme/czssr/main/images/qq_pay.png', 
'type'=>'qqpay' 
]
];

//其他面板显示设置------------------------------------------------------------------------------------------

#邀请返利中心短网址
$System_Config['Short_Link']='false';    //是否启用短网址替换
$System_Config['Short_api']='http://muzidwz.cn/sina_short.php?data=';  //短网址的API链接备用http://qingmeidwz.cn/shorten.php?url_long

#用户文档
$System_Config['enable_documents'] = 'false';	    //是否允许未登陆用户查看文档中心
$System_Config['documents_name'] = $System_Config['engname'] . ' 文档中心';	    //文档中心名称
$System_Config['remote_documents'] = 'false';	    //是否从远程加载文档中心，否的话请执行 php xcat initdocuments
$System_Config['documents_source'] = 'https://raw.githubusercontent.com/Czssr/PANEL_DOC/master/GeekQu';	    //远程文档加载地址

#后台商品列表 销量统计
$System_Config['sales_period']='30';	//统计指定周期内的销量，值为【expire/任意大于0的整数】

#国旗
//需要节点显示国旗，请把enable_flag改为true，填写节点名字实例：日本 东京VIP线路一
$System_Config['enable_flag']='true';			//启用该项之前务必先仔细阅读教程
$System_Config['flag_regex']='/.*?(?=\s)/';		//从站点全名中匹配【国家/地区】的正则表达式(php版)

#捐赠
$System_Config['enable_donate']='false';	//是否显示用户捐赠（所有收入将被公开）

#iOS账户显示
$System_Config['display_ios_class']='1';	//至少等级为多少的用户可以看见，小于0时关闭此功能也就是不显示在前台
$System_Config['ios_account']='联系客服安装';			//iOS账户
$System_Config['ios_password']='联系客服安装';			//iOS密码
//$System_Config['ios_account']='(ID1账户:<EMAIL>);ID2账户:<EMAIL>';			//iOS账户
//$System_Config['ios_password']='(ID1密码:Wwang1688);ID2密码：Gg19890428';			//iOS密码


//节点检测-----------------------------------------------------------------------------------------------
//检测被墙，亲测不准，仅作为参看，建议不开
//需要在定时任务中加入  */1 * * * * php /网站目录/xcat detectGFW
#GFW检测，请通过crontab进行【开启/关闭】
$System_Config['detect_gfw_interval']='7200';										//检测间隔，单位：秒，低于推荐值会爆炸
$System_Config['detect_gfw_port']='22';												//所有节点服务器都打开的TCP端口，常用的为22（SSH端口）或者填写你的单端口号
$System_Config['detect_gfw_url']='http://duankou.wlphp.com/api.php';	//检测节点是否被gfw墙了的API的URL
$System_Config['detect_gfw_judge']='$json_tcping[\'msg\'][\'status\']=="Openning"';				//判断是否被墙的依据，json_tcping为上方URL返回的json数组
$System_Config['detect_gfw_count']='2';												//尝试次数

#离线检测
$System_Config['enable_detect_offline']='false';
#离线检测是否推送到Server酱 请配置好上文的Server配置
$System_Config['enable_detect_offline_useScFtqq']='false';// Job.php

//V2Ray相关设置------------------------------------------------------------------------------------------//未调用
//$System_Config['v2ray_port']='443';					//V2Ray端口
//$System_Config['v2ray_protocol']='HTTP/2 + TLS';	//V2Ray协议
//$System_Config['v2ray_alter_id']='32';
//$System_Config['v2ray_level']='0';

//以下所有均为高级设置（一般用不上，不用改---------------------------------------------------------------------
#杂项
$System_Config['enable_login_bind_ip']='false';		//是否将登陆线程和IP绑定
$System_Config['authDriver'] = 'cookie';			//不能更改此项
$System_Config['salt'] = '';						//密码加密用，请留空
$System_Config['sessionDriver'] = 'cookie';			//可选: cookie,redis
$System_Config['cacheDriver'] = 'cookie';			//可选: cookie,redis
$System_Config['tokenDriver'] = 'db';				//可选: db,redis
$System_Config['jump_delay']='1200';				//跳转延时，单位ms，不建议太长
$System_Config['theme']    = 'czssr';			//主题默认'material'
$System_Config['pacp_offset']='-20000';				//VPN 端口偏移
$System_Config['pacpp_offset']='-20000';
$System_Config['Speedtest_duration']='6';			//显示多长时间的测速记录
$System_Config['login_warn']='false';				//异地登陆提示
$System_Config['timeZone'] = 'PRC';					//PRC 天朝时间  UTC 格林时间
$System_Config['pwdMethod'] = 'sha256';				//密码加密   可选 md5,sha256，argon2i（argon2i需要至少php7.2）
$System_Config['db_charset'] = 'utf8';
$System_Config['db_collation'] = 'utf8_general_ci';
$System_Config['db_prefix'] = '';
$System_Config['muKeyList'] = ['　'];                //多 key 列表
$System_Config['mu_port_migration'] = 'false';       //为后端直接下发偏移后的端口   //未调用
$System_Config['relay_port_migration'] = 'false';    //为中转规则下发偏移后的端口，此项未经测试   //未调用
$System_Config['add_emoji_to_node_name'] = 'false';  //为部分订阅中默认添加 emoji
$System_Config['add_appName_to_ss_uri'] = 'true';    //为 SS 节点名称中添加站点名
$System_Config['subscribe_client'] = 'false';        //下载协议客户端时附带节点和订阅信息

// 审计自动封禁开关
$System_Config['enable_auto_detect_ban'] = 'false';  //false为关闭，true为开启

// 管理员不受审计限制
$System_Config['auto_detect_ban_allow_admin'] = 'true';

// 审计封禁的例外用户 ID
$System_Config['auto_detect_ban_allow_users'] = [];

// 审计封禁判断类型：
//   - 1 = 仁慈模式，每触碰多少次封禁一次
//   - 2 = 疯狂模式，累计触碰次数按阶梯进行不同时长的封禁
$System_Config['auto_detect_ban_type'] = '1';

// 仁慈模式每次执行封禁所需的触发次数
$System_Config['auto_detect_ban_number'] = '3000';  //达到触碰次数

// 仁慈模式每次封禁的时长 (分钟)
$System_Config['auto_detect_ban_time'] = '1';   //封禁时长

// 疯狂模式阶梯
// key 为触发次数
//   - type：可选 time 按时间 或 kill 删号
//   - time：时间，单位分钟
$System_Config['auto_detect_ban'] = [
    100 => [
        'type' => 'time',
        'time' => '120'
    ],
    300 => [
        'type' => 'time',
        'time' => '720'
    ],
    600 => [
        'type' => 'time',
        'time' => '4320'
    ],
    1000 => [
        'type' => 'kill',
        'time' => '0'
    ]
];

#aws
$System_Config['aws_access_key_id'] = '';
$System_Config['aws_secret_access_key'] = '';

#redis
$System_Config['redis_scheme'] = 'tcp';
$System_Config['redis_host'] = '127.0.0.1';
$System_Config['redis_port'] = '6379';
$System_Config['redis_database'] = '0';
$System_Config['redis_password']="";

#Radius设置
//需要的话就加入定时任务
//*/1 * * * * php /网站目录/xcat synclogin
//*/1 * * * * php /网站目录/xcat syncvpn
//*/1 * * * * php -n /网站目录/xcat syncnas
$System_Config['enable_radius']='false';			//是否开启Radius
$System_Config['radius_db_host']='';				//以下4项为Radius数据库设置
$System_Config['radius_db_database']='';
$System_Config['radius_db_user']='';
$System_Config['radius_db_password']='';
$System_Config['radius_secret']='';					//Radius连接密钥

#Cloudxns
$System_Config['enable_cloudxns']='false';			//是否开启Cloudxns
$System_Config['cloudxns_apikey']='fc3a62b0e0140f592fb5e62558cfce988';				//自己去 cloudxns.net 申请
$System_Config['cloudxns_apisecret']='cd7fa476893asdes9';
$System_Config['cloudxns_domain']='example.com';		//你的域名

#Cloudflare
$System_Config['cloudflare_enable']='false';										//是否开启 Cloudflare 解析
$System_Config['cloudflare_email']='<EMAIL>';								//Cloudflare 邮箱地址
$System_Config['cloudflare_key']='c2547eb745079dac9320b638f5e225cf483cc5cfdda41';	//Cloudflare API Key
$System_Config['cloudflare_name']='example.com';									//域名

#不安全中转模式，这个开启之后使用除了 auth_aes128_md5 或者 auth_aes128_sha1 以外的协议地用户也可以设置和使用中转
$System_Config['relay_insecure_mode']='false';		//强烈推荐不开启

#是否夹带统计代码，自己在 resources/views/{主题名} 下创建一个 analytics.tpl ，如果有必要就用 literal 界定符
$System_Config['enable_analytics_code']='false';

#在套了CDN之后获取用户真实ip，如果您不知道这是什么，请不要乱动
if ( isset($_SERVER["HTTP_X_FORWARDED_FOR"]) ) {
$list = explode("," , $_SERVER["HTTP_X_FORWARDED_FOR"]);
$_SERVER["REMOTE_ADDR"] = $list[0];
}


