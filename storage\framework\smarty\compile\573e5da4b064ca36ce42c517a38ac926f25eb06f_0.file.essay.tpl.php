<?php
/* Smarty version 3.1.33, created on 2022-02-06 15:29:40
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/essay.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_61ff78e4dd4397_36704829',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '573e5da4b064ca36ce42c517a38ac926f25eb06f' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/user/essay.tpl',
      1 => 1573213922,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:user/main.tpl' => 1,
    'file:user/footer.tpl' => 1,
  ),
),false)) {
function content_61ff78e4dd4397_36704829 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:user/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<link rel="stylesheet" href="/theme/czssr/main/css/quill.core.css" type="text/css">
<style>
   .doudong:hover { transform: translateY(-2px);}
</style>
     <!-- Header -->
	<div class="header pb-6 d-flex align-items-center" style="min-height: 500px; background-image: url(/theme/czssr/main/picture/profile-cover.jpg); background-size: cover; background-position: center top;">
      <!-- Mask -->
      <span class="mask bg-gradient-default opacity-8"></span>
      <!-- Header container -->
      <div class="container-fluid d-flex align-items-center">
        <div class="row">
          <div class="col-lg-10 col-md-10">
            <h1 class="display-2 text-white">Hello <?php echo $_smarty_tpl->tpl_vars['user']->value->user_name;?>
</h1>
            <p class="text-white mt-0 mb-5">This is our essays page. You can find something interesting in this page.</p>
            <div class="col-lg-7 col-8">
              <h6 class="h2 text-white d-inline-block mb-0">Essay</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">文章中心</li>
                </ol>
              </nav>
            </div>
			
          </div>
        </div>
      </div>
    </div>
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <div class="row">
        <?php if ($_smarty_tpl->tpl_vars['empty']->value != 0) {?>
	  <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['essays']->value, 'essay');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['essay']->value) {
?>
	    <div class="col-xl-4">
          <div class="card doudong">
            <!-- Card header -->
            <div class="card-header">
              <!-- Title -->
              <h4 class="text-ellipsis mb-0"><?php echo $_smarty_tpl->tpl_vars['essay']->value->title;?>
</h4>
            </div>
            <!-- Card body -->
            <div class="card-body">
              <div id="editor"></div>
			      <div v-html="content"><?php echo $_smarty_tpl->tpl_vars['essay']->value->content;?>
</div>
              <span style="font-size: 12px;"><?php echo $_smarty_tpl->tpl_vars['essay']->value->date;?>
</span>
			  </div>
            </div>
			
        </div>
      <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
		<?php } else { ?>
		<div class="col">
		    <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">掌柜可懒了.</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>掌柜可懒了,暂时莫得文章.</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
		</div>	
		<?php }?>
	 
      </div><!--row-->

	  
	  
	  
	<?php $_smarty_tpl->_subTemplateRender('file:user/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
echo '<script'; ?>
 src="/theme/czssr/main/js/quill.min.js"><?php echo '</script'; ?>
>	  

	<?php }
}
