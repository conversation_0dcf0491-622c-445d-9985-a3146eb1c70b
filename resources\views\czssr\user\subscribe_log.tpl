{include file='user/main.tpl'}
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Subscribe Logs</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
				  <li class="breadcrumb-item"><a href="/user/subscribe_log">订阅记录</a></li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/ticket/create" class="btn btn-sm btn-neutral">工单</a>
              <a href="/user/shop" class="btn btn-sm btn-neutral">前往商店</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">订阅记录查看</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<blockquote class="blockquote mb-0">
							<p class="description">您可在此查询您账户最近 {$config['subscribeLog_keep_days']} 天的订阅记录, 确保您的账户没有被盗用.</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
			
            <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">记录表</h3>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive">
				{$logs->render()}
					<table class="table align-items-center table-flush">
					<thead class="thead-light">
						<tr>
							<th>ID</th>
                            <th>订阅类型</th>
                            <th>IP</th>
                            <th>归属地</th>
                            <th>时间</th>
                            <th>User-Agent</th>
						</tr>
					</thead>
					<tbody class="list">
				    {foreach $logs as $log}
					
					<tr>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>#{$log->id}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$log->subscribe_type}</span>
						</td>
						<td>
							<span class="badge">{$log->request_ip}</span>
						</td>
						{assign var="location" value=$iplocation->getlocation($log->request_ip)}
						<td>
							<span class="badge">{iconv("gbk", "utf-8//IGNORE", $location.country)} {iconv("gbk", "utf-8//IGNORE", $location.area)}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$log->request_time}</span>
						</td>
						<td>
							<span class="badge">{$log->request_user_agent}</span>
						</td>
					</tr>
					 
					{/foreach}
					</tbody>
					</table>
				
				</div>
              </div>
            </div><!--card-->
		
        </div>
      </div><!--row-->
	  

	  {include file='user/footer.tpl'}
	  