<?php
namespace App\Services\Gateway;
use App\Services\View;
use App\Services\Auth;
use App\Services\Config;
use App\Models\Paylist;
class TomatoPay extends AbstractPayment
{

    public function purchase($request, $response, $args)
    {
        $type = $request->getParam('type');
        $price = $request->getParam('price');
        if($price <= 0){
            return json_encode(['errcode'=>-1,'errmsg'=>"非法的金额."]);
        }
        $user = Auth::getUser();
        $settings = Config::get("tomatopay")[$type];
        $pl = new Paylist();
        $pl->userid = $user->id;
        $pl->total = $price;
        $pl->tradeno = self::generateGuid();
        $pl->save();
        $fqaccount=$settings['account'];
        $fqkey=$settings['token'];
        $fqmchid = $settings['mchid'];
		$fqtype = 1;
		$fqtrade = $pl->tradeno;
	    $fqcny = $price;
        $data = [
            'account' => $settings['account'],
			'mchid' => $settings['mchid'],
			'type' => 1,
			'trade' => $pl->tradeno,
			'cny' => $price,
        ];
      $signs = md5("mchid=".$fqmchid."&account=".$fqaccount."&cny=".$fqcny."&type=1&trade=".$fqtrade.$fqkey);
        $url="https://b.fanqieui.com/gateways/".$type.".php?account=".$fqaccount."&mchid=".$fqmchid."&type=".$fqtype."&trade=".$fqtrade."&cny=".$fqcny."&signs=".$signs;
		if($type == "alipay"){
		    $result = json_encode(['code' => 0, 'url' => $url, 'pid' => $pl->tradeno]);
        }else{
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_HEADER, 0);
			curl_setopt($ch, CURLOPT_URL, $url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22");
			curl_setopt($ch, CURLOPT_FOLLOWLOCATION ,1);
			curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 0);
			$output = curl_exec($ch);
			$arr=array();
			$reg= "/<img (.*?) src=\"(.+?)\".*?>/";
			preg_match_all($reg, $output, $arr);
			$imglink=$arr[0][0];
			curl_close($ch);
			if(strpos($imglink,"/qrcode")){
			     $imglink=str_replace("/qrcode","https://b.fanqieui.com/qrcode",$imglink); 
			 }
			 if(strpos($imglink, "width:300px;hight:300px")){
			     $imglink=str_replace("width:300px;hight:300px","display: inline-block;",$imglink); 
			 }
			 if(strpos($imglink, 'alt="Photo"')){
			     $imglink=str_replace('alt="Photo"','width="210" height="210"',$imglink); 
			 }
			 $result = json_encode(['code' => 0, 'url' => $imglink, 'pid' => $pl->tradeno]);
        }
        return $result;
    }
    
    public function notify($request, $response, $args)
    {
		$type = $args['type'];
		$settings = Config::get("tomatopay")[$type];
        $order_data = $_REQUEST;
        $transid   = $order_data['trade_no'];       //转账交易号
		$invoiceid = $order_data['out_trade_no'];     //订单号
		$amount    = $order_data['total_fee'];          //获取递过来的总价格
		$status    = $order_data['trade_status'];         //获取传递过来的交易状态
        $signs    = $order_data['sign']; 
        
        $security  = array();
        $security['out_trade_no'] = $invoiceid;
        $security['total_fee'] = $amount;
        $security['trade_no'] = $transid;
        $security['trade_status'] = $status;
          if ($type == "alipay") {
              $type = "支付宝";
          }elseif($type == "wxpay") {
              $type = "微信支付";
          }
		foreach ($security as $k=>$v)
		{
		    $o.= "$k=".urlencode($v)."&";
		}
		$sign = md5(substr($o,0,-1).$settings['token']);

         if ($sign == $signs) {
			 //验重
			 $p = Paylist::where('tradeno', '=', $order_data['out_trade_no'])->first();
			 $money = $p->total;
			 if ($p->status != 1) {
                  $this->postPayment($order_data['out_trade_no'], $type);
                    echo 'SUCCESS';
                } else {
                    echo 'ERROR';
                }
        	echo 'success';
        	if($ispost==0) header("Location: /user/code");
        }else{
           echo '验证失败';
        }
    }
    public function getPurchaseHTML()
    {
        return '';
    }
    public function getReturnHTML($request, $response, $args)
    {

    }
    public function getStatus($request, $response, $args)
    {
        $p = Paylist::where('tradeno', $_POST['pid'])->first();
        $return['ret'] = 1;
        $return['result'] = $p->status;
        return json_encode($return);
    }
}
