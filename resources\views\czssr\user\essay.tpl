{include file='user/main.tpl'}
<link rel="stylesheet" href="/theme/czssr/main/css/quill.core.css" type="text/css">
<style>
   .doudong:hover { transform: translateY(-2px);}
</style>
     <!-- Header -->
	<div class="header pb-6 d-flex align-items-center" style="min-height: 500px; background-image: url(/theme/czssr/main/picture/profile-cover.jpg); background-size: cover; background-position: center top;">
      <!-- Mask -->
      <span class="mask bg-gradient-default opacity-8"></span>
      <!-- Header container -->
      <div class="container-fluid d-flex align-items-center">
        <div class="row">
          <div class="col-lg-10 col-md-10">
            <h1 class="display-2 text-white">Hello {$user->user_name}</h1>
            <p class="text-white mt-0 mb-5">This is our essays page. You can find something interesting in this page.</p>
            <div class="col-lg-7 col-8">
              <h6 class="h2 text-white d-inline-block mb-0">Essay</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">文章中心</li>
                </ol>
              </nav>
            </div>
			
          </div>
        </div>
      </div>
    </div>
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <div class="row">
        {if $empty != 0}
	  {foreach $essays as $essay}
	    <div class="col-xl-4">
          <div class="card doudong">
            <!-- Card header -->
            <div class="card-header">
              <!-- Title -->
              <h4 class="text-ellipsis mb-0">{$essay->title}</h4>
            </div>
            <!-- Card body -->
            <div class="card-body">
              <div id="editor"></div>
			      <div v-html="content">{$essay->content}</div>
              <span style="font-size: 12px;">{$essay->date}</span>
			  </div>
            </div>
			
        </div>
      {/foreach}
		{else}
		<div class="col">
		    <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">掌柜可懒了.</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>掌柜可懒了,暂时莫得文章.</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
		</div>	
		{/if}
	 
      </div><!--row-->

	  
	  
	  
	{include file='user/footer.tpl'}
<script src="/theme/czssr/main/js/quill.min.js"></script>	  

	