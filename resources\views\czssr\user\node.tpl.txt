 {include file='user/main.tpl'}
 <style>
   .doudong:hover { transform: translateY(-2px);}
</style>
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Nodes</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">节点列表</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/code" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 充值</a>
              <a href="/user/shop" class="btn btn-sm btn-neutral">商店</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header --> 

	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
    <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">节点使用说明</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>用户等级：{$levels}</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>对应的节点使用<code>权限</code>根据你的用户等级和用户群组来决定的.</p>
						<p>
							<i class="ni ni-card ni-lg icon-ver"></i>当前套餐：<font color="#399AF2" size="3">{$level->name}</font> 套餐&nbsp;&nbsp;&nbsp;<a class="btn btn-success btn-sm" href="/user/shop" >点此升级</a>
						</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
										
        {if $config['show_free_nodes'] == "true"}
		<div class="card">
          <!-- Card header -->
          <div class="card-header">
            <h3 class="mb-0">免费节点列表 - VIP0免费线路</h3>
          </div>
          <!-- Card body -->
			<div class="card-body bg-gradient-Secondary">
				<div class="row">
				{foreach $nodes as $node}
				{if $node['class'] == 0}
				  <div class="col-12 col-sm-12 col-lg-6">
					<div class="doudong">
						<div class="card card-stats">
							<div class="card-body">
							<div class="row align-items-center">
								<div class="col-auto">
								<!-- Avatar -->
										<div class="avatar avatar-xl rounded-circle">
											<img src="/images/prefix/{$node['flag']}" >
										</div>
								</div>
								{if $node['mu_only'] != 1 && ($node['sort'] != 11 || $node['sort']!=12)}
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" onclick="urlChange('{$node['id']}',0,{if $relay_rule != null}{$relay_rule->id}{else}0{/if})">
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif $node['sort'] == 11}
							    <div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" data-toggle="modal" data-target="#node-modal-{$node['id']}">
						
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']} - V2ray
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif ($node['sort'] == 0 || $node['sort'] == 10) && $node['mu_only'] != -1}
							    {foreach $nodes_muport as $single_muport}
								    {$relay_rule = null}
								{if $node['sort'] == 10 && $single_muport['user']['is_multi_user'] != 2}
									{$relay_rule = $tools->pick_out_relay_rule($node['id'], $single_muport['server']->server, $relay_rules)}
								{/if}
								
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" onclick="urlChange('{$node['id']}',{$single_muport['server']->server},{if $relay_rule != null}{$relay_rule->id}{else}0{/if})">
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}{if $relay_rule != null} - {$relay_rule->dist_node()->name}{/if} - 单端口{$single_muport['server']->server} 
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
								{/foreach}
							{/if}
							</div>
							</div>
						</div>
					</div>
				  </div>
				{/if}
				{/foreach}	
				</div>
			</div>
	    </div><!--card-->
	    {/if}
		
		
		<div class="card">
          <!-- Card header -->
          <div class="card-header">
		  {foreach $levelList as $level}
			{if $level->level == 1}
            <h3 class="mb-0">{$level->name}节点列表 - VIP1 | 线路</h3>
			{/if}
		  {/foreach}
          </div>
          <!-- Card body -->
			<div class="card-body bg-gradient-Secondary">
				<div class="row">
				{foreach $nodes as $node}
				{if $node['class'] == 1}
				  <div class="col-12 col-sm-12 col-lg-6">
					<div class="doudong">
						<div class="card card-stats">
							<div class="card-body">
							<div class="row align-items-center">
								<div class="col-auto">
								<!-- Avatar -->
										<div class="avatar avatar-xl rounded-circle">
											<img src="/images/prefix/{$node['flag']}" >
										</div>
								</div>
								{if $node['mu_only'] != 1 && ($node['sort'] != 11 || $node['sort']!=12)}
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 1}onclick="urlChange('{$node['id']}',0,{if $relay_rule != null}{$relay_rule->id}{else}0{/if})" {else}data-toggle="modal" data-target="#node-modal-ban" {/if}>
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif $node['sort'] == 11}
							    <div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 1} data-toggle="modal" data-target="#node-modal-{$node['id']}" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
						
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']} - V2ray
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif ($node['sort'] == 0 || $node['sort'] == 10) && $node['mu_only'] != -1}
							    {foreach $nodes_muport as $single_muport}
								    {$relay_rule = null}
								{if $node['sort'] == 10 && $single_muport['user']['is_multi_user'] != 2}
									{$relay_rule = $tools->pick_out_relay_rule($node['id'], $single_muport['server']->server, $relay_rules)}
								{/if}
								
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 1}onclick="urlChange('{$node['id']}',{$single_muport['server']->server},{if $relay_rule != null}{$relay_rule->id}{else}0{/if})" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}{if $relay_rule != null} - {$relay_rule->dist_node()->name}{/if} - 单端口{$single_muport['server']->server} 
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
								{/foreach}
							{/if}
							</div>
							</div>
						</div>
					</div>
				  </div>
				{/if}
				{/foreach}	
				</div>
			</div>
	    </div><!--card-->
		
		<div class="card">
          <!-- Card header -->
          <div class="card-header">
		  {foreach $levelList as $level}
			{if $level->level == 2}
            <h3 class="mb-0">{$level->name}节点列表 - VIP2 | 线路</h3>
			{/if}
		  {/foreach}
          </div>
          <!-- Card body -->
			<div class="card-body bg-gradient-Secondary">
				<div class="row">
				{foreach $nodes as $node}
				{if $node['class'] == 2}
				  <div class="col-12 col-sm-12 col-lg-6">
					<div class="doudong">
						<div class="card card-stats">
							<div class="card-body">
							<div class="row align-items-center">
								<div class="col-auto">
								<!-- Avatar -->
										<div class="avatar avatar-xl rounded-circle">
											<img src="/images/prefix/{$node['flag']}" >
										</div>
								</div>
							{if $node['mu_only'] != 1 && ($node['sort'] != 11 || $node['sort']!=12)}
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 2}onclick="urlChange('{$node['id']}',0,{if $relay_rule != null}{$relay_rule->id}{else}0{/if})" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif $node['sort'] == 11}
							    <div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 2} data-toggle="modal" data-target="#node-modal-{$node['id']}" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
						
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']} - V2ray
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif ($node['sort'] == 0 || $node['sort'] == 10) && $node['mu_only'] != -1}
							    {foreach $nodes_muport as $single_muport}
								    {$relay_rule = null}
								{if $node['sort'] == 10 && $single_muport['user']['is_multi_user'] != 2}
									{$relay_rule = $tools->pick_out_relay_rule($node['id'], $single_muport['server']->server, $relay_rules)}
								{/if}
								
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 2}onclick="urlChange('{$node['id']}',{$single_muport['server']->server},{if $relay_rule != null}{$relay_rule->id}{else}0{/if})" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}{if $relay_rule != null} - {$relay_rule->dist_node()->name}{/if} - 单端口{$single_muport['server']->server} 
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
								{/foreach}
							{/if}
							</div>
							</div>
						</div>
					</div>
				  </div>
				{/if}
				{/foreach}	
				</div>
			</div>
	    </div><!--card-->
	
	    <div class="card">
          <!-- Card header -->
          <div class="card-header">
		  {foreach $levelList as $level}
			{if $level->level == 3}
            <h3 class="mb-0">{$level->name}节点列表 - VIP3 | 线路</h3>
			{/if}
		  {/foreach}
          </div>
          <!-- Card body -->
			<div class="card-body bg-gradient-Secondary">
				<div class="row">
				{foreach $nodes as $node}
				{if $node['class'] == 3}
				  <div class="col-12 col-sm-12 col-lg-6">
					<div class="doudong">
						<div class="card card-stats">
							<div class="card-body">
							<div class="row align-items-center">
								<div class="col-auto">
								<!-- Avatar -->
										<div class="avatar avatar-xl rounded-circle">
											<img src="/images/prefix/{$node['flag']}" >
										</div>
								</div>
							{if $node['mu_only'] != 1 && ($node['sort'] != 11 || $node['sort']!=12)}
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 3}onclick="urlChange('{$node['id']}',0,{if $relay_rule != null}{$relay_rule->id}{else}0{/if})" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif $node['sort'] == 11}
							    <div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 3} data-toggle="modal" data-target="#node-modal-{$node['id']}" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
						
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']} - V2ray
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif ($node['sort'] == 0 || $node['sort'] == 10) && $node['mu_only'] != -1}
							    {foreach $nodes_muport as $single_muport}
								    {$relay_rule = null}
								{if $node['sort'] == 10 && $single_muport['user']['is_multi_user'] != 2}
									{$relay_rule = $tools->pick_out_relay_rule($node['id'], $single_muport['server']->server, $relay_rules)}
								{/if}
								
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 3}onclick="urlChange('{$node['id']}',{$single_muport['server']->server},{if $relay_rule != null}{$relay_rule->id}{else}0{/if})" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}{if $relay_rule != null} - {$relay_rule->dist_node()->name}{/if} - 单端口{$single_muport['server']->server} 
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
								{/foreach}
							{/if}
							</div>
							</div>
						</div>
					</div>
				  </div>
				{/if}
				{/foreach}	
				</div>
			</div>
	    </div><!--card-->
		
		<div class="card">
          <!-- Card header -->
          <div class="card-header">
		  {foreach $levelList as $level}
			{if $level->level == 4}
            <h3 class="mb-0">{$level->name}节点列表 - VIP4 | 线路</h3>
			{/if}
		  {/foreach}
          </div>
          <!-- Card body -->
			<div class="card-body bg-gradient-Secondary">
				<div class="row">
				{foreach $nodes as $node}
				{if $node['class'] == 4}
				  <div class="col-12 col-sm-12 col-lg-6">
					<div class="doudong">
						<div class="card card-stats">
							<div class="card-body">
							<div class="row align-items-center">
								<div class="col-auto">
								<!-- Avatar -->
										<div class="avatar avatar-xl rounded-circle">
											<img src="/images/prefix/{$node['flag']}" >
										</div>
								</div>
							{if $node['mu_only'] != 1 && ($node['sort'] != 11 || $node['sort']!=12)}
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 4}onclick="urlChange('{$node['id']}',0,{if $relay_rule != null}{$relay_rule->id}{else}0{/if})" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif $node['sort'] == 11}
							    <div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 4} data-toggle="modal" data-target="#node-modal-{$node['id']}" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
						
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']} - V2ray
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif ($node['sort'] == 0 || $node['sort'] == 10) && $node['mu_only'] != -1}
							    {foreach $nodes_muport as $single_muport}
								    {$relay_rule = null}
								{if $node['sort'] == 10 && $single_muport['user']['is_multi_user'] != 2}
									{$relay_rule = $tools->pick_out_relay_rule($node['id'], $single_muport['server']->server, $relay_rules)}
								{/if}
								
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 4}onclick="urlChange('{$node['id']}',{$single_muport['server']->server},{if $relay_rule != null}{$relay_rule->id}{else}0{/if})" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}{if $relay_rule != null} - {$relay_rule->dist_node()->name}{/if} - 单端口{$single_muport['server']->server} 
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
								{/foreach}
							{/if}
							</div>
							</div>
						</div>
					</div>
				  </div>
				{/if}
				{/foreach}	
				</div>
			</div>
	    </div><!--card-->
	    
	    	<div class="card">
          <!-- Card header -->
          <div class="card-header">
		  {foreach $levelList as $level}
			{if $level->level == 6}
            <h3 class="mb-0">{$level->name}节点列表 - VIP5 | 线路</h3>
			{/if}
		  {/foreach}
          </div>
          <!-- Card body -->
			<div class="card-body bg-gradient-Secondary">
				<div class="row">
				{foreach $nodes as $node}
				{if $node['class'] == 6}
				  <div class="col-12 col-sm-12 col-lg-6">
					<div class="doudong">
						<div class="card card-stats">
							<div class="card-body">
							<div class="row align-items-center">
								<div class="col-auto">
								<!-- Avatar -->
										<div class="avatar avatar-xl rounded-circle">
											<img src="/images/prefix/{$node['flag']}" >
										</div>
								</div>
							{if $node['mu_only'] != 1 && ($node['sort'] != 11 || $node['sort']!=12)}
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 6}onclick="urlChange('{$node['id']}',0,{if $relay_rule != null}{$relay_rule->id}{else}0{/if})" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif $node['sort'] == 11}
							    <div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 6} data-toggle="modal" data-target="#node-modal-{$node['id']}" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
						
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']} - V2ray
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif ($node['sort'] == 0 || $node['sort'] == 10) && $node['mu_only'] != -1}
							    {foreach $nodes_muport as $single_muport}
								    {$relay_rule = null}
								{if $node['sort'] == 10 && $single_muport['user']['is_multi_user'] != 2}
									{$relay_rule = $tools->pick_out_relay_rule($node['id'], $single_muport['server']->server, $relay_rules)}
								{/if}
								
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 6}onclick="urlChange('{$node['id']}',{$single_muport['server']->server},{if $relay_rule != null}{$relay_rule->id}{else}0{/if})" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}{if $relay_rule != null} - {$relay_rule->dist_node()->name}{/if} - 单端口{$single_muport['server']->server} 
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
								{/foreach}
							{/if}
							</div>
							</div>
						</div>
					</div>
				  </div>
				{/if}
				{/foreach}	
				</div>
			</div>
	    </div><!--card-->
	    
	    <div class="card">
          <!-- Card header -->
          <div class="card-header">
		  {foreach $levelList as $level}
			{if $level->level == 7}
            <h3 class="mb-0">{$level->name}节点列表 - VIP7 | 线路</h3>
			{/if}
		  {/foreach}
          </div>
          <!-- Card body -->
			<div class="card-body bg-gradient-Secondary">
				<div class="row">
				{foreach $nodes as $node}
				{if $node['class'] == 7}
				  <div class="col-12 col-sm-12 col-lg-6">
					<div class="doudong">
						<div class="card card-stats">
							<div class="card-body">
							<div class="row align-items-center">
								<div class="col-auto">
								<!-- Avatar -->
										<div class="avatar avatar-xl rounded-circle">
											<img src="/images/prefix/{$node['flag']}" >
										</div>
								</div>
							{if $node['mu_only'] != 1 && ($node['sort'] != 11 || $node['sort']!=12)}
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 7}onclick="urlChange('{$node['id']}',0,{if $relay_rule != null}{$relay_rule->id}{else}0{/if})" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif $node['sort'] == 11}
							    <div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 7} data-toggle="modal" data-target="#node-modal-{$node['id']}" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
						
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']} - V2ray
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
							{elseif ($node['sort'] == 0 || $node['sort'] == 10) && $node['mu_only'] != -1}
							    {foreach $nodes_muport as $single_muport}
								    {$relay_rule = null}
								{if $node['sort'] == 10 && $single_muport['user']['is_multi_user'] != 2}
									{$relay_rule = $tools->pick_out_relay_rule($node['id'], $single_muport['server']->server, $relay_rules)}
								{/if}
								
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" {if $user->class >= 7}onclick="urlChange('{$node['id']}',{$single_muport['server']->server},{if $relay_rule != null}{$relay_rule->id}{else}0{/if})" {else} data-toggle="modal" data-target="#node-modal-ban" {/if}>
			
									{if $node['online']=="1"}
										<span class="bg-gradient-green text-white">在线</span> |
									{elseif $node['online']=='0'}
										<span class="bg-gradient-danger text-white">升级</span> |
									{else}
										<span class="bg-gradient-danger text-white">维护</span> |
									{/if}
									{$node['name']}{if $relay_rule != null} - {$relay_rule->dist_node()->name}{/if} - 单端口{$single_muport['server']->server} 
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : {$node['info']}</p>
								<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong>{$node['traffic_rate']} 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong>{if $user->isAdmin()}{if $node['online_user'] == -1}N/A{else}{$node['online_user']}{/if}{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：{if $node['latest_load'] == -1}N/A{else}{$node['latest_load']}%{/if}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：{$node['bandwidth']}</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									{if $node['traffic_limit']>0}
									<strong>{$node['traffic_used']}/{$node['traffic_limit']}</strong> 
									{else}
									<strong>{$node['traffic_used']}GB</strong>
									{/if}
								</div>
								{/foreach}
							{/if}
							</div>
							</div>
						</div>
					</div>
				  </div>
				{/if}
				{/foreach}	
				</div>
			</div>
	    </div><!--card-->
		
	
		</div><!--col-->
	</div><!--row-->

	
{function displayV2rayNode node=null}
	{assign var=server_explode value=";"|explode:$node['server']}
	<p class="description mb-2">地址：<span class="card-tag tag-blue">{$server_explode[0]}</span></p>

	<p class="description mb-2">端口：<span class="card-tag tag-volcano">{$server_explode[1]}</span></p>

	<p class="description mb-2">协议参数：<span class="card-tag tag-green">{$server_explode[0]}</span></p>

	<p class="description mb-2">用户 UUID：<span class="card-tag tag-geekblue">{$user->getUuid()}</span></p>

	<p class="description mb-2">流量比例：<span class="card-tag tag-red">{$node['traffic_rate']}</span></p>

	<p class="description mb-2">AlterId：<span class="card-tag tag-purple">{$server_explode[2]}</span></p>

	<p class="description mb-2">VMess链接：
		<a href="javascript:void(0);" class="copy-text" data-clipboard-text="{URL::getV2Url($user, $node['raw_node'])}">点击复制</a>
	</p>
{/function}

	<!-- VMess  Modal -->
	{foreach $nodes as $node}
	{if $user->class >= $node->class}
		{if $node['sort'] == 11}
		<div class="modal fade" id="node-modal-{$node['id']}" tabindex="-1" role="dialog" aria-labelledby="VMessModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="VMessModalLabel">
				{if $config['enable_flag']=='true'}
				<img src="/images/prefix/{$node['flag']}" height="22" width="40" />
				{/if}
				 {$node['name']}</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
			    {displayV2rayNode node=$node}
		      </div>
		      <div class="modal-footer">
		        <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
		      </div>
		    </div>
		  </div>
		</div>
		{/if}
	{/if}
	{/foreach}
	
	    <div class="modal fade" id="node-modal-ban" tabindex="-1" role="dialog" aria-labelledby="banModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="banModalLabel" class="text-danger">您没有足够的权限</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
			    <p class="description mb-2">您当前等级不足以使用该节点, 如需升级请<a href="/user/shop"> 点击这里 </a>升级套餐</p>
		      </div>
		      <div class="modal-footer">
		        <button type="button" class="btn btn-Secondary" data-dismiss="modal">Close</button>
		      </div>
		    </div>
		  </div>
		</div>
		
	
		<div class="modal fade" tabindex="-1" role="dialog" id="nodeinfo" aria-labelledby="nodeinfoModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
			<div class="modal-content">
			<div class="modal-header">
				<h4 id="nodeinfoModalLabel">节点详细信息</h4>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
			</div>
			
					<iframe id="infoifram" style="display: block;border: none;width: 100%;height: 600px;margin: 0;padding: 0;over"></iframe>

			</div>
		  </div>
		</div>

	  {include file='user/footer.tpl'}
	  

<script>
	function urlChange(id, is_mu, rule_id) {
		var site = './node/' + id + '?ismu=' + is_mu + '&relay_rule=' + rule_id;
		if (id == 'guide') {
			var doc = document.getElementById('infoifram').contentWindow.document;
			doc.open();
			doc.write('<img src="../images/node.gif" style="width: 100%;height: 100%; border: none;"/>');
			doc.close();
		}
		else {
			document.getElementById('infoifram').src = site;
		}
		$("#nodeinfo").modal();
	}
</script>
