<?php
/* Smarty version 3.1.33, created on 2022-02-07 02:50:54
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/coupon.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_6200188ec29c52_56027964',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'ef0355861a01d1451a8c5cc06471bfe5e0f979a9' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/coupon.tpl',
      1 => 1577789926,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/main.tpl' => 1,
    'file:table/checkbox.tpl' => 1,
    'file:table/table.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:admin/footer.tpl' => 1,
    'file:table/js_1.tpl' => 1,
    'file:table/js_2.tpl' => 1,
    'file:table/js_delete.tpl' => 1,
  ),
),false)) {
function content_6200188ec29c52_56027964 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Coupons</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/coupon">优惠码</a></li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/shop" class="btn btn-sm btn-neutral">商品</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">优惠码添加</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">优惠码(生产随机优惠码可不填):</label>
							<input id="prefix" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">优惠码额度(百分比,九折就填10):</label>
							<input id="credit" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">优惠码有效期(h):</label>
							<input id="expire" class="form-control form-control-sm" type="number" value="24">
						</div>
						<div class="form-group">
							<label class="form-control-label">优惠码可用商品ID:</label>
							<input id="shop" class="form-control form-control-sm" type="text">
							<p class="description badge-dot"><i class="bg-warning"></i>不填即为所有商品可用, 多个的话用英文半角逗号,分割</p>
						</div>
						<div class="form-group">
							<label class="form-control-label">优惠码每个用户可用次数:</label>
							<input id="count" class="form-control form-control-sm" type="number" value="1">
						</div>
						<div class="form-group">
							<label class="form-control-label">选择生成方式:</label>
							<select id="generate-type" class="form-control form-control-sm" name="generate-type">
                                <option value="2">随机字符</option>
                                <option value="1">指定字符</option>
                                <option value="3">指定字符+随机字符</option>
							</select>
						</div>
					</div>
					<div class="modal-footer">
						<button id="coupon" type="button" class="btn btn-primary">确认提交</button>
					</div>
				</div>
			</div>
			<div class="card">
              <!-- Card header -->
				<div class="card-header">
					<h3 class="mb-0">优惠码记录</h3>
				</div>
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>显示表项: <?php $_smarty_tpl->_subTemplateRender('file:table/checkbox.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?></p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
					<div class="card-body">
					<!-- Light table -->
						<div class="table-responsive py-4">
						<?php $_smarty_tpl->_subTemplateRender('file:table/table.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
						</div>
					</div>
				</div>		
			</div>
			
        </div>
      </div><!--row-->
       <!--删除modal-->
		<div class="modal fade" id="delete_modal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="deleteModalLabel" class="text-danger">确认删除吗?</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>请问你确认要删除吗?</p>
				</div>	 
		      </div>
			    <div class="modal-footer">
                    <button id="delete_input" type="button" class="btn btn-primary">确认提交</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
		    </div>
		  </div>
		</div>
	 
		<?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php $_smarty_tpl->_subTemplateRender('file:admin/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
echo '<script'; ?>
 src="//cdn.jsdelivr.net/npm/datatables.net@1.10.19"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="/theme/czssr/main/js/dataTables.material.min.js"><?php echo '</script'; ?>
> 

<?php echo '<script'; ?>
>

    function delete_modal_show(id) {
        deleteid = id;
        $("#delete_modal").modal();
    }
    <?php $_smarty_tpl->_subTemplateRender('file:table/js_1.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


    window.addEventListener('load', () => {
        <?php $_smarty_tpl->_subTemplateRender('file:table/js_2.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
		
		function coupon() {
            let couponCode = $$getValue('prefix');

            $.ajax({
                type: "POST",
                url: "/admin/coupon",
                dataType: "json",
                data: {
                    prefix: $$getValue('prefix'),
                    credit: $$getValue('credit'),
                    shop: $$getValue('shop'),
                    onetime: $$getValue('count'),
                    expire: $$getValue('expire'),
                    generate_type: $$getValue('generate-type'),
                },
                success: data => {
				    if (data.ret) {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                        window.setTimeout("location.href='/admin/coupon'", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                    }
                },
                error: (jqXHR) => {
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
				}
            });
        }
		 $("#coupon").on("click", coupon);
		 
		function delete_id(){
			$.ajax({
				type:"DELETE",
				url:"/admin/coupon",
				dataType:"json",
				data:{
					id: deleteid
				},
				success: data => {
					if (data.ret) {
					    $("#delete_modal").modal("hide");
						$("#result").modal();
						$("#msg").html(data.msg);
						<?php $_smarty_tpl->_subTemplateRender('file:table/js_delete.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
					} else {
						$("#result").modal();
						$("#msg").html(data.msg);
					}
				},
				error: jqXHR => {
					$("#result").modal();
					$("#msg").html("发生错误了: " + jqXHR.status);
				}
			});
		}
		$("#delete_input").on("click", delete_id);
    })
<?php echo '</script'; ?>
>

<?php }
}
