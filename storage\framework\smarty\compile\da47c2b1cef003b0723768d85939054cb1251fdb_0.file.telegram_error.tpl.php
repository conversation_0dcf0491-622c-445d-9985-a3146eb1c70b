<?php
/* Smarty version 3.1.33, created on 2022-03-02 08:13:01
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/telegram_error.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_621eb68d7bc8a9_73885719',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'da47c2b1cef003b0723768d85939054cb1251fdb' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/telegram_error.tpl',
      1 => 1567094446,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_621eb68d7bc8a9_73885719 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE HTML>
<html>
<head>
    <title>产生了一个错误 - <?php echo $_smarty_tpl->tpl_vars['config']->value["appName"];?>
 </title>
    <meta name="keywords" content=""/>
    <meta name="description" content=""/>
    <meta charset="utf-8"/>
    <link rel="shortcut icon" href="/favicon.ico"/>
    <link rel="bookmark" href="/favicon.ico" type="image/x-icon"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
    <link rel="shortcut icon" type="image/ico" href="images/ssr.ico">
	<style>
        /*! Spectre.css v0.5.0 | MIT License | github.com/picturepan2/spectre */

        html {
            font-family: sans-serif;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%
        }

        body {
            margin: 0
        }

        footer {
            display: block
        }

        a {
            background-color: transparent;
            -webkit-text-decoration-skip: objects
        }

        a:active,
        a:hover {
            outline-width: 0
        }

        ::-webkit-file-upload-button {
            -webkit-appearance: button;
            font: inherit
        }

        *,
        ::after,
        ::before {
            box-sizing: inherit
        }

        html {
            box-sizing: border-box;
            font-size: 20px;
            line-height: 1.5;
            -webkit-tap-highlight-color: transparent
        }

        body {
            background: #fff;
            color: #50596c;
            font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
            font-size: .8rem;
            overflow-x: hidden;
            text-rendering: optimizeLegibility
        }

        a {
            color: #5755d9;
            outline: 0;
            text-decoration: none
        }

        a:focus {
            box-shadow: 0 0 0 .1rem rgba(87, 85, 217, .2)
        }

        a:active,
        a:focus,
        a:hover {
            color: #4240d4;
            text-decoration: underline
        }

        .h1,
        .h4 {
            font-weight: 500
        }

        .h1 {
            font-size: 2rem
        }

        .h4 {
            font-size: 1.2rem
        }

        p {
            margin: 0 0 1rem
        }

        a {
            -webkit-text-decoration-skip: ink edges;
            text-decoration-skip: ink edges
        }

        .form-input:not(:placeholder-shown):invalid {
            border-color: #e85600
        }

        .form-input:not(:placeholder-shown):invalid:focus {
            box-shadow: 0 0 0 .1rem rgba(232, 86, 0, .2)
        }

        .container {
            margin-left: auto;
            margin-right: auto;
            padding-left: .4rem;
            padding-right: .4rem;
            width: 100%
        }

        .container.grid-lg {
            max-width: 976px
        }

        .empty {
            background: #f8f9fa;
            border-radius: .1rem;
            color: #667189;
            padding: 3.2rem 1.6rem;
            text-align: center
        }

        .empty .empty-subtitle,
        .empty .empty-title {
            margin: .4rem auto
        }

        .empty .empty-action {
            margin-top: .8rem
        }

        .text-error {
            color: #e85600
        }

        .divider {
            display: block;
            position: relative
        }

        .divider {
            border-top: .05rem solid #e7e9ed;
            height: .05rem;
            margin: .4rem 0
        }

        .container::after {
            clear: both;
            content: "";
            display: table
        }

        .centered {
            display: block;
            float: none;
            margin-left: auto;
            margin-right: auto
        }

        .valign {
            display: -webkit-box!important;
            display: -webkit-flex!important;
            display: -ms-flexbox!important;
            display: flex!important;
            -webkit-box-align: center!important;
            -webkit-align-items: center!important;
            -ms-flex-align: center!important;
            align-items: center!important;
        }

        .section-footer {
            color: #acb3c2;
            padding: 2rem .5rem 0 .5rem;
            position: relative;
            z-index: 200;
        }

        .section-footer a {
            color: #667189;
        }
    </style>
    <?php if (!empty($_smarty_tpl->tpl_vars['redirect']->value)) {?>
        <meta http-equiv="refresh" content="3; url=<?php echo $_smarty_tpl->tpl_vars['redirect']->value;?>
"/>
    <?php }?>
    <noscript>
        <link rel="stylesheet" href="/assets/css/noscript.css"/>
    </noscript>
</head>
<body>
<div class="empty valign" style="height:100vh">
<div class="centered">
<p class="empty-title h1"><?php echo $_smarty_tpl->tpl_vars['title']->value;?>
</p>
<p class="empty-title h4"><?php echo $_smarty_tpl->tpl_vars['message']->value;?>
</p>
<div class="divider"></div>
<div class="empty-action">
</div>
<footer class="section section-footer">
<div id="copyright" class="grid-footer container grid-lg">©
<span year=""><?php echo date("Y");?>
</span>
<a href="<?php echo $_smarty_tpl->tpl_vars['config']->value["baseUrl"];?>
" target="_blank"><?php echo $_smarty_tpl->tpl_vars['config']->value["appName"];?>
</a>
</div>
</footer>
</div>
</div>
</body>
</html>
<?php }
}
