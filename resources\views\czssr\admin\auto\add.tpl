{include file='admin/main.tpl'}
    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Auto Crontab</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/auto">下发命令</a></li>
                  <li class="breadcrumb-item active" aria-current="page">新建命令</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/user" class="btn btn-sm btn-neutral">用户</a>
            </div>
          </div>
        </div>
      </div>
    </div>
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">新建自动化命令</h3>
              </div>
              <!-- Card body -->
				<div class="bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label for="content">命令</label>
							<textarea class="form-control" id="content" rows="3"></textarea>
						</div>
						<div class="form-group">
							<label for="sign">GPG签名</label>
							<textarea class="form-control" id="sign" rows="3"></textarea>
						</div>
					</div>
					<div class="modal-footer">
						<button id="submit" type="button" class="btn btn-primary">确认提交</button>
					</div>
				</div>
				
			</div>
        </div>
      </div><!--row-->

	{include file='dialog.tpl'}
	{include file='admin/footer.tpl'}

<script>
    window.addEventListener('load', () => {
        function submit() {
            $.ajax({
                type: "POST",
                url: "/admin/auto",
                dataType: "json",
                data: {
                    content: $$getValue('content'),
					sign: $$getValue('sign')
                },
                success: data => {
                    if (data.ret) {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $('#msg').html("发生错误："+jqXHR.status);
                }
            });
        }

       $("#submit").click(function () {
            submit();
        });
    })
</script>
