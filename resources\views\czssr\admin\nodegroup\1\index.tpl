{include file='admin/main.tpl'}


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Groups</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">群组设置</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/node" class="btn btn-sm btn-neutral">节点</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">群组设置(节点/用户/商品)</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
                      <a class="btn btn-primary btn-sm mb-3" href="/admin/nodegroup/create">新建群组</a>
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>显示表项: {include file='table/checkbox.tpl'}</p>
							
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
			
			<div class="card">
              <!-- Card body -->
			  <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive py-4">
				  {include file='table/table.tpl'}
					
				</div>
              </div>
            </div><!--card-->
		
        </div>
      </div><!--row-->
	  <!--删除modal-->
		<div class="modal fade" id="delete_modal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="deleteModalLabel" class="text-danger">确认删除吗?</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>请问你确认要删除吗?</p>
				</div>	 
		      </div>
			    <div class="modal-footer">
                    <button id="delete_input" type="button" class="btn btn-primary">确认提交</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
		    </div>
		  </div>
		</div>
		{include file='dialog.tpl'}
	  {include file='admin/footer.tpl'}
<script src="//cdn.jsdelivr.net/npm/datatables.net@1.10.19"></script>
<script src="/theme/czssr/main/js/dataTables.material.min.js"></script>

<script>
function delete_modal_show(id) {
    deleteid = id;
	$("#delete_modal").modal();
}
{include file='table/js_1.tpl'}

window.addEventListener('load', () => {


	function delete_id(){
		$.ajax({
			type:"DELETE",
			url:"/admin/nodegroup",
			dataType:"json",
			data:{
				id: deleteid
			},
			success: data => {
				if (data.ret) {
                    $("#delete_modal").modal("hide");
					$("#result").modal();
                    $('#msg').html(data.msg);
					{include file='table/js_delete.tpl'}
				}else{
					$("#result").modal();
                    $('#msg').html(data.msg);
				}
			},
			error: jqXHR => {
				$("#result").modal();
                $("#msg").html("发生错误了: " + jqXHR.status);
			}
		});
	}

		$("#delete_input").on("click", delete_id);
		{include file='table/js_2.tpl'}
})

</script>
