<?php
/* Smarty version 3.1.33, created on 2022-06-03 15:46:33
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/code/add.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_6299bc59022eb0_24871546',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '5a3510693e6bb56ca3f13d59e8a3c47e1bf68d04' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/code/add.tpl',
      1 => 1572184684,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/main.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:admin/footer.tpl' => 1,
  ),
),false)) {
function content_6299bc59022eb0_24871546 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Create Code</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
                  <li class="breadcrumb-item"><a href="/admin/code">充值码<?php if ($_smarty_tpl->tpl_vars['config']->value['enable_donate'] == 'true') {?>与捐赠<?php }?>管理</a></li>
				  <li class="breadcrumb-item active" aria-current="page">添加充值码</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/user" class="btn btn-sm btn-neutral">用户</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">添加充值码</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">数目:</label>
							<input id="amount" class="form-control form-control-sm" type="text" value="1">
						</div>
						<div class="form-group">
							<label class="form-control-label">金额(仅数字):</label>
							<input id="number" class="form-control form-control-sm" type="number">
						</div>
					</div>
				<div class="modal-footer">
					<button id="submit" type="button" class="btn btn-primary">确认提交</button>
				</div>
				</div>
			</div>
		
        </div>
      </div><!--row-->
		 <?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php $_smarty_tpl->_subTemplateRender('file:admin/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


<?php echo '<script'; ?>
>
    window.addEventListener('load', () => {
        function submit() {
            $.ajax({
                type: "POST",
                url: "/admin/code",
                dataType: "json",
                data: {
                    amount: $$getValue('amount'),
                    number: $$getValue('number')
                },
                success: data => {
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            });
        }

        $("#submit").on("click", submit);

    })
<?php echo '</script'; ?>
>

<?php }
}
