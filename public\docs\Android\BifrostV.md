## 应用概述
## [点击在线观看android视频教程](https://cdn.jsdelivr.net/gh/lxiaoya1003/tuchuang@master/androidssr99.mp4)

<video width="300" height="150" controls="controls">
<source src="https://cdn.jsdelivr.net/gh/lxiaoya1003/tuchuang@master/androidssr99.mp4" type="video/mp4" />
</video>

BifrostV 是在 Android 平台上的客户端软件，支持 VMess 及 Shadowsocks 协议。

## 应用下载

以下是各平台该应用的下载地址。

- Google Play：[BifrostV](https://play.google.com/store/apps/details?id=com.github.dawndiy.bifrostv&hl=zh)
- ...

*请注意，官方仅有 Google Play 下载地址，您可能需要先进行科学下载才能使用该客户端。*

## 获取订阅

此处将显示您的订阅链接，请注意为登录状态：

[cinwell website](/sublink?type=v2ray ':include :type=markdown')

!> 这个 **订阅链接** 非常重要，你应当把它当做密码一样妥善保管。

## 配置 BifrostV

打开 BifrostV 点击左上角的加号图标选择 **导入**，随后点击 **从 URL 导入**。

![1](https://i.loli.net/2019/02/13/5c63064d21248.png ':size=600')

在弹出的输入框中粘贴上方 **[获取订阅](#获取订阅)** 中的订阅链接并点击 **好的**。

![2](https://i.loli.net/2019/02/13/5c6306e90f89f.png ':size=200')

## 开始使用

点击选择您中意的节点，点击右下角的按钮即可连接。

如操作系统提示添加 VPN 配置，请点击 运行 并验证您的 密码、指纹等。
