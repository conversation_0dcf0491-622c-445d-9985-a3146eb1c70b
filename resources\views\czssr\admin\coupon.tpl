{include file='admin/main.tpl'}


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Coupons</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/coupon">优惠码</a></li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/shop" class="btn btn-sm btn-neutral">商品</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">优惠码添加</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">优惠码(生产随机优惠码可不填):</label>
							<input id="prefix" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">优惠码额度(百分比,九折就填10):</label>
							<input id="credit" class="form-control form-control-sm" type="text">
						</div>
						<div class="form-group">
							<label class="form-control-label">优惠码有效期(h):</label>
							<input id="expire" class="form-control form-control-sm" type="number" value="24">
						</div>
						<div class="form-group">
							<label class="form-control-label">优惠码可用商品ID:</label>
							<input id="shop" class="form-control form-control-sm" type="text">
							<p class="description badge-dot"><i class="bg-warning"></i>不填即为所有商品可用, 多个的话用英文半角逗号,分割</p>
						</div>
						<div class="form-group">
							<label class="form-control-label">优惠码每个用户可用次数:</label>
							<input id="count" class="form-control form-control-sm" type="number" value="1">
						</div>
						<div class="form-group">
							<label class="form-control-label">选择生成方式:</label>
							<select id="generate-type" class="form-control form-control-sm" name="generate-type">
                                <option value="2">随机字符</option>
                                <option value="1">指定字符</option>
                                <option value="3">指定字符+随机字符</option>
							</select>
						</div>
					</div>
					<div class="modal-footer">
						<button id="coupon" type="button" class="btn btn-primary">确认提交</button>
					</div>
				</div>
			</div>
			<div class="card">
              <!-- Card header -->
				<div class="card-header">
					<h3 class="mb-0">优惠码记录</h3>
				</div>
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>显示表项: {include file='table/checkbox.tpl'}</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
					<div class="card-body">
					<!-- Light table -->
						<div class="table-responsive py-4">
						{include file='table/table.tpl'}
						</div>
					</div>
				</div>		
			</div>
			
        </div>
      </div><!--row-->
       <!--删除modal-->
		<div class="modal fade" id="delete_modal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="deleteModalLabel" class="text-danger">确认删除吗?</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>请问你确认要删除吗?</p>
				</div>	 
		      </div>
			    <div class="modal-footer">
                    <button id="delete_input" type="button" class="btn btn-primary">确认提交</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
		    </div>
		  </div>
		</div>
	 
		{include file='dialog.tpl'}
	  {include file='admin/footer.tpl'}
<script src="//cdn.jsdelivr.net/npm/datatables.net@1.10.19"></script>
<script src="/theme/czssr/main/js/dataTables.material.min.js"></script> 

<script>

    function delete_modal_show(id) {
        deleteid = id;
        $("#delete_modal").modal();
    }
    {include file='table/js_1.tpl'}


    window.addEventListener('load', () => {
        {include file='table/js_2.tpl'}
		
		function coupon() {
            let couponCode = $$getValue('prefix');

            $.ajax({
                type: "POST",
                url: "/admin/coupon",
                dataType: "json",
                data: {
                    prefix: $$getValue('prefix'),
                    credit: $$getValue('credit'),
                    shop: $$getValue('shop'),
                    onetime: $$getValue('count'),
                    expire: $$getValue('expire'),
                    generate_type: $$getValue('generate-type'),
                },
                success: data => {
				    if (data.ret) {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                        window.setTimeout("location.href='/admin/coupon'", {$config['jump_delay']});
                    }
                },
                error: (jqXHR) => {
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
				}
            });
        }
		 $("#coupon").on("click", coupon);
		 
		function delete_id(){
			$.ajax({
				type:"DELETE",
				url:"/admin/coupon",
				dataType:"json",
				data:{
					id: deleteid
				},
				success: data => {
					if (data.ret) {
					    $("#delete_modal").modal("hide");
						$("#result").modal();
						$("#msg").html(data.msg);
						{include file='table/js_delete.tpl'}
					} else {
						$("#result").modal();
						$("#msg").html(data.msg);
					}
				},
				error: jqXHR => {
					$("#result").modal();
					$("#msg").html("发生错误了: " + jqXHR.status);
				}
			});
		}
		$("#delete_input").on("click", delete_id);
    })
</script>

