{include file='admin/main.tpl'}


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Nodes</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
                  <li class="breadcrumb-item"><a href="/admin/node">节点列表</a></li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/node" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/user" class="btn btn-sm btn-neutral">用户</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">节点列表</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
					<a class="btn btn-primary btn-sm mb-3" href="/admin/node/create">新建节点</a>
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>显示表项: {include file='table/checkbox.tpl'}</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
			
            <div class="card">
              <!-- Card body -->
			  <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive py-4">
				  {include file='table/table.tpl'}
					
				</div>
              </div>
            </div><!--card-->
		     <div class="card">
				<div class="card-header bg-transparent">
					<h4 class="mb-0">检测节点被强(少用)</h4>
				</div>
				<div class="card-body">
					<div class="form-group">
						<div class="custom-control custom-radio custom-control-inline">
							<input type="radio" id="check_node_all" name="check_node" class="custom-control-input" checked>
							<label class="custom-control-label" for="check_node_all">批量检测(仅返回被强的节点)</label>
						</div>
						<div class="custom-control custom-radio custom-control-inline">
							<input type="radio" id="check_node_one" name="check_node" class="custom-control-input">
							<label class="custom-control-label" for="check_node_one">单个检测</label>
						</div>
					</div> 
					<div class="form-group">
						<label class="form-control-label">填写IP(选择批量检测请留空):</label>
						<input id="check_ip" class="form-control form-control-sm" type="text" placeholder="请输入ip...">
					</div>
					<div class="form-group">
						<label class="form-control-label">填写端口(必填):</label>
						<input id="check_port" class="form-control form-control-sm" type="number" placeholder="请输入端口号...">
					</div>
					<div class="modal-footer">
						<button id="check_nodes" type="button" class="btn btn-primary">确认提交</button>
					</div>
				</div>
				<div class="card-body">
				    <div class="table-responsive">
						<table class="table align-items-center table-flush">
						  <thead class="thead-light">
							<tr>
								<th>ID</th>
								<th>节点名称</th>
								<th>检测IP</th>
								<th>端口号</th>
								<th>结果</th>
							</tr>
                            </thead>
							<tbody id="checkResult">
										
						  </tbody>
						</table>
					</div>
				</div>	
			</div><!--card-->
          
        </div>
      </div><!--row-->
	  
	  <!--删除modal-->
		<div class="modal fade" id="delete_modal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="deleteModalLabel" class="text-danger">确认删除吗?</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>请问你确认要删除吗?</p>
				</div>	 
		      </div>
			    <div class="modal-footer">
                    <button id="delete_input" type="button" class="btn btn-primary">确认提交</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
		    </div>
		  </div>
		</div>
      <!--check nodes modal-->
		<div class="modal fade" id="check_nodes_modal" tabindex="-1" role="dialog" aria-labelledby="checknodesModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
					<div class="modal-header">
                        <h4 id="checknodesModalLabel">系统通知</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
                    </div>
                    <div class="modal-body">
						<!-- Card body -->
						<div class="card-body">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>正在检测, 请勿关闭页面....</p>
						</div>	 
					</div>
                    <div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
					</div>
                </div>
            </div>
        </div>
		{include file='dialog.tpl'}
       
	  {include file='admin/footer.tpl'}
<script src="//cdn.jsdelivr.net/npm/datatables.net@1.10.19"></script>  
<script src="/theme/czssr/main/js/dataTables.material.min.js"></script>

<script>
    function delete_modal_show(id) {
        deleteid = id;
        $("#delete_modal").modal();
    }

    {include file='table/js_1.tpl'}

    window.addEventListener('load', () => {

        function delete_id() {
            $.ajax({
                type: "DELETE",
                url:"/admin/node",
                dataType: "json",
                data: {
                    id: deleteid
                },
                success: data => {
                    if (data.ret) {
                        $("#delete_modal").modal("hide");
                        $("#result").modal();
                        $("#msg").html(data.msg);
                        {include file='table/js_delete.tpl'}
                    } else {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            });
        }
		$("#delete_input").on("click", delete_id);
      {include file='table/js_2.tpl'}
    })

</script>