<?php
namespace Aws\CloudWatchEvents;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon CloudWatch Events** service.
 *
 * @method \Aws\Result activateEventSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise activateEventSourceAsync(array $args = [])
 * @method \Aws\Result createEventBus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createEventBusAsync(array $args = [])
 * @method \Aws\Result createPartnerEventSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPartnerEventSourceAsync(array $args = [])
 * @method \Aws\Result deactivateEventSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deactivateEventSourceAsync(array $args = [])
 * @method \Aws\Result deleteEventBus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteEventBusAsync(array $args = [])
 * @method \Aws\Result deletePartnerEventSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePartnerEventSourceAsync(array $args = [])
 * @method \Aws\Result deleteRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteRuleAsync(array $args = [])
 * @method \Aws\Result describeEventBus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeEventBusAsync(array $args = [])
 * @method \Aws\Result describeEventSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeEventSourceAsync(array $args = [])
 * @method \Aws\Result describePartnerEventSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describePartnerEventSourceAsync(array $args = [])
 * @method \Aws\Result describeRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeRuleAsync(array $args = [])
 * @method \Aws\Result disableRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disableRuleAsync(array $args = [])
 * @method \Aws\Result enableRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise enableRuleAsync(array $args = [])
 * @method \Aws\Result listEventBuses(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEventBusesAsync(array $args = [])
 * @method \Aws\Result listEventSources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEventSourcesAsync(array $args = [])
 * @method \Aws\Result listPartnerEventSourceAccounts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPartnerEventSourceAccountsAsync(array $args = [])
 * @method \Aws\Result listPartnerEventSources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPartnerEventSourcesAsync(array $args = [])
 * @method \Aws\Result listRuleNamesByTarget(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listRuleNamesByTargetAsync(array $args = [])
 * @method \Aws\Result listRules(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listRulesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listTargetsByRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTargetsByRuleAsync(array $args = [])
 * @method \Aws\Result putEvents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putEventsAsync(array $args = [])
 * @method \Aws\Result putPartnerEvents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putPartnerEventsAsync(array $args = [])
 * @method \Aws\Result putPermission(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putPermissionAsync(array $args = [])
 * @method \Aws\Result putRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putRuleAsync(array $args = [])
 * @method \Aws\Result putTargets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putTargetsAsync(array $args = [])
 * @method \Aws\Result removePermission(array $args = [])
 * @method \GuzzleHttp\Promise\Promise removePermissionAsync(array $args = [])
 * @method \Aws\Result removeTargets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise removeTargetsAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result testEventPattern(array $args = [])
 * @method \GuzzleHttp\Promise\Promise testEventPatternAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 */
class CloudWatchEventsClient extends AwsClient {}
