<?php
/**
 * Created by <PERSON>丶橘子.
 * User: Sakura丶橘子.  https://t.me/ssrser
 * Date: 2019/9/12
 * Time: 8:16 AM
 */

namespace App\Services\Gateway;

use App\Services\Auth;
use App\Services\Config;
use App\Models\User;
use App\Models\Code;
use App\Models\Paylist;
use App\Services\View;

class Umikm extends AbstractPayment
{

    function isHTTPS()
    {
        define('HTTPS', false);
        if (defined('HTTPS') && HTTPS) return true;
        if (!isset($_SERVER)) return FALSE;
        if (!isset($_SERVER['HTTPS'])) return FALSE;
        if ($_SERVER['HTTPS'] === 1) {  //Apache
            return TRUE;
        } elseif ($_SERVER['HTTPS'] === 'on') { //IIS
            return TRUE;
        } elseif ($_SERVER['SERVER_PORT'] == 443) { //其他
            return TRUE;
        }
        return FALSE;
    }
	/**
	* 校验$value是否非空
	*  if not set ,return true;
	*    if is null , return true;
	**/
	function checkEmpty($value)
	{
		if (!isset($value))
			return true;
		if ($value === null)
			return true;
		if (trim($value) === "")
			return true;
	
		return false;
	}
    function convertUrlQuery($query)
	{
		$queryParts = explode('&', $query);
		$params = array();
		foreach ($queryParts as $param) {
			$item = explode('=', $param);
			$params[$item[0]] = $item[1];
		}
		return $params;
	}
	function getSignString($params)
	{
		ksort($params);
		$signStr = "";
		foreach ($params as $key => $val) {
			$signStr .= $key . '=' .$val . '&';
		}
		return rtrim($signStr, '&');
	}
		
	function signEncrypt($params, $umikm_privateKey)
	{
		$result = array('status' => 0, 'msg' => '加密失败', 'sign' => '');
		if (empty($params) || empty($umikm_privateKey)) {
			return $result;
		}
		try {
			if (openssl_sign($params, $signedMsg, $umikm_privateKey, OPENSSL_ALGO_SHA256)) {
					$result['status'] = 1;
					$result['sign'] = base64_encode($signedMsg);
					$result['msg'] = "加密成功";
			}
		} catch (\Exception $e) {
		}

		return $result;
	}
  
	function signCheck($params, $sign, $publicKey)
	{
		$success = false;
		try {
			// 签名验证
			$success = openssl_verify($params, base64_decode($sign), $publicKey, OPENSSL_ALGO_SHA256);
			return $success;
		} catch (\Exception $e) {
	
		}

		return $success;
	
	}
	function https_post($url, $data)
	{
	
	    $curl = curl_init();
	    curl_setopt($curl, CURLOPT_URL, $url);
	    if (!empty($data)) {
	        curl_setopt($curl, CURLOPT_POST, 1);
	        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
	    }
	    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
	    $output = curl_exec($curl);  //执行操作
        if (curl_errno($curl)) {
            $output = 'Errno';//.curl_error($curl);//捕抓异常
        }
	    curl_close($curl);
	    return $output;
	
	}

	function buildRequestForm($para_temp, $getWay)
	{
		$sHtml = "<form id='zyupaysubmit' name='zyupaysubmit' action='" . $getWay . "' method='POST'>";
		foreach ($para_temp as $key => $val) {
			if (false === self::checkEmpty($val)) {
				$val = str_replace("'", "&apos;", $val);
				$sHtml .= "<input type='hidden' name='" . $key . "' value='" . $val . "'/>";
			}
		}

		$sHtml = $sHtml . "<input type='submit' value='ok' style='display:none;'></form>";
		$sHtml = $sHtml . "<script>document.forms['zyupaysubmit'].submit();</script>";
		return $sHtml;
	}
	

	public function searchorder($out_trade_no)
    {
        $umikm_mch_id = Config::get('umikm_mch_id');//这里改成商户ID
        $umikm_appid = Config::get('umikm_appid'); //这是您的子应用ID
        $umikm_sysPublicKey = Config::get('umikm_sysPublicKey'); //这是您的公钥
        $umikm_priKey = Config::get('umikm_privateKey'); //这是您的私钥
      
		$getWay = "https://payapi.umikm.com";//上游网关
		
		$url = (self::isHTTPS() ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'];
		
    	$params = [
    		"appid"        => $umikm_appid,
    		"mch_id"       => $umikm_mch_id,
			"out_trade_no" => $out_trade_no,//用户订单号
			"sign_type"    => "RSA",//rsa支付宝签名
    	];
		
	  	$signStr = self::getSignString($params);
        $umikm_privateKey = "-----BEGIN RSA PRIVATE KEY-----\n" .
        wordwrap($umikm_priKey, 64, "\n", true) .
        "\n-----END RSA PRIVATE KEY-----";
		
		$signResult = self::signEncrypt($signStr, $umikm_privateKey);

	    if (empty($signResult) || !$signResult['status']) {
           die("加密码异常");
        }

	    $params['sign'] = $signResult['sign'];
        $result = self::https_post($getWay . '/api/payment/search', $params);
	    $json = json_decode($result, true);
		
		if (!empty($json)) {
            $back_data = array();
            $back_data['status'] = $json['status'];    //订单状态
		} else {
			$res['ret'] = 0;
            $res['msg'] = "发生错误". $json['code']."请重新发起支付";
            return $response->getBody()->write(json_encode($res));
        }
		        // 获取订单状态
		$return = $back_data['status'];

        return $return;

	}
    public function purchase($request, $response, $args)
    {

        $user = Auth::getUser();
        $price = $request->getParam('price');
        if ($price <= 0) {
			$return['ret'] = 0;
			$return['msg'] = "非法的金额";
            return json_encode($return);
        }
        $pl = new Paylist();
        $pl->userid = $user->id;
        $pl->total = $price;
        $pl->tradeno = (string)time()."UID".(string)$user->id;
        $pl->save();
        
        $url = (self::isHTTPS() ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'];

        // 获取收款二维码内容
        $qrCodeArr = $url."/pay/wx_umikm?price=".$price."&tradeno=".$pl->tradeno;
        $qrCodeContent = $qrCodeArr;
	
        $return['ret'] = 1;
        $return['qrcode'] = $qrCodeContent;
        $return['price'] = $pl->total;
        $return['pid'] = $pl->tradeno;
	
        return json_encode($return);
		
    }
    public function XwPublic($request, $response, $args)
    {
		$umikm_mch_id = Config::get('umikm_mch_id');//这里改成商户ID
        $umikm_appid = Config::get('umikm_appid'); //这是您的子应用ID
        $umikm_sysPublicKey = Config::get('umikm_sysPublicKey'); //这是您的公钥
        $umikm_priKey = Config::get('umikm_privateKey'); //这是您的私钥

		$getWay = "https://payapi.umikm.com";//上游网关
		
        $url = (self::isHTTPS() ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'];
        $url2 = Config::get('apiUrl');
		$arr = parse_url($url.$_SERVER['REQUEST_URI']);
		$arr_query = self::convertUrlQuery($arr['query']);
		$arr_price = $arr_query;
		$pl = Paylist::where("tradeno", $arr_price['tradeno'])->where("status", "<>", 1)->first();
		$price = $arr_price['price'];
		if ($pl == null) {
			exit("订单不存在, 请重新发起支付!");
		}
		$params = [
              	"mch_id"       => $umikm_mch_id,
              	"appid"        => $umikm_appid,
              	"out_trade_no" => $pl->tradeno,//用户订单号
              	"total_fee"    => $price,//订单支付金额
              	"fee_type"     => "CNY",
              	"channel_code" => "XwPublic",   //小微商户支付
              	"notify_url"   => $url2 . '/payment_b/notify',//异步通知地址
              	"callback_url" => $url . '/user/code',//同步通知地址
              	"create_ip"    => $_SERVER["REMOTE_ADDR"],//下单IP
              	"body"         => "生活用品",
              	"time_start"   => date("Y-m-d H:i:s", time()),//下单时间
              	"sign_type"    => "RSA",//rsa支付宝签名
          	];
	
          	$signStr = self::getSignString($params);
          	$umikm_privateKey = "-----BEGIN RSA PRIVATE KEY-----\n" .
          	wordwrap($umikm_priKey, 64, "\n", true) .
          	"\n-----END RSA PRIVATE KEY-----";
	
          	$signResult = self::signEncrypt($signStr, $umikm_privateKey);

          	if (empty($signResult) || !$signResult['status']) {
             	die("加密码异常");
          	}

          	$params['sign'] = $signResult['sign'];
		
		$result = self::buildRequestForm($params,$getWay.'/api/payment/index');
		return View::getSmarty()->assign('result', $result)->fetch('user/umikm.tpl');
	}
	
    public function notify($request, $response, $args)
    {
		$resultData = $_POST;
		
        $success = self::searchorder($resultData['out_trade_no']);//从官方获取订单状态，以免伪造
			
           if ($success == "0000") {
			    $pay_id = $resultData['out_trade_no']; //支付流水号
                self::postPayment($pay_id, "微信支付");
                exit ("OK");
			} else {
			    //验签失败
				exit("fail");
			}
		  
       return;
    }

    function getPurchaseHTML()
    {
      //  return View::getSmarty()->fetch("user/umikm.tpl");
    }

    function getReturnHTML($request, $response, $args)
    {
        return 0;
    }

    function getStatus($request, $response, $args)
    {
        $p = Paylist::where("tradeno", $_POST['pid'])->first();
        $return['ret'] = 1;
        $return['result'] = $p->status;
        return json_encode($return);
    }
}