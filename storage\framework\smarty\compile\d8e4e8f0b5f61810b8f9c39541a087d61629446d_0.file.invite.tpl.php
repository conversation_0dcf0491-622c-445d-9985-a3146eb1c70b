<?php
/* Smarty version 3.1.33, created on 2022-02-18 00:14:55
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/invite.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_620e747fd71605_84836893',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'd8e4e8f0b5f61810b8f9c39541a087d61629446d' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/admin/invite.tpl',
      1 => 1580585698,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/main.tpl' => 1,
    'file:table/checkbox.tpl' => 1,
    'file:table/table.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:admin/footer.tpl' => 1,
    'file:table/js_1.tpl' => 1,
    'file:table/lang_chinese.tpl' => 1,
    'file:table/js_3.tpl' => 1,
    'file:table/js_delete.tpl' => 1,
  ),
),false)) {
function content_620e747fd71605_84836893 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Invite</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/invite">邀请和返利</a></li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/user" class="btn btn-sm btn-neutral">用户</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">邀请和返利</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<a class="btn btn-primary btn-sm mb-3" href="/admin/level/create">新建等级</a>
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>公共邀请码功能已废弃，如需开放注册请在 .config.php 中将 register_mode 项目设置为 open.</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">需要增加邀请链接数量的用户:</label>
							<input id="uid" class="form-control form-control-sm" type="text">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>填写用户的ID, 或者用户的完整邮箱. </p>
						</div>
						<div class="form-group">
							<label class="form-control-label">邀请链接数量(仅数字):</label>
							<input id="num" class="form-control form-control-sm" type="number">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>增加用户邀请链接使用数量, 仅数字. </p>
						</div>
					</div>
					<div class="modal-footer">
						<button id="invite" type="button" class="btn btn-primary">确认增加</button>
					</div>
				</div>
			</div>
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h4 class="mb-0">返利记录</h4>
              </div>
				<div class="card-body">
					<blockquote class="blockquote mb-0">
						<p class="description badge-dot mr-4"><i class="bg-warning"></i>显示表项: <?php $_smarty_tpl->_subTemplateRender('file:table/checkbox.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?></p>
						<p class="description badge-dot mr-4"><i class="bg-warning"></i>如果客户产生退款, 请删除删除对应的AFF记录, 否则会重复计算可用邀请余额.</p>
						<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
					</blockquote>
				</div>

              <!-- Card body -->
			  <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive py-4">
				  <?php $_smarty_tpl->_subTemplateRender('file:table/table.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
					
				</div>
              </div>
			
            </div><!--card-->

        </div>
      </div><!--row-->
	   <!--delete modal-->
	   <div class="modal fade" id="delete_modal" tabindex="-1" role="dialog" aria-labelledby="deleteidModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="deleteidModalLabel" class="text-danger">删除信息确认</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p id="deleteid" class="description">ID：</p>
		        </div>
		      </div>
			   <div class="modal-footer">
                    <button id="delete_input" type="button" class="btn btn-primary">确认提交</button>
               </div>
		   </div>
		 </div>
	   </div>
		<?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php $_smarty_tpl->_subTemplateRender('file:admin/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
echo '<script'; ?>
 src="//cdn.jsdelivr.net/npm/datatables.net@1.10.19"><?php echo '</script'; ?>
>  
<?php echo '<script'; ?>
 src="/theme/czssr/main/js/dataTables.material.min.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
>
  	function del(delete_id) {
		$("#deleteid").html("返利记录ID: "+delete_id);
		deleteid = delete_id;
        $("#delete_modal").modal();
	}

 <?php $_smarty_tpl->_subTemplateRender('file:table/js_1.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
    window.addEventListener('load', () => {
       table_1 = $('#table_1').DataTable({
           order: [[1, 'desc']],
           stateSave: true,
           serverSide: true,
           ajax: {
               url: "/admin/payback/ajax",
               type: "POST",
           },
           columns: [
               
               {"data": "op", "orderable": false},
               {"data": "id"},
               {"data": "total"},
               {"data": "userid"},
               {"data": "event_user_name"},
               {"data": "ref_by"},
               {"data": "ref_user_name"},
			   {"data": "ref_get"},
			   {"data": "ref_traffic", "orderable": false},
			   {"data": "datetime", "orderable": false}
			   
            ],
			"columnDefs": [
                {
                    targets: ['_all'],
                    className: 'mdl-data-table__cell--non-numeric'
                }
            ],

            <?php $_smarty_tpl->_subTemplateRender('file:table/lang_chinese.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
        });

       <?php $_smarty_tpl->_subTemplateRender('file:table/js_3.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>  
    $("#delete_input").click(function () {
		$.ajax({
			type: "DELETE",
			url: "/admin/invitedelete",
			dataType: "json",
			data: {
				deleteid: deleteid
			},
			success: function (data) {
				if (data.ret) {
                  $("#delete_modal").modal('hide');
					swal({
                       title: "删除结果",
                       text: data.msg, 
                       type:"success"
                       });
					   <?php $_smarty_tpl->_subTemplateRender('file:table/js_delete.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
                 // window.setTimeout("location.href=window.location.href", 2000);
				} else {
                    $("#delete_modal").modal('hide');
					swal('Oops...',data.msg,'error');
				}
			},
			error: function (jqXHR) {
				$("#result").modal();
                $("#msg").html("发生错误了: " + jqXHR.status);
			}
		});
	});
   })
<?php echo '</script'; ?>
><?php }
}
