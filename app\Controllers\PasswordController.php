<?php

namespace App\Controllers;

use App\Models\User;
use App\Models\PasswordReset;
use App\Services\Password;
use App\Services\Config;
use App\Utils\Geetest;
use App\Utils\CaptchaClient;
use App\Utils\Hash;

/***
 * Class Password
 * @package App\Controllers
 * 密码重置
 */

class PasswordController extends BaseController
{
    /*
    public function reset()
    {
        return $this->view()->display('password/reset.tpl');
    }*/
    
    public function reset()
    {
    	$GtSdk = null;
        $recaptcha_sitekey = null;
    	if (Config::get('enable_reset_captcha') == 'true'){
            switch(Config::get('captcha_provider'))
            {
                case 'recaptcha':
                    $recaptcha_sitekey = Config::get('recaptcha_sitekey');
                    break;
                case 'geetest':
                    $uid = time().rand(1, 10000) ;
                    $GtSdk = Geetest::get($uid);
                    break;
                case 'CaptchaDX':
                	$CaptchaDX_AppId = Config::get('CaptchaDX_AppId');
                	break;
            }
        }
        return $this->view()
            ->assign('geetest_html', $GtSdk)
            ->assign('base_url', Config::get('baseUrl'))
            ->assign('recaptcha_sitekey', $recaptcha_sitekey)
            ->assign('CaptchaDX_AppId', $CaptchaDX_AppId)
            ->display('password/reset.tpl');
    }
    public function handleReset($request, $response, $args)
    {
        $email =  $request->getParam('email');
        // check limit
		$email = trim($email);  //移除多余空白字符
        $email = strtolower($email);  //转换为小写

        if (Config::get('enable_reset_captcha') == 'true') {
            switch(Config::get('captcha_provider'))
            {
                case 'recaptcha':
                    $recaptcha = $request->getParam('recaptcha');
                    if ($recaptcha == ''){
                        $ret = false;
                    }else{
                        $json = file_get_contents("https://recaptcha.net/recaptcha/api/siteverify?secret=".Config::get('recaptcha_secret')."&response=".$recaptcha);
                        $ret = json_decode($json)->success;
                    }
                    break;
                case 'geetest':
                    $ret = Geetest::verify($request->getParam('geetest_challenge'), $request->getParam('geetest_validate'), $request->getParam('geetest_seccode'));
                    break;
                case 'CaptchaDX':
                   if(empty($request->getParam('token'))){
                   	  $ret = false;
                   }else{
                      $CaptchaClient = new CaptchaClient();
                      $ret = $CaptchaClient->verifyToken($request->getParam('token'));
                   }
                   break;
            }
            if (!$ret) {
                $rs['ret'] = 0;
                $rs['msg'] = "系统无法接受您的验证结果，请刷新页面后重试。";
                return $response->getBody()->write(json_encode($res));
            }
        }
        //geet及验end
        $user = User::where('email', $email)->first();
        if ($user == null) {
            $rs['ret'] = 0;
            $rs['msg'] = '此邮箱不存在,请刷新页面后重填';
            return $response->getBody()->write(json_encode($rs));
        }
        // send email start
        $rs['ret'] = 1;
        $rs['msg'] = '重置邮件已经发送,请检查邮箱.';
        if (Password::sendResetEmail($email)) {
            $res['msg'] = "邮件发送失败，请联系网站管理员。";
        }

        return $response->getBody()->write(json_encode($rs));
    }

    public function token($request, $response, $args)
    {
        $token = $args['token'];
        return $this->view()->assign('token', $token)->display('password/token.tpl');
    }

    public function handleToken($request, $response, $args)
    {
        $tokenStr = $args['token'];
        $password =  $request->getParam('password');
        $repasswd =  $request->getParam('repasswd');

        if ($password != $repasswd) {
            $res['ret'] = 0;
            $res['msg'] = "两次输入不符合";
            return $response->getBody()->write(json_encode($res));
        }

        if (strlen($password) < 8) {
            $res['ret'] = 0;
            $res['msg'] = "密码太短啦";
            return $response->getBody()->write(json_encode($res));
        }

        // check token
        $token = PasswordReset::where('token', $tokenStr)->first();
        if ($token == null || $token->expire_time < time()) {
            $rs['ret'] = 0;
            $rs['msg'] = '链接已经失效,请重新获取';
            return $response->getBody()->write(json_encode($rs));
        }

        $user = User::where('email', $token->email)->first();
        if ($user == null) {
            $rs['ret'] = 0;
            $rs['msg'] = '链接已经失效,请重新获取';
            return $response->getBody()->write(json_encode($rs));
        }

        // reset password
        $hashPassword = Hash::passwordHash($password);
        $user->pass = $hashPassword;
        $user->ga_enable = 0;
        if (!$user->save()) {
            $rs['ret'] = 0;
            $rs['msg'] = '重置失败,请重试';
            return $response->getBody()->write(json_encode($rs));
        }
        $rs['ret'] = 1;
        $rs['msg'] = '重置成功';

        $user->clean_link();

        return $response->getBody()->write(json_encode($rs));
    }
}
