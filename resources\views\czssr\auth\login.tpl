{include file='header.tpl'}

<body>
<!-- 以下代码变黑白色 -->
<!-- <style type="text/css">
html {
filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
-webkit-filter: grayscale(100%);}
</style> -->
<!-- 以上代码变黑白色 -->
  <header class="header-global">
    <nav id="navbar-main" class="navbar navbar-main navbar-expand-lg navbar-transparent navbar-light headroom">
      <div class="container">
        <a class="navbar-brand mr-lg-5" href="/">
          <img src="/theme/czssr/main/picture/white.png" alt="brand">
          <span class="engname1"> {$config['engname']}</span>
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar_global" aria-controls="navbar_global" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button><!--菜单button-->
        <div class="navbar-collapse collapse" id="navbar_global">
          <div class="navbar-collapse-header">
            <div class="row">
              <div class="col-6 collapse-brand">
                <a href="/">
                  <img src="/theme/czssr/main/picture/blue.png" alt="brand">
                  <span class="engname3"> {$config['engname']}</span>
                </a>
              </div>
              <div class="col-6 collapse-close"><!--关闭button-->
                <button type="button" class="navbar-toggler" data-toggle="collapse" data-target="#navbar_global" aria-controls="navbar_global" aria-expanded="false" aria-label="Toggle navigation">
                  <span></span>
                  <span></span>
                </button>
              </div>
            </div>
          </div>
          <ul class="navbar-nav navbar-nav-hover align-items-lg-center">
				<li class="nav-item ">
				{if $user->isLogin}
				<a href="/user" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-sign-in"></i>
					<span class="nav-link-inner--text">My Account</span>
				</a>
				{else}
				<a href="/auth/login" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-sign-in"></i>
					<span class="nav-link-inner--text">Login</span>
				</a>
				{/if}
				</li>
				<li class="nav-item ">
				{if $config['register_mode']!='close'}
				<a href="/auth/register" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-user-plus"></i>
					<span class="nav-link-inner--text">Sign Up</span>
				</a>
				{else}
				<a href="javascript:void(0);" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-user-plus"></i>
					<span class="nav-link-inner--text">Stop register</span>
				</a>
				{/if}
				</li>
				<li class="nav-item ">
				<a href="/password/reset" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-repeat"></i>
					<span class="nav-link-inner--text">Reset Password</span>
				</a>
				</li>
				<li class="nav-item ">
				<a href="/doc" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-question-circle"></i>
					<span class="nav-link-inner--text">Support</span>
				</a>
				</li>
           </ul>
            <ul class="navbar-nav align-items-lg-center ml-lg-auto">
				<li class="nav-item">
				<!--<a class="nav-link nav-link-icon" href="#" target="_blank" data-toggle="tooltip" title="Star us on Telegram">
					<i class="fa fa-telegram"></i>
					<span class="nav-link-inner--text d-lg-none">Telegram</span>
				</a>-->
				
				</li>
            </ul>
        </div>
      </div>
    </nav>
  </header>
  <main>
    <section class="section section-shaped section-lg" style="min-height: calc(100vh);">
      <div class="shape shape-style-1 bg-gradient-default">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
      <div class="container pt-lg-md">
        <div class="row justify-content-center">
          <div class="col-lg-5">
            <div class="card bg-secondary shadow border-0">
              <div class="card-header bg-white">
                <div class="text-muted text-center mb-3"><small>Sign in with</small></div>
                <div class="btn-wrapper text-center">
                  {if $config['enable_tg'] == 'true'}
                  <a href="#" class="btn btn-neutral btn-icon" id="calltgauth" data-toggle="modal" data-target="#modal-default">
                    <span class="btn-inner--icon">
                      <img alt="image" src="/theme/czssr/main/picture/telegram.svg">
                    </span>
                    <span class="">Telegram</span>
                  </a>
                  {/if}
                  {if $config['enable_qq'] == 'true'}
				
                  <a href="{$login_url}" class="btn btn-neutral btn-icon" id="qqlogin">
                    <span class="btn-inner--icon">
                      <img alt="image" src="/theme/czssr/main/picture/qq.svg">
                    </span>
                    <span class="">Tencent</span>
                  </a>
				
                  {/if}
                </div>
				 <div class="modal fade" id="modal-default" tabindex="-1" role="dialog" aria-labelledby="modal-default" aria-hidden="true">
                    <div class="modal-dialog modal- modal-dialog-centered modal-" role="document">
                      <div class="modal-content">
                        <div class="modal-header">
                          <h6 class="modal-title" id="modal-title-default">Sign in with Telegram.</h6>
                          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true" style="font-size:1em">×</span>
                          </button>
                        </div>
                        <div class="modal-body">
                          <div class="py-3 text-center">
                            <i class="fa fa-telegram fa-4x"></i>
                            <h5 class="heading mt-4">一键登陆</h5>
							<p id="telegram-alert">正在载入 Telegram，如果长时间未显示请刷新页面或检查代理</p>
							<div class="text-center" id="telegram-login-box"></div>
							<p>或者添加机器人账号 <a href="https://t.me/{$telegram_bot}" target="_blank">@{$telegram_bot}</a>，发送下面的数字给它。</p>
							<div class="text-center">
								<h2><code id="code_number">{$login_number}</code></h2>
							</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
               </div>
              <div class="card-body px-lg-5 py-lg-5">
                <form action="javascript:void(0);" method="POST">
                  <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                      <div class="input-group-prepend">
                        <span class="input-group-text"><i class="ni ni-email-83"></i></span>
                      </div>
                      <input class="form-control" placeholder="Email" type="email" id="email">
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="input-group input-group-alternative">
                      <div class="input-group-prepend">
                        <span class="input-group-text"><i class="ni ni-lock-circle-open"></i></span>
                      </div>
                      <input class="form-control" placeholder="Password" type="password" id="passwd">
                    </div>
                  </div>
				  {if $geetest_html != null}
				  <div class="form-group">
                    <div class="input-group-alternative">
                        <div id="embed-captcha"></div>
                    </div>
                  </div>
                  {/if}
                  {if $recaptcha_sitekey != null}
                  <div class="form-group">
                    <div class="input-group-alternative">
                        <div align="center" class="g-recaptcha" data-sitekey="{$recaptcha_sitekey}"></div>
                    </div>
                  </div>
                  {/if}
                 {if $CaptchaDX_AppId != null}
                  <div class="form-group mb-3">
                    <div class="input-group-alternative">
                        <div align="center" class="g-recaptcha" data-sitekey="{$recaptcha_sitekey}"></div>
						<div id="CaptchaDX"></div>
                    </div>
                  </div>
                  {/if}
                  <div class="custom-control custom-control-alternative custom-checkbox">
                    <input class="custom-control-input" id="remember_me" type="checkbox">
                    <label class="custom-control-label" for="remember_me">
                      <span>Remember me</span>
                    </label>
                  </div>
                  <div class="text-center">
                    <button type="button" class="btn btn-primary my-4" id="login">Sign in</button>
                  </div>
                </form>
				
              </div>
            </div>

          </div>
        </div>
      </div>
    </section>
  </main>
  <!-- Footer -->
{include file='footer.tpl'}



{if $config['enable_tg'] == 'true'}
    <script>
        $(document).ready(function () {
            $("#calltgauth").click(
                    function () {
                        f();
                    }
            );

            function f() {
                $.ajax({
                    type: "POST",
                    url: "qrcode_check",
                    dataType: "json",
                    data: {
                        token: "{$login_token}",
                        number: "{$login_number}"
                    },
                    success: (data) => {
                        if (data.ret > 0) {
                            clearTimeout(tid);

                            $.ajax({
                                type: "POST",
                                url: "/auth/qrcode_login",
                                dataType: "json",
                                data: {
                                    token: "{$login_token}",
                                    number: "{$login_number}"
                                },
                                success: (data) => {
                                    if (data.ret) {
                                        swal({
											title: "<span style='color: #9954bb; font-size:2rem; font-weight: 500'>"+data.user+"</span>",
											text: "Welcome, 欢迎进入{$config["appName"]}~!",

										});
                                        window.setTimeout("location.href='/user'", {$config['jump_delay']});
                                    }
                                },
                                error: (jqXHR) => {
                                    swal('Oops...',"发生错误"+jqXHR.status,'error');
                                }
                            });

                        } else {
                            if (data.ret === -1) {
                                $('#code_number').replaceWith('<code id="code_number">此数字已经过期，请刷新页面后重试。</code>');
                            }
                        }
                    },
                    error: (jqXHR) => {
                        if (jqXHR.status !== 200 && jqXHR.status !== 0) {
                            swal('Oops...',"发生错误"+jqXHR.status,'error');
                        }
                    }
                });
                tid = setTimeout(f, 2500); //循环调用触发setTimeout
            }


        })
    </script>
    <script>
        $(document).ready(function () {
            var el = document.createElement('script');
            document.getElementById('telegram-login-box').append(el);
            el.onload = function () {
                $('#telegram-alert').remove()
            }
            el.src = 'https://telegram.org/js/telegram-widget.js?4';
            el.setAttribute('data-size', 'large')
            el.setAttribute('data-telegram-login', '{$telegram_bot}')
            el.setAttribute('data-auth-url', '{$base_url}/auth/telegram_oauth')
            el.setAttribute('data-request-access', 'write')
        });
    </script>
{/if}
{if $geetest_html != null}
  {if isset($geetest_html)}
    <script src="//static.geetest.com/static/tools/gt.js"></script>
    <script>
        var handlerEmbed = function (captchaObj) {
            // 将验证码加到id为captcha的元素里

            captchaObj.onSuccess(function () {
                validate = captchaObj.getValidate();
            });

            captchaObj.appendTo("#embed-captcha");

            captcha = captchaObj;
            // 更多接口参考：http://www.geetest.com/install/sections/idx-client-sdk.html
        };

        initGeetest({
            gt: "{$geetest_html->gt}",
            challenge: "{$geetest_html->challenge}",
            product: "embed", // 产品形式，包括：float，embed，popup。注意只对PC版验证码有效
            offline: {if $geetest_html->success}0{else}1{/if} // 表示用户后台检测极验服务器是否宕机，与SDK配合，用户一般不需要关注
        }, handlerEmbed);
    </script>
  {/if}
{/if}
{if $recaptcha_sitekey != null}
    <script src="https://recaptcha.net/recaptcha/api.js" async defer></script>
{/if}
{if $CaptchaDX_AppId != null}
	<script src="https://cdn.dingxiang-inc.com/ctu-group/captcha-ui/index.js"></script>
{/if}
<script>
$(document).ready(function(){
{if $CaptchaDX_AppId != null}
var appId = '{$config["CaptchaDX_AppId"]}';
 var myCaptcha = _dx.Captcha(document.getElementById('CaptchaDX'), {
   appId: appId,
	type: 'basic', 
   style: 'oneclick',
   width: '310',
 });
var token = "";
myCaptcha.on('verifySuccess', function(security_code){
	  if (security_code != null || security_code != 'undefined' || security_code != ''){
	     token = security_code;  //security_code.split(':',1);
	  }
});
{/if}

    function login(){
		{if $geetest_html != null}
          	if (typeof validate == 'undefined') {
              	swal('Oops...',"请滑动验证码来完成验证",'warning');
              	return;
          	}
          	if (!validate) {
              	swal('Oops...',"请滑动验证码来完成验证",'warning');
              	return;
          	}
        {/if}
		{if $CaptchaDX_AppId != null}
        if (token == null || token == '' || token == 'undifined'){
           swal('Oops...',"请滑动验证码来完成验证",'warning');
		   return;
        }
        {/if}
		document.getElementById("login").disabled = true;

        $.ajax({
            type:"POST",
            url:"/auth/login",
            dataType:"json",
            data:{
                email: $("#email").val(),
                passwd: $("#passwd").val(),
			  {if $CaptchaDX_AppId != null}
			    token: token,
			  {/if}
			//	code: $("#code").val(),//取消两步验证
              {if $recaptcha_sitekey != null}
                recaptcha: grecaptcha.getResponse(),
              {/if}
                remember_me: $("#remember_me:checked").val(){if $geetest_html != null},
                geetest_challenge: validate.geetest_challenge,
                geetest_validate: validate.geetest_validate,
                geetest_seccode: validate.geetest_seccode{/if}
            },
            success:function(data){
          		var user= data.user;
                var jump_link = data.href;
                  /*
                  swal({
                    title: "Merry Christmas~!",
                    html: "<span style='color: #9954bb; font-size:2rem; font-weight: 500'>"+user+"</span>",
                    imageUrl: "/images/shendan.png",
                  }).then(function(){
                    window.location.href = '/user';
                  });
                */
                if(data.ret == 1){
                    swal({
                      title: "<span style='color: #9954bb; font-size:2rem; font-weight: 500'>"+user+"</span>",
                      text: "Welcome, 欢迎进入{$config["appName"]}~!",

                    });
                  window.setTimeout('location.href="'+jump_link+'"', {$config['jump_delay']});
                }else{
                	swal('Oops...',data.msg,'error');
					document.getElementById("login").disabled = false;
                    {if $geetest_html != null}
                     captcha.reset();
                    {/if}
				    {if $CaptchaDX_AppId != null}
				     myCaptcha.reload();
				    {/if}
		        }
            },
            error:function(jqXHR){
				swal('Oops...',"发生错误"+jqXHR.status,'error');
				document.getElementById("login").disabled = false;
                {if $geetest_html != null}
                 captcha.reset();
                {/if}
				{if $CaptchaDX_AppId != null}
		         myCaptcha.reload();
			    {/if}
		    }
        });
    }
    $("html").keydown(function(event){
        if(event.keyCode==13){
            login();
        }
    });
    $("#login").click(function(){
        login();
    });

})
</script>

  