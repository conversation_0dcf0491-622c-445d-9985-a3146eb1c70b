{include file='admin/main.tpl'}


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Create Level</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/level">等级设置</a></li>
                  <li class="breadcrumb-item active" aria-current="page">编辑等级</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/ticket" class="btn btn-sm btn-neutral">工单</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">编辑用户等级 #{$level->name}</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group">
							<label class="form-control-label">用户等级名称:</label>
							<input id="name" class="form-control form-control-sm" type="text" value="{$level->name}">
						</div>
						<div class="form-group">
							<label class="form-control-label">用户等级(仅数字,务必有0等级存在):</label>
							<input id="level" class="form-control form-control-sm" type="number" value="{$level->level}">
						</div>
					</div>
					<div class="modal-footer">
						<button id="submit" type="button" class="btn btn-primary">确认修改</button>
					</div>
				</div>
			</div>

        </div>
      </div><!--row-->
	  
		{include file='dialog.tpl'}
	  {include file='admin/footer.tpl'}


<script>

window.addEventListener('load', () => {
        	//检查输入合法性
  function validateLevel(value){
    var pattern = /^[1-9]\d*|0$/;
	if (pattern.test(value)) {
      return true; 
    }else{
      return false;
    }
  }   
        function submit() {
            if(!validateLevel($("#level").val())) {
  　　　		  swal('Oops...', "等级输入不合法,请检查后输入",'error');
            	return false;
    		}
    		if($("#level").val()==null || $("#level").val()=='' || $("#name").val()==null){
    		    swal('Oops...', "等级不能为空！",'error');
    		    return;
    		}
            $.ajax({
                type: "PUT",
                url: "/admin/level/{$level->id}",
                dataType: "json",
                data: {
					name: $$getValue('name'),
					level: $$getValue('level')
                },
                success: data => {
                    if (data.ret) {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", 2000);
                    } else {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $("#msg").html("发生错误了: " + jqXHR.status);
                }
            });
        }

        $("#submit").on("click", submit);

    });
    
</script>