<?php
namespace App\Command;

use App\Services\Config;
use App\Services\Mail;
use App\Models\User;
use Ozdemir\Datatables\Datatables;
use App\Utils\DatatablesHelper;
use App\Models\Ann;

class SendMail
{
	
	
	public static function sendMail()
    {   
    	
		$sendingCount = Ann::where('status', 2)->count();
		if($sendingCount > 0){//有正在发送的代码，直接停止执行
		   die();
		}
		$sendInfo = Ann::where('status', 1)->first();//查找第一个有待发送的数据	
		if(empty($sendInfo)) {//不存在，直接终止
		   die();
		}
		if($sendInfo->issend == 0){//不允许发送，直接终止
		   die();
		}
	
		$vip = $sendInfo->vip;
		$content = $sendInfo->content;
		//$userLastId = User::orderBy('id','desc')->value('id');
		$userLastId = User::where('class', ">=", $vip)->count();
		if($userLastId > 0){
			Ann::where('id', $sendInfo->id)->update(['status' => 2]);//执行过程中，状态改为正在执行
			//for($i = 0; $i <= $userLastId; $i+=Config::get('sendPageLimit')){
			//  $users = User::where('class', ">=", $vip)->where('id', '>', $i)->where('id', '<=', $i + Config::get('sendPageLimit'))->get();
			for($i = 0; $i < $userLastId; $i+=Config::get('sendPageLimit')){
				$users = User::where('class', ">=", $vip)->skip($i)->limit(Config::get('sendPageLimit'))->get();
				foreach($users as $user){
					$subject = Config::get('appName')."-公告";
					$to = $user->email;
					if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
					continue;
					}
					$text = $content;
					try {
					file_put_contents(PUBLIC_PATH.'/sendMail.log',$to."\n",FILE_APPEND);
					file_put_contents(PUBLIC_PATH.'/sendMail.log',$subject."\n",FILE_APPEND);
					file_put_contents(PUBLIC_PATH.'/sendMail.log',$text."\n",FILE_APPEND);
					file_put_contents(PUBLIC_PATH.'/sendMail.log','----------------------------'."\n",FILE_APPEND);
						Mail::send($to , $subject, 'news/warn.tpl', [
								"user" => $user,"text" => $text
						], [
						]);  
						$secound = rand(1,5);
						sleep($secound);
					// sleep(1);
					} catch (\Exception $e) { 
						continue;
					}
				}
			}
		}
		//执行完成，改状态为已完成//或没有符合条件用户
		Ann::where('id', $sendInfo->id)->update(['status' => 3]);
    }
    
}




