<?php
/* Smarty version 3.1.33, created on 2022-03-23 21:44:21
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/password/reset.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_623b2435c9b874_40160137',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'a599a8db54aefda6294219954477b2d95ae70925' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/password/reset.tpl',
      1 => 1648043051,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.tpl' => 1,
    'file:footer.tpl' => 1,
  ),
),false)) {
function content_623b2435c9b874_40160137 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:header.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<body>
<!-- 以下代码变黑白色 -->
<!-- <style type="text/css">
html {
filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
-webkit-filter: grayscale(100%);}
</style> -->
<!-- 以上代码变黑白色 -->
  <header class="header-global">
    <nav id="navbar-main" class="navbar navbar-main navbar-expand-lg navbar-transparent navbar-light headroom">
      <div class="container">
        <a class="navbar-brand mr-lg-5" href="/">
          <img src="/theme/czssr/main/picture/white.png" alt="brand">
          <span class="engname1"> <?php echo $_smarty_tpl->tpl_vars['config']->value['engname'];?>
</span>
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar_global" aria-controls="navbar_global" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button><!--菜单button-->
        <div class="navbar-collapse collapse" id="navbar_global">
          <div class="navbar-collapse-header">
            <div class="row">
              <div class="col-6 collapse-brand">
                <a href="/">
                  <img src="/theme/czssr/main/picture/blue.png" alt="brand">
                  <span class="engname3"> <?php echo $_smarty_tpl->tpl_vars['config']->value['engname'];?>
</span>
                </a>
              </div>
              <div class="col-6 collapse-close"><!--关闭button-->
                <button type="button" class="navbar-toggler" data-toggle="collapse" data-target="#navbar_global" aria-controls="navbar_global" aria-expanded="false" aria-label="Toggle navigation">
                  <span></span>
                  <span></span>
                </button>
              </div>
            </div>
          </div>
          <ul class="navbar-nav navbar-nav-hover align-items-lg-center">
				<li class="nav-item ">
				<?php if ($_smarty_tpl->tpl_vars['user']->value->isLogin) {?>
				<a href="/user" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-sign-in"></i>
					<span class="nav-link-inner--text">My Account</span>
				</a>
				<?php } else { ?>
				<a href="/auth/login" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-sign-in"></i>
					<span class="nav-link-inner--text">Login</span>
				</a>
				<?php }?>
				</li>
				<li class="nav-item ">
				<?php if ($_smarty_tpl->tpl_vars['config']->value['register_mode'] != 'close') {?>
				<a href="/auth/register" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-user-plus"></i>
					<span class="nav-link-inner--text">Sign Up</span>
				</a>
				<?php } else { ?>
				<a href="javascript:void(0);" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-user-plus"></i>
					<span class="nav-link-inner--text">Stop register</span>
				</a>
				<?php }?>
				</li>
				<li class="nav-item ">
				<a href="/password/reset" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-repeat"></i>
					<span class="nav-link-inner--text">Reset Password</span>
				</a>
				</li>
				<li class="nav-item ">
				<a href="/doc" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-question-circle"></i>
					<span class="nav-link-inner--text">Support</span>
				</a>
				</li>
           </ul>
            <ul class="navbar-nav align-items-lg-center ml-lg-auto">
				<li class="nav-item">
				<!--<a class="nav-link nav-link-icon" href="#" target="_blank" data-toggle="tooltip" title="Star us on Telegram">
					<i class="fa fa-telegram"></i>
					<span class="nav-link-inner--text d-lg-none">Telegram</span>
				</a>-->
				
				</li>
            </ul>
        </div>
      </div>
    </nav>
  </header>
  <main>
    <section class="section section-shaped section-lg" style="min-height: calc(100vh);">
      <div class="shape shape-style-1 bg-gradient-default">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
      <div class="container pt-lg-md">
        <div class="row justify-content-center">
          <div class="col-lg-5">
            <div class="card bg-secondary shadow border-0">
              <div class="card-header bg-white">
                <div class="text-muted text-center mb-3"><small>Password Reset</small></div>
               </div>
              <div class="card-body px-lg-5 py-lg-5">
                <form action="javascript:void(0);" method="POST">
                  <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                      <div class="input-group-prepend">
                        <span class="input-group-text"><i class="ni ni-email-83"></i></span>
                      </div>
                      <input class="form-control" placeholder="Email" type="email" id="email">
                    </div>
                  </div>
                  
				  <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
				  <div class="form-group mb-3">
                    <div class="input-group-alternative">
                        <div id="embed-captcha"></div>
                    </div>
                  </div>
                  <?php }?>
                  <?php if ($_smarty_tpl->tpl_vars['recaptcha_sitekey']->value != null) {?>
                  <div class="form-group mb-3">
                    <div class="input-group-alternative">
                        <div align="center" class="g-recaptcha" data-sitekey="<?php echo $_smarty_tpl->tpl_vars['recaptcha_sitekey']->value;?>
"></div>
                    </div>
                  </div>
                  <?php }?>
				  <?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
                  <div class="form-group mb-3">
                    <div class="input-group-alternative">
                        <div align="center" class="g-recaptcha" data-sitekey="<?php echo $_smarty_tpl->tpl_vars['recaptcha_sitekey']->value;?>
"></div>
						<div id="CaptchaDX"></div>
                    </div>
                  </div>
                  <?php }?>
                  <div class="form-group mb-3">
                   <span class="mb-0 font-sm"><i class="fa fa-exclamation-triangle fa-lg"></i>&nbsp;请填写你注册时候的邮箱.</span>
                  </div>
                  <div class="text-center">
                    <button type="button" class="btn btn-primary my-4" id="reset">Send</button>
                  </div>
                </form>
				
              </div>
            </div>

          </div>
        </div>
      </div>
    </section>
  </main>
  <!-- Footer -->
<?php $_smarty_tpl->_subTemplateRender('file:footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
  <?php if (isset($_smarty_tpl->tpl_vars['geetest_html']->value)) {?>
    <?php echo '<script'; ?>
 src="//static.geetest.com/static/tools/gt.js"><?php echo '</script'; ?>
>
    <?php echo '<script'; ?>
>
        var handlerEmbed = function (captchaObj) {
            // 将验证码加到id为captcha的元素里

            captchaObj.onSuccess(function () {
                validate = captchaObj.getValidate();
            });

            captchaObj.appendTo("#embed-captcha");

            captcha = captchaObj;
            // 更多接口参考：http://www.geetest.com/install/sections/idx-client-sdk.html
        };

        initGeetest({
            gt: "<?php echo $_smarty_tpl->tpl_vars['geetest_html']->value->gt;?>
",
            challenge: "<?php echo $_smarty_tpl->tpl_vars['geetest_html']->value->challenge;?>
",
			width: '100%',
            product: "embed", // 产品形式，包括：float，embed，popup。注意只对PC版验证码有效
            offline: <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value->success) {?>0<?php } else { ?>1<?php }?> // 表示用户后台检测极验服务器是否宕机，与SDK配合，用户一般不需要关注
        }, handlerEmbed);
    <?php echo '</script'; ?>
>
  <?php }
}
if ($_smarty_tpl->tpl_vars['recaptcha_sitekey']->value != null) {?>
    <?php echo '<script'; ?>
 src="https://recaptcha.net/recaptcha/api.js" async defer><?php echo '</script'; ?>
>
<?php }
if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
	<?php echo '<script'; ?>
 src="https://cdn.dingxiang-inc.com/ctu-group/captcha-ui/index.js"><?php echo '</script'; ?>
>
<?php }
echo '<script'; ?>
>
   function validateEmail(value){
    var pattern = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
	if (pattern.test(value)) {
      return true; 
    }else{
      return false;
    }
  }   
    function validateEmail(value){
    var pattern = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
	if (pattern.test(value)) {
      return true; 
    }else{
      return false;
    }
  }   
<?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
var appId = '<?php echo $_smarty_tpl->tpl_vars['config']->value["CaptchaDX_AppId"];?>
';
 var myCaptcha = _dx.Captcha(document.getElementById('CaptchaDX'), {
   appId: appId,
	type: 'basic', 
   style: 'oneclick',
   width: '310',
 });
var token = "";
myCaptcha.on('verifySuccess', function(security_code){
	  if (security_code != null || security_code != 'undefined' || security_code != ''){
	     token = security_code;  //security_code.split(':',1);
	  }
});
<?php }?>
$("#reset").on('click', function(){
		/*邮箱检测*/
		if(!validateEmail($("#email").val())) {
  　　　　　swal('Oops...', "邮箱不合法,请检查后输入",'error');
            return false;
        }
        var email_arr = $("#email").val().split('@');
        var email_blacklist = ["qq.com","sina.com", "163.com","sina.cn", "gmail.com", "live.com", "163.com", "139.com", "outlook.com", "189.cn", "foxmail.com", "vip.qq.com", "hotmail.com", "126.com", "aliyun.com", "yeah.net", "sohu.com", "live.jp", "msn.com", "icloud.com"];
        if ($.inArray(email_arr[1], email_blacklist) == "-1") {
          swal('Oops...', "暂不支持此邮箱，请更换如QQ、谷歌、新浪、网易等常见邮箱。",'error');
          return false;
        } 
		<?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
		if (typeof validate == 'undefined') {
			swal('Oops...',"请滑动验证码来完成验证",'warning');
			return;
		}
		if (!validate) {
			swal('Oops...',"请滑动验证码来完成验证",'warning');
			return;
		}
        <?php }?>
        <?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
        if (token == null || token == '' || token == 'undifined'){
           swal('Oops...',"请滑动验证码来完成验证",'warning');
		   return;
        }
        <?php }?>
        swal({
			title: "正在发送，请稍候...", 
			button: false,
        });
		document.getElementById("reset").disabled = true;
		
        $.ajax({
          type:"POST",
          url:"/password/reset",
          dataType:"json",
          data:{
            email: $("#email").val(),
			<?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
			token: token,
			<?php }?>
			<?php if ($_smarty_tpl->tpl_vars['recaptcha_sitekey']->value != null) {?>
            recaptcha: grecaptcha.getResponse(),
            <?php }?>
            <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
            geetest_challenge: validate.geetest_challenge,
            geetest_validate: validate.geetest_validate,
            geetest_seccode: validate.geetest_seccode
			<?php }?>
          },
          success:function(data){
            if(data.ret == 1){
              swal({
                title: data.msg,
				type: "success"
              });
              // window.setTimeout("location.href='/auth/login'", 2000);
            }else{
			   swal('Oops...',data.msg,'error');
			   	document.getElementById("reset").disabled = false;
                <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
                 captcha.reset();
                <?php }?>
				<?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
				 myCaptcha.reload();
				<?php }?>
            }
          },
          error:function(jqXHR){
			swal('Oops...',"发生错误"+jqXHR.status,'error');
			document.getElementById("reset").disabled = false;
            <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
             captcha.reset();
            <?php }?>
			<?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
		     myCaptcha.reload();
			<?php }?>
          }
        });

})
<?php echo '</script'; ?>
><?php }
}
