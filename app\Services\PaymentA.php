<?php
/**
 * Created by PhpStorm.
 * User: tonyzou
 * Date: 2018/9/24
 * Time: 下午7:07
 */

namespace App\Services;

use App\Services\Config;
use App\Services\Gateway\{
    Codepay, TomatoPay, AopF2F, Flyfoxpay, IDtPay, Mycode
};

class PaymentA
{
    public static function getClient(){
        $method = Config::get("payment_system_A");
        switch($method){
            case("codepay"):
                return new Codepay();
            case("mycode"):
                return new Mycode();    
            case("tomatopay"):
                return new TomatoPay();
            case("f2fpay"):
                return new AopF2F();
            case("flyfoxpay"):
                return new Flyfoxpay();
			case ('idtpay'):
                return new IDtPay();
            default:
                return NULL;
        }
    }

    public static function notify($request, $response, $args){
        return self::getClient()->notify($request, $response, $args);
    }

    public static function returnHTML($request, $response, $args){
        return self::getClient()->getReturnHTML($request, $response, $args);
    }

	public static function purchaseHTML(){
		if (self::getClient() != NULL) {
			return self::getClient()->getPurchaseHTML();
		} else {
			return '';
		}
    }
    public static function mycodeHTML(){
		if (Config::get("payment_system_A") == "mycode"){
			if (self::getClient() != NULL) {
				return self::getClient()->XwPublic($request, $response, $args);
			} else {
				return '';
			}
    	}
	}
	public static function idtpayHTML(){
		if (Config::get("payment_system_A") == "idtpay"){
			if (self::getClient() != NULL) {
				return self::getClient()->XwPublic($request, $response, $args);
			} else {
				return '';
			}
    	}
	}
    public static function getStatus($request, $response, $args){
        return self::getClient()->getStatus($request, $response, $args);
    }

    public static function purchase($request, $response, $args){
        return self::getClient()->purchase($request, $response, $args);
    }
}