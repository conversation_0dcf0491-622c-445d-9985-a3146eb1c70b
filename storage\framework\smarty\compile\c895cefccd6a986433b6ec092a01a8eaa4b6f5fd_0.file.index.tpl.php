<?php
/* Smarty version 3.1.33, created on 2022-07-17 18:25:22
  from '/www/wwwroot/www.shadowingy.com/resources/views/czssr/doc/index.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62d3e3929d4841_01121895',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'c895cefccd6a986433b6ec092a01a8eaa4b6f5fd' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.com/resources/views/czssr/doc/index.tpl',
      1 => 1575413154,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_62d3e3929d4841_01121895 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE html>
<html>
<head>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta charset="UTF-8">
  <title><?php echo $_smarty_tpl->tpl_vars['appName']->value;?>
</title>
  <link rel="stylesheet" href="/theme/czssr/assets/doc/css/vue.css">
</head>
<body>
  <nav>
    <ul>
    <li><a href="/">回到主页</a></li>
      <li><a href="/user/">用户中心</a>
        <ul>
          <li><a href="/user/edit">资料编辑</a></li>
          <li><a href="/user/node">节点中心</a></li>
          <li><a href="/user/code">充值捐赠</a></li>
          <li><a href="/user/shop">套餐购买</a></li>
        </ul>
      </li>
    </ul>
  </nav>
  <div id="docs">加载中...</div>
  <?php echo '<script'; ?>
>
    const root = window.location.host;
    var test = window.location.href;
	var vars = test.split("/");
	var varss = vars[3].split("?");
	

    window.$docsify = {
      name: '<?php echo $_smarty_tpl->tpl_vars['appName']->value;?>
',
      alias: {
            '/.*/_sidebar.md': '/_sidebar.md'
      },
      basePath: '<?php echo $_smarty_tpl->tpl_vars['basePath']->value;?>
',
      auto2top: true,
      loadSidebar: true,
      autoHeader: true,
      homepage: 'index.md',
      nameLink: '/doc/',
      el: '#docs',
      copyCode: {
          buttonText : '点击拷贝',
          errorText  : '拷贝失败',
          successText: '拷贝成功'
      },
      
    
      plugins: [
        function(hook, vm) {
          hook.beforeEach((markdown) => {
            const result1 = markdown.replace(/\/sublink\?type=(\w+)/g, `//${root}/sublink?type=$1`);
            return result1;
          });
          hook.beforeEach((markdown) => {
            const result2 = markdown.replace(/\/getsoft\?type=(\w+)/g, `//${root}/getsoft?type=$1`);
            return result2;
          });
        },
      ]
      
     
    }
    
  <?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
 src="/theme/czssr/assets/doc/js/docsify.min.js"><?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
 src="/theme/czssr/assets/doc/js/emoji.js"><?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
 src="/theme/czssr/assets/doc/js/zoom-image.js"><?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
 src="/theme/czssr/assets/doc/js/docsify-copy-code"><?php echo '</script'; ?>
>
  <?php echo '<script'; ?>
 src="/theme/czssr/assets/doc/js/prism-yaml.min.js"><?php echo '</script'; ?>
>
</body>
</html><?php }
}
