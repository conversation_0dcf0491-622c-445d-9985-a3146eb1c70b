<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Contracts\EventDispatcher;

use Psr\EventDispatcher\StoppableEventInterface;

if (interface_exists(StoppableEventInterface::class)) {
    /**
     * Event is the base class for classes containing event data.
     *
     * This class contains no event data. It is used by events that do not pass
     * state information to an event handler when an event is raised.
     *
     * You can call the method stopPropagation() to abort the execution of
     * further listeners in your event listener.
     *
     * <AUTHOR> <<EMAIL>>
     * <AUTHOR> <<EMAIL>>
     * <AUTHOR> <<EMAIL>>
     * <AUTHOR> <bsch<PERSON><PERSON>@gmail.com>
     * <AUTHOR> <<EMAIL>>
     */
    class Event implements StoppableEventInterface
    {
        private $propagationStopped = false;

        /**
         * Returns whether further event listeners should be triggered.
         */
        public function isPropagationStopped(): bool
        {
            return $this->propagationStopped;
        }

        /**
         * Stops the propagation of the event to further event listeners.
         *
         * If multiple event listeners are connected to the same event, no
         * further event listener will be triggered once any trigger calls
         * stopPropagation().
         */
        public function stopPropagation(): void
        {
            $this->propagationStopped = true;
        }
    }
} else {
    /**
     * Event is the base class for classes containing event data.
     *
     * This class contains no event data. It is used by events that do not pass
     * state information to an event handler when an event is raised.
     *
     * You can call the method stopPropagation() to abort the execution of
     * further listeners in your event listener.
     *
     * <AUTHOR> Blanco <<EMAIL>>
     * <AUTHOR> Wage <<EMAIL>>
     * <AUTHOR> Borschel <<EMAIL>>
     * <AUTHOR> Schussek <<EMAIL>>
     * <AUTHOR> Grekas <<EMAIL>>
     */
    class Event
    {
        private $propagationStopped = false;

        /**
         * Returns whether further event listeners should be triggered.
         */
        public function isPropagationStopped(): bool
        {
            return $this->propagationStopped;
        }

        /**
         * Stops the propagation of the event to further event listeners.
         *
         * If multiple event listeners are connected to the same event, no
         * further event listener will be triggered once any trigger calls
         * stopPropagation().
         */
        public function stopPropagation(): void
        {
            $this->propagationStopped = true;
        }
    }
}
