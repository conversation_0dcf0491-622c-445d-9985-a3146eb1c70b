{include file='admin/main.tpl'}


    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Check take money</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item active" aria-current="page">提现管理</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/invite" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/user" class="btn btn-sm btn-neutral">用户</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">系统中所有提现</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
					
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>显示表项: {include file='table/checkbox.tpl'}</p>
							
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
			
            <div class="card">
              <!-- Card body -->
			  <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive py-4">
				  {include file='table/table.tpl'}
					
				</div>
              </div>
            </div><!--card-->
		
        </div>
      </div><!--row-->

	  {include file='admin/footer.tpl'}
<script src="//cdn.jsdelivr.net/npm/datatables.net@1.10.19"></script>
<script src="//theme/czssr/main/js/dataTables.material.min.js"></script>
<script>
{include file='table/js_1.tpl'}

window.addEventListener('load', () => {
    table = $('#table_tickets').DataTable({
		ajax: 'ticket/ajax',
		processing: true,
		serverSide: true,
		order: [[ 1, 'desc' ]]
	});

	{include file='table/js_2.tpl'}
})
</script>