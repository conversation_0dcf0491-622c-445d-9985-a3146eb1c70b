<?php
// This file was auto-generated from sdk-root/src/data/workmailmessageflow/2019-05-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-05-01', 'endpointPrefix' => 'workmailmessageflow', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon WorkMail Message Flow', 'serviceId' => 'WorkMailMessageFlow', 'signatureVersion' => 'v4', 'uid' => 'workmailmessageflow-2019-05-01', ], 'operations' => [ 'GetRawMessageContent' => [ 'name' => 'GetRawMessageContent', 'http' => [ 'method' => 'GET', 'requestUri' => '/messages/{messageId}', ], 'input' => [ 'shape' => 'GetRawMessageContentRequest', ], 'output' => [ 'shape' => 'GetRawMessageContentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'GetRawMessageContentRequest' => [ 'type' => 'structure', 'required' => [ 'messageId', ], 'members' => [ 'messageId' => [ 'shape' => 'messageIdType', 'location' => 'uri', 'locationName' => 'messageId', ], ], ], 'GetRawMessageContentResponse' => [ 'type' => 'structure', 'required' => [ 'messageContent', ], 'members' => [ 'messageContent' => [ 'shape' => 'messageContentBlob', ], ], 'payload' => 'messageContent', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'errorMessage' => [ 'type' => 'string', ], 'messageContentBlob' => [ 'type' => 'blob', 'streaming' => true, ], 'messageIdType' => [ 'type' => 'string', 'max' => 120, 'min' => 1, 'pattern' => '[a-z0-9\\-]*', ], ],];
