{include file='admin/main.tpl'}

    <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Default</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">管理中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">首页</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/ticket" class="btn btn-sm btn-neutral">工单</a>
            </div>
          </div>
          <!-- Card stats -->
          <div class="row">
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">累计注册用户</h5>
                      <span class="h2 font-weight-bold mb-0">{$sts->getTotalUser()} <small>个用户</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-red text-white rounded-circle shadow">
                        <i class="ni ni-active-40"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/admin/user"><i class="ni ni-spaceship icon-ver"></i>&nbsp;查看用户列表</a></span>
                  </p>
                </div>
              </div>
            </div>
			<div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">历史累计收入</h5>
                      <span class="h2 font-weight-bold mb-0">{$sale_money_num} <small>CNY</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-green text-white rounded-circle shadow">
                        <i class="ni ni-money-coins"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/admin/bought"><i class="ni ni-credit-card icon-ver"></i>&nbsp;查看购买记录</a></span>
                  </p>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">当前在线人数</h5>
                      <span class="h2 font-weight-bold mb-0">{($sts->getOnlineUser(3600)-$sts->getOnlineUser(60))} <small>人</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-orange text-white rounded-circle shadow">
                        <i class="ni ni-chart-pie-35"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/admin/alive"><i class="ni ni-laptop icon-ver"></i>&nbsp;查看在线人数详情</a></span>
                  </p>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6">
              <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                  <div class="row">
                    <div class="col">
                      <h5 class="card-title text-uppercase text-muted mb-0">累计节点线路</h5>
                      <span class="h2 font-weight-bold mb-0">{$nodes_all_num} <small>条</small></span>
                    </div>
                    <div class="col-auto">
                      <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                        <i class="ni ni-chart-bar-32"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mt-3 mb-0 text-sm">
                    <span class="text-nowrap"><a href="/admin/node"><i class="ni ni-spaceship icon-ver"></i>&nbsp;查看节点列表</a></span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container-fluid mt--6">
      <div class="row">
        <div class="col-lg-6 col-md-6">
          <div class="card">
			<div class="card-header bg-transparent">
               <h4 class="mb-0">用户签到情况(总用户 {$sts->getTotalUser()}人)</h4>
            </div>
            <div class="card-body">
				<div style="height: 300px; width: 100%;">
				<canvas id="chart-doughnut1"></canvas>
				</div>
			</div>
          </div>
		  <div class="card">
			<div class="card-header bg-transparent">
               <h4 class="mb-0">用户在线情况(总用户 {$sts->getTotalUser()}人)</h4>
            </div>
            <div class="card-body">
				<div style="height: 300px; width: 100%;">
				<canvas id="chart-doughnut3"></canvas>
				</div>
			</div>
          </div>
		</div>  
		
        <div class="col-lg-6 col-md-6">
          <div class="card">
			<div class="card-header bg-transparent">
               <h4 class="mb-0">节点在线情况(节点数 {$sts->getTotalNodes()}个)</h4>
            </div>
            <div class="card-body">
				<div style="height: 300px; width: 100%;">
				<canvas id="chart-doughnut2"></canvas>
				</div>
			</div>
          </div>
		  <!--Traffic-->
		  <div class="card">
		    <div class="card-header bg-transparent">
               <h4 class="mb-0">流量使用情况(总分配流量 {$sts->getTotalTraffic()})</h4>
            </div>
            <div class="card-body">
				<div style="height: 300px; width: 100%;">
				<canvas id="chart-doughnut4"></canvas>
				</div>
			</div>
          </div>
        </div>
		
      </div><!--row-->
	  <div class="row">
		<div class="col-lg-4 col-md-6 col-sm-6">
          <!-- Basic with card header -->
          <div class="card">
            <!-- Card header -->
            <div class="card-header">
              <!-- Title -->
              <h5 class="h3 mb-0">财务简报</h5>
            </div>
            <!-- Card image -->
            <!-- List group -->
            <!-- Card body -->
            <div class="card-body">
              <ul class="list-group list-group-flush">
              	<li class="list-group-item badge-dot mr-4"><i class="bg-success"></i>今日笔数: {$sts->getFinanceToday('count')} 笔&nbsp;<i class="bg-success"></i>昨日笔数: {$sts->getFinanceYesterday('count')} 笔</li>
              	<li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>今日流水: ￥{$sts->getFinanceToday('total')}&nbsp;<i class="bg-warning"></i>昨日流水: ￥{$sts->getFinanceYesterday('total')}</li>
              	<li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>7天笔数: {$sts->getFinanceWeek('count')} 笔&nbsp;<i class="bg-warning"></i>7天流水: ￥{$sts->getFinanceWeek('total')}</li>
                <li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>上月笔数: {$sts->getFinanceMonth('count')} 笔&nbsp;<i class="bg-warning"></i>上月流水: ￥{$sts->getFinanceMonth('total')}</li>
              </ul>
            </div>
          </div>
		</div>
		<div class="col-lg-4 col-md-6 col-sm-6">
          <!-- Basic with card header -->
          <div class="card">
            <!-- Card header -->
            <div class="card-header">
              <!-- Title -->
              <h5 class="h3 mb-0">人数简报</h5>
            </div>
            <!-- Card image -->
            <!-- List group -->
            <!-- Card body -->
            <div class="card-body">
              <ul class="list-group list-group-flush">
              	<li class="list-group-item badge-dot mr-4"><i class="bg-success"></i>今日新注册: {$sts->getUserNumToday()} 人&nbsp;<i class="bg-success"></i>昨日注册: {$sts->getUserNumYesterday()} 人</li>
              	<li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>7天注册: {$sts->getUserNumWeek()} 人&nbsp;<i class="bg-warning"></i>上月注册: {$sts->getUserNumMonth()} 人</li>
              	<li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>
                  {if $sts->getUserNumWeek() != 0}
                  7天转化: {round(($sts->getFinanceWeek('count') / $sts->getUserNumWeek() *100),2)} %&nbsp;
                  {else}
                  7天转化: 0 %&nbsp;
                  {/if}
                  <i class="bg-warning"></i>
                  {if $sts->getUserNumMonth() != 0}
                  上月转化: {round(($sts->getFinanceMonth('count') / $sts->getUserNumMonth()*100),2)} %</li>
                  {else}
                  上月转化: 0 %
                  {/if}
				<li class="list-group-item badge-dot mr-4"><i class="bg-danger"></i>7天账号过期: {$sts->getUserClassExp('expire_in')} 人&nbsp;<i class="bg-danger"></i>7天等级过期: {$sts->getUserClassExp('class_expire')} 人</li>
              </ul>
            </div>
          </div>
		</div>
		<div class="col-lg-4 col-md-6 col-sm-6">
          <!-- Basic with card header -->
          <div class="card">
            <!-- Card header -->
            <div class="card-header">
              <!-- Title -->
              <h5 class="h3 mb-0">客户付费简报</h5>
            </div>
            <!-- Card image -->
            <!-- List group -->
            <!-- Card body -->
            <div class="card-body">
              <ul class="list-group list-group-flush">
              	<li class="list-group-item badge-dot mr-4"><i class="bg-success"></i>总用户: {$sts->getTotalUser()} 人&nbsp;</li>
              	<li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>白嫖用户: {$sts->getUserPayNum('nopay')} 人&nbsp;</li>
              	<li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>付费用户: {$sts->getUserPayNum('yespay')} 人&nbsp;</li>
                <li class="list-group-item badge-dot mr-4"><i class="bg-warning"></i>付费转化: {round(($sts->getUserPayNum('yespay') / $sts->getTotalUser() *100),2)} %&nbsp;</li>
              </ul>
            </div>
          </div>
		</div>
	  </div><!--row-->
	  <div class="row">
		<div class="col">
	        <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h4 class="mb-0">流量排行榜(异常示警)</h4>
              </div>
              <!-- Card body -->
			  <div class="card-body">
				<blockquote class="blockquote mb-0">
				     {include file='table/checkbox.tpl'}
				</blockquote>
			  </div>			
              <div class="card-body">
				<div class="table-responsive">
				  {include file='table/table.tpl'}
				</div>
              </div>
            </div><!--card-->
		</div><!--Gird-->
	   </div><!--row-->
      
	  {include file='admin/footer.tpl'}
	<script src="/theme/czssr/main/js/chart.min.js"></script>
    <script src="//cdn.jsdelivr.net/npm/datatables.net@1.10.19"></script>  
    <script src="/theme/czssr/main/js/dataTables.material.min.js"></script>
<!--流量排行榜-->
<script>
 {include file='table/js_1.tpl'}
    window.addEventListener('load', () => {
       table_1 = $('#table_1').DataTable({
           order: [[1, 'asc']],
           stateSave: true,
           serverSide: true,
           ajax: {
               url: "/admin/traffic_used/ajax",
               type: "POST",
           },
           columns: [
               {literal}
               {"data": "id"},
               {"data": "email"},
               {"data": "transfer"},
               {"data": "todayTraffic"},
               {"data": "allTraffic"},
               {"data": "transfer_enable"},
			   {/literal}
            ],
			"columnDefs": [
                {
                    targets: ['_all'],
                    className: 'mdl-data-table__cell--non-numeric'
                }
            ],

            {include file='table/lang_chinese.tpl'}
        });

       {include file='table/js_3.tpl'}  
  })	   
</script>	
<!--签到情况-->	
<script>
	//
	// Charts
	//
	'use strict';
	window.chartColors = {
		red: 'rgb(255, 99, 132)',
		orange: 'rgb(255, 159, 64)',
		yellow: 'rgb(255, 205, 86)',
		green: 'rgb(75, 192, 192)',
		blue: 'rgb(54, 162, 235)',
		purple: 'rgb(153, 102, 255)',
		grey: 'rgb(201, 203, 207)'
	};

	var ctx = document.getElementById("chart-doughnut1").getContext('2d');
	var myChart = new Chart(ctx, {
		type: 'doughnut',
		data: {
		datasets: [{
			data: [
					{number_format($sts->getTodayCheckinUser()/$sts->getTotalUser()*100,2)},
					{number_format((($sts->getCheckinUser()-$sts->getTodayCheckinUser())/$sts->getTotalUser())*100,2)},
					{number_format((1-($sts->getCheckinUser()/$sts->getTotalUser()))*100,2)}
						
					
				],
			backgroundColor: [
				window.chartColors.red,
				window.chartColors.orange,
				window.chartColors.blue,
			],
			label: 'Dataset 1'
		}],
			labels: [
				"今日签到用户 {number_format($sts->getTodayCheckinUser()/$sts->getTotalUser()*100,2)}% {$sts->getTodayCheckinUser()}人",
				"曾经签到过的用户 {number_format((($sts->getCheckinUser()-$sts->getTodayCheckinUser())/$sts->getTotalUser())*100,2)}% {$sts->getCheckinUser()-$sts->getTodayCheckinUser()}人",
				"没有签到过的用户 {number_format((1-($sts->getCheckinUser()/$sts->getTotalUser()))*100,2)}% {$sts->getTotalUser()-$sts->getCheckinUser()}人"
			]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			title: {
				display: false,
				//text: '用户签到情况(总用户 {$sts->getTotalUser()}人)'
			}
		}
	});
</script>

<!--节点情况-->
<script>
	//
	// Charts
	//
	'use strict';
	window.chartColors = {
		red: 'rgb(255, 99, 132)',
		orange: 'rgb(255, 159, 64)',
		yellow: 'rgb(255, 205, 86)',
		green: 'rgb(75, 192, 192)',
		blue: 'rgb(54, 162, 235)',
		purple: 'rgb(153, 102, 255)',
		grey: 'rgb(201, 203, 207)'
	};

	var ctx = document.getElementById("chart-doughnut2").getContext('2d');
	var myChart = new Chart(ctx, {
		type: 'doughnut',
		data: {
		datasets: [{
			data: [
					{if $sts->getTotalNodes()!=0}
					{number_format((1-($sts->getAliveNodes()/$sts->getTotalNodes()))*100,2)},
					{number_format((($sts->getAliveNodes()/$sts->getTotalNodes()))*100,2)}
					{/if}
					
				],
			backgroundColor: [
				window.chartColors.red,
				//window.chartColors.orange,
				window.chartColors.blue,
			],
			label: 'Dataset 1'
		}],
			labels: [
				{if $sts->getTotalNodes() != 0}
				"离线节点 {number_format((1-($sts->getAliveNodes()/$sts->getTotalNodes()))*100,2)}% {$sts->getTotalNodes()-$sts->getAliveNodes()}个",
				"在线节点 {number_format((($sts->getAliveNodes()/$sts->getTotalNodes()))*100,2)}% {$sts->getAliveNodes()}个"
				{/if}
			]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			title: {
				display: false,
				//text: '节点在线情况(节点数 {$sts->getTotalNodes()}个)'
			}
		}
	});
</script>

<!--在线人数情况-->
<script>
	//
	// Charts
	//
	'use strict';
	window.chartColors = {
		red: 'rgb(255, 99, 132)',
		orange: 'rgb(255, 159, 64)',
		yellow: 'rgb(255, 205, 86)',
		green: 'rgb(75, 192, 192)',
		blue: 'rgb(54, 162, 235)',
		purple: 'rgb(153, 102, 255)',
		grey: 'rgb(201, 203, 207)'
	};

	var ctx = document.getElementById("chart-doughnut3").getContext('2d');
	var myChart = new Chart(ctx, {
		type: 'doughnut',
		data: {
		datasets: [{
			data: [
					{number_format((($sts->getUnusedUser()/$sts->getTotalUser()))*100,2)},
					{number_format((($sts->getTotalUser()-$sts->getOnlineUser(86400)-$sts->getUnusedUser())/$sts->getTotalUser())*100,2)},
					{number_format(($sts->getOnlineUser(86400)-$sts->getOnlineUser(3600))/$sts->getTotalUser()*100,2)},
					{number_format(($sts->getOnlineUser(3600)-$sts->getOnlineUser(60))/$sts->getTotalUser()*100,2)},
					{number_format(($sts->getOnlineUser(60))/$sts->getTotalUser()*100,2)}
					
					
				],
			backgroundColor: [
                window.chartColors.red,
                window.chartColors.orange,
                window.chartColors.yellow,
                window.chartColors.green,
                window.chartColors.blue,
            ],
			label: 'Dataset 1'
		}],
			labels: [
				"从未在线的用户 {number_format((($sts->getUnusedUser()/$sts->getTotalUser()))*100,2)}% {(($sts->getUnusedUser()))}人",
				"一天以前在线的用户 {number_format((($sts->getTotalUser()-$sts->getOnlineUser(86400)-$sts->getUnusedUser())/$sts->getTotalUser())*100,2)}% {($sts->getTotalUser()-$sts->getOnlineUser(86400)-$sts->getUnusedUser())}人",
				"一天内在线的用户 {number_format(($sts->getOnlineUser(86400)-$sts->getOnlineUser(3600))/$sts->getTotalUser()*100,2)}% {($sts->getOnlineUser(86400)-$sts->getOnlineUser(3600))}人",
				"一小时内在线的用户 {number_format(($sts->getOnlineUser(3600)-$sts->getOnlineUser(60))/$sts->getTotalUser()*100,2)}% {($sts->getOnlineUser(3600)-$sts->getOnlineUser(60))}人",
				"一分钟内在线的用户 {number_format(($sts->getOnlineUser(60))/$sts->getTotalUser()*100,2)}% {($sts->getOnlineUser(60))}人"
			]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			title: {
				display: false,
				//text: '用户在线情况(总用户 {$sts->getTotalUser()}人)'
			}
		}
	});
</script>
			
<!--流量使用情况-->
<script>
	//
	// Charts
	//
	'use strict';
	window.chartColors = {
		red: 'rgb(255, 99, 132)',
		orange: 'rgb(255, 159, 64)',
		yellow: 'rgb(255, 205, 86)',
		green: 'rgb(75, 192, 192)',
		blue: 'rgb(54, 162, 235)',
		purple: 'rgb(153, 102, 255)',
		grey: 'rgb(201, 203, 207)'
	};

	var ctx = document.getElementById("chart-doughnut4").getContext('2d');
	var myChart = new Chart(ctx, {
		type: 'doughnut',
		data: {
		datasets: [{
			data: [
					{number_format((($sts->getRawUnusedTrafficUsage()/$sts->getRawTotalTraffic()))*100,2)},
					{number_format((($sts->getRawLastTrafficUsage()/$sts->getRawTotalTraffic()))*100,2)},
					{number_format((($sts->getRawTodayTrafficUsage()/$sts->getRawTotalTraffic()))*100,2)}
					
				],
			backgroundColor: [
				window.chartColors.blue,
				window.chartColors.orange,
				window.chartColors.red,
            ],
			label: 'Dataset 1'
		}],
			labels: [
				"总剩余可用 {number_format((($sts->getRawUnusedTrafficUsage()/$sts->getRawTotalTraffic()))*100,2)}% {(($sts->getUnusedTrafficUsage()))}",
				"总过去已用 {number_format((($sts->getRawLastTrafficUsage()/$sts->getRawTotalTraffic()))*100,2)}% {(($sts->getLastTrafficUsage()))}",
				"总今日已用 {number_format((($sts->getRawTodayTrafficUsage()/$sts->getRawTotalTraffic()))*100,2)}% {(($sts->getTodayTrafficUsage()))}"
			]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			title: {
				display: false,
				//text: '流量使用情况(总分配流量 {$sts->getTotalTraffic()})'
			}
		}
	});
		
</script>
		