<?php

namespace App\Middleware;

use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Message\ResponseInterface;
use App\Services\Config;
use App\Utils\QQWry;

class Home
{
    public function __invoke(ServerRequestInterface $request, ResponseInterface $response, $next)
    {
       
       	function getip() {
          if (getenv("HTTP_CLIENT_IP") && strcasecmp(getenv("HTTP_CLIENT_IP") , "unknown")) {
              $ip = getenv("HTTP_CLIENT_IP");
          } else if (getenv("HTTP_X_FORWARDED_FOR") && strcasecmp(getenv("HTTP_X_FORWARDED_FOR") , "unknown")) {
              $ip = getenv("HTTP_X_FORWARDED_FOR");
          } else if (getenv("REMOTE_ADDR") && strcasecmp(getenv("REMOTE_ADDR") , "unknown")) {
              $ip = getenv("REMOTE_ADDR");
          } else if (isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] && strcasecmp($_SERVER['REMOTE_ADDR'], "unknown")) {
              $ip = $_SERVER['REMOTE_ADDR'];
          } else {
              $ip = "unknown";
          }
          return $ip;
	   }
       if(count(Config::get('nolocaltions')) != 0){
            empty($ip) && $ip = getip();
            if ($ip == "127.0.0.1") return "本机地址";
            $iplocation = new QQWry();
            $nolocaltions = Config::get('nolocaltions');
            $location=$iplocation->getlocation($ip);
            $result = iconv('gbk', 'utf-8//IGNORE', $location['country']);
            $success=1;
            foreach ($nolocaltions as $key=>$val){
                if (strstr($result, $val) !== false){
                    $success=0;
                    break;
                }
            }
            if($success==0){
                $newResponse = $response->withStatus(302)->withHeader('Location', '/404');
                return $newResponse;
            }
       }
       if(Config::get('stop_subUrl') == 'true'){
           $url = $_SERVER['HTTP_HOST'];
           $suburl = Config::get('subUrl');
           $jump_link = explode('/',$suburl);
           $links = $_SERVER["REQUEST_URI"];
           $link = explode('/',$links);
           if (($link[1] != "link" || $link[1] != "checkapi") && $jump_link[2] == $url) {
             $newResponse = $response->withStatus(302)->withHeader('Location', '/404');
             return $newResponse;
           }
        }
       $enablePages = array('auth', 'password', 'doc');
       if(Config::get('stop_apiUrl') == 'true'){
          $host=$_SERVER['HTTP_HOST'];
          $apiUrl=Config::get('apiUrl');
          $jump = explode('/',$apiUrl);
          $apilink=$_SERVER["REQUEST_URI"];
          $api = explode('/',$apilink);
          if ($host==$jump[2] && ($api[1] == null || in_array($api[1], $enablePages))){
             $newResponse = $response->withStatus(302)->withHeader('Location', '/404');
             return $newResponse;
          }
       }
        $response = $next($request, $response);
        return $response;
    }
}
