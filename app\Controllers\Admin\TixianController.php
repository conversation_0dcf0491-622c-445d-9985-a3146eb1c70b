<?php
namespace App\Controllers\Admin;

use App\Models\Tixian;
use voku\helper\AntiXSS;
use App\Models\User;
use App\Services\Auth;

use App\Services\Mail;
use App\Services\Config;
use App\Controllers\AdminController;
use Ozdemir\Datatables\Datatables;
use App\Utils\DatatablesHelper;

class TixianController extends AdminController
{	
	public function index($request, $response, $args)
    {
        $table_config['total_column'] = array("op" => "操作", "id" => "ID", "type" => "申请类型", "user_id" => "用户ID",
                              "ref_tx" => "提现状态", "ref_accid" => "账号类型", "ref_acc" => "提现账号", "tx_money" => "提现金额", "tx_time" => "提现时间");
        $table_config['default_show_column'] = array();
        foreach ($table_config['total_column'] as $column => $value) {
            array_push($table_config['default_show_column'], $column);
        }
        $table_config['ajax_url'] = 'tixian/ajax';
        return $this->view()->assign('table_config', $table_config)->display('admin/tixian/index.tpl');
    }
	
	 public function edit($request, $response, $args)
    {
        $id = $args['id'];
        $Tixian = Tixian::find($id);
        if ($Tixian == null) {
        }
        return $this->view()->assign('Tixian', $Tixian)->display('admin/tixian/edit.tpl');
    }	
		
	public function update($request, $response, $args)
    {
        $id = $args['id'];
        $Tixian = Tixian::find($id);
		$Tixian->ref_tx = $request->getParam('ref_tx');
        $Tixian->tx_time = date('Y-m-d H:i:s');
        $result_tx = $request->getParam('ref_tx');
        $tx_money = $request->getParam('tx_money');
        $Tixian->save();
      
        if ($result_tx == 2) {
          $adminUser = User::where("id", "=", $Tixian->user_id)->first();
         /* $subject = Config::get('appName')."-提现申请审核结果通知";
          $to = $adminUser->email;
          $text = "对不起，您的提现申请被拒绝了，金额已退回可用余额里面，详情登陆网站查询原因。" ;
          try {
              Mail::send($to, $subject, 'news/warn.tpl', [
                  "user" => $user,"text" => $text
              ], [
              ]);
          } catch (\Exception $e) {
              echo $e->getMessage();
          }*/
      
          $adminUser->ref_money += $tx_money;//返还金额到用户可用余额
          $adminUser->save();
          $rs['ret'] = 0;
          $rs['msg'] = "你已拒绝提现！";
          return $response->getBody()->write(json_encode($rs));
        } else {
          $adminUser = User::where("id", "=", $Tixian->user_id)->first();
          $subject = Config::get('appName')."-提现申请审核结果通知";
          $to = $adminUser->email;
          $text = "恭喜您，您的提现申请通过了(已支付)，请您注意查收。" ;
          try {
              Mail::send($to, $subject, 'news/warn.tpl', [
                  "user" => $user,"text" => $text
              ], [
              ]);
          } catch (\Exception $e) {
              echo $e->getMessage();
          }

          $rs['ret'] = 1;
          $rs['msg'] = "通过审核成功";
          return $response->getBody()->write(json_encode($rs));
        }
    }

	public function ajax($request, $response, $args)
    {
        $datatables = new Datatables(new DatatablesHelper());
        $datatables->query('Select id as op,id,type,user_id,ref_tx,ref_accid,ref_acc,tx_money,tx_time from tixian');

        $datatables->edit('op', function ($data) {
            return '<a class="btn btn-brand" '.($data['ref_tx'] != 0 ? "disabled" : 'id="row_delete_'.$data['id'].'" href="/admin/tixian/'.$data['id'].'/edit" ').'>审核</a>';
        });
       
        $datatables->edit('type', function ($data) {
            return $data['type'] == 1 ? '转入' : '提现';
        });
        $datatables->edit('ref_tx', function ($data) {
            switch ($data['ref_tx']) {
              case 0:
                return "申请中";

              case 1:
                return "已通过";

              case 2:
                return "已拒绝";
            }
        });
        $datatables->edit('ref_accid', function ($data) {
            switch ($data['ref_accid']) {
              case 0:
                return "本站";

              case 1:
                return "支付宝";

              case 2:
                return "微信";
            }
        });


        $body = $response->getBody();
        $body->write($datatables->generate());
    }
}

			