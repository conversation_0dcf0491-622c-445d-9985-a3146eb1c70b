<?php
/* Smarty version 3.1.33, created on 2022-02-28 19:51:19
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/staff.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_621cb737dcda21_37890544',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '16bc7c4e252dd368a71e2c2a1a5e88a72366f62c' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/staff.tpl',
      1 => 1584941942,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_621cb737dcda21_37890544 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE HTML>
<html>
	<head>
		<title><?php echo $_smarty_tpl->tpl_vars['config']->value["appName"];?>
</title>
        <meta name="keywords" content=""/>
        <meta name="description" content=""/>
        <meta charset="utf-8" />
        <link rel="shortcut icon" href="/favicon.ico"/>
        <link rel="bookmark" href="/favicon.ico"/>
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
		<link rel="stylesheet" href="/assets/css/main.css"/>
        <link href="/bootstrap" type="text/html" rel="stylesheet">
        <noscript><link rel="stylesheet" href="/assets/css/noscript.css" /></noscript>   
  </head>
  
       <body>
			<div id="wrapper">
              <!--首页开始-->
                          <div class="content">
							<div class="inner">
								<h1>staff</h1>
                          </div>
                        </div>
<!--
I'm glad you use this theme, the development is no so easy, I hope you can keep the copyright, I will thank you so much.
It will not impact the appearance and can give developers a lot of support :)

很高兴您使用并喜欢该主题，开发不易 十分谢谢与希望您可以保留一下版权声明。它不会影响美观并可以给开发者很大的支持和动力。 :)
-->                  
											<p>MIT许可证（MIT）</p>

											<p class="copyright">&copy;2015 orvice</p>

											<p>特此免费授予任何获得副本的人这个软件和相关的文档文件（“软件”）来处理在软件中没有限制，
                                              包括但不限于权利使用，复制，修改，合并，发布，分发，再许可和/或销售该软件的副本，
                                              并允许软件所属的人员提供这样做，但须符合以下条件：</p>

											<p>上述版权声明和本许可声明必须包含在内本软件的副本或实质性部分。</p>

											<p>本软件按“原样”提供，不附有任何形式的明示或暗示保证默示的，包括但不限于对适销性的保证，
                                              适用于特定目的和不侵权。在任何情况下，作者或版权持有者对任何索赔，损坏或其他责任均不负任何责任责任，无论是在合同，民事侵权行为或其他方面，
                                              与本软件或本软件的使用或其他交易有关或与之有关软件。</p>
									
										<h2 class="content-sub-heading">免责声明</h2>
										<p>您当前所访问的站点使用的是下面所提到的开源程序。您能看到这个页面仅代表这个站点使用了本程序，其使用过程中发生的一切问题需要其自行解决，本程序不承担任何责任。</p>


                                   <div class="table-wrapper">
										<table>

											<tbody>
												<tr>
													<td>原作</td>
													<td><a href="https://github.com/orvice/">orvice</a></td>
												</tr>
												<tr>
													<td>现阶段维护</td>
													<td><a href="https://github.com/galaxychuck">galaxyq</a>，<a href="https://github.com/dumplin233">dumplin</a>，<a href="https://github.com/mxihan">Rin SAMA</a>，<a href="https://github.com/xcxnig">Miku</a>，<a href="https://github.com/ZJY2003">Tony Zou</a>，<a href="https://github.com/laurieryayoi">Nymph</a></td>
												</tr>
												<tr>
													<td>监督</td>
													<td><a href="https://www.zhaoj.in">（曾经的）glzjin</a></td>
												</tr>
												<tr>
													<td>作画</td>
													<td><a href="https://github.com/Daemonite/">Daemonite</a>,<a href="https://www.zhaoj.in">（曾经的）glzjin</a>     ,<a href="https://github.com/galaxychuck">galaxyq</a></td>
												</tr>
                                              <tr>
													<td>永远喜欢</td>
													<td>永远喜欢 <a href="https://t.me/Anankke">Anankke</a></td>
												</tr>
												<tr>
													<td>出演</td>
													<td><a href="https://www.zhaoj.in">（曾经的）glzjin</a>，<a href="http://mengyang.wang/">mengyang</a>，<a href="http://tony.ecy.ren/">tony</a></td>
												</tr>
											  <tr>
													<td>主题</td>
													<td><a href=""> 橘子</a></td>
												</tr>
                                                
												<tr>	
													<td>感谢两位协助的小姐姐:</td>
													<td>小月姑娘   
													<a href="https://github.com/hackxiaoya">杨二雅子(二丫)</a></td>
													
											  </tr>
                                              <tr>												
													<td>感谢GeekQu大佬的订阅系统:</td>
													<td><a href="https://github.com/GeekQu/">GeekQu</a></td>
												</tr>
                                              <tr>
													<td>鸣谢</td>
													<td>所有被引用过代码的同学，以及所有提交过 PR 的同学。当然，还有在使用这份程序的你我Ta。</td>
											  </tr>
                                               <tr>
													<td>本程序的参与人员列表</td>
													<td>此列表为参与到 <a href="https://github.com/NimaQu/"> glzjin mod UIM</a> 中的人员，在此表示感谢。</td>
												</tr>
                                              
											</tbody>
										</table>
									</div>
                                        

                           <nav>
							<ul>  
                          <a href="javascript:history.back();" class="button">返回上一页</a>
                           </ul>
						</nav>
                 
             
              <!--首页结束-->
					<div id="main">
                     
                      <!--全部标签结束-->
                              </div>
                     <!-- 版权底部 -->
                      <footer id="footer">
                   <p class="copyright">&copy;2015-2018 <?php echo $_smarty_tpl->tpl_vars['config']->value["appName"];?>
</p>
                      </footer>
              <!-- 版权结束 -->
			 </div>
                <!-- BG -->
			<div id="bg"></div>
	        	<!-- Scripts -->
			<?php echo '<script'; ?>
 src="https://cdn.jsdelivr.net/npm/jquery@1.11.3"><?php echo '</script'; ?>
>
			<?php echo '<script'; ?>
 src="https://cdn.jsdelivr.net/gh/ajlkn/skel@3.0.1/dist/skel.min.js"><?php echo '</script'; ?>
>
			<?php echo '<script'; ?>
 src="/assets/js/util.js"><?php echo '</script'; ?>
>
         <?php echo '<script'; ?>
 src="/assets/js/main.js"><?php echo '</script'; ?>
>
	</body>
</html>
<?php }
}
