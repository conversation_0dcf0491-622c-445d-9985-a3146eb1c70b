<?php
// This file was auto-generated from sdk-root/src/data/swf/2012-01-25/paginators-1.json
return [ 'pagination' => [ 'GetWorkflowExecutionHistory' => [ 'input_token' => 'nextPageToken', 'limit_key' => 'maximumPageSize', 'output_token' => 'nextPageToken', 'result_key' => 'events', ], 'ListActivityTypes' => [ 'input_token' => 'nextPageToken', 'limit_key' => 'maximumPageSize', 'output_token' => 'nextPageToken', 'result_key' => 'typeInfos', ], 'ListClosedWorkflowExecutions' => [ 'input_token' => 'nextPageToken', 'limit_key' => 'maximumPageSize', 'output_token' => 'nextPageToken', 'result_key' => 'executionInfos', ], 'ListDomains' => [ 'input_token' => 'nextPageToken', 'limit_key' => 'maximumPageSize', 'output_token' => 'nextPageToken', 'result_key' => 'domainInfos', ], 'ListOpenWorkflowExecutions' => [ 'input_token' => 'nextPageToken', 'limit_key' => 'maximumPageSize', 'output_token' => 'nextPageToken', 'result_key' => 'executionInfos', ], 'ListWorkflowTypes' => [ 'input_token' => 'nextPageToken', 'limit_key' => 'maximumPageSize', 'output_token' => 'nextPageToken', 'result_key' => 'typeInfos', ], 'PollForDecisionTask' => [ 'input_token' => 'nextPageToken', 'limit_key' => 'maximumPageSize', 'output_token' => 'nextPageToken', 'result_key' => 'events', ], ],];
