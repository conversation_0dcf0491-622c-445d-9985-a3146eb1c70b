{include file='user/main.tpl'}
<link rel="stylesheet" href="/theme/czssr/main/css/quill.core.css" type="text/css">
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Tickets</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
				  <li class="breadcrumb-item"><a href="/user/ticket">工单系统</a></li>
                  <li class="breadcrumb-item active" aria-current="page">新建工单</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/shop" class="btn btn-sm btn-neutral">商店购买</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">创建工单</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group mt-3">
							<label class="form-control-label">标题&nbsp;:&nbsp填入需要处理的事情，加QQ客服处理更快捷;</label>
							<input id="title" class="form-control form-control-sm" type="text" placeholder="请输入标题...">
						</div>
						<div class="form-group mt-3">
							<label class="form-control-label">内容(点击上传图片后会自动上传比较慢等缓存,别重复点)&nbsp;:&nbsp退款需在购买三天内可以退款80%，超过三天拒绝退款。退款在工单里填入原因和收款支付宝账号，提交后账号会被关闭。（谨慎操作）;</label>
							<div id="editor" data-toggle="quill">
                                
                            </div>
						</div>提交退款后五个工作日内您的支付宝账号会收到退款。如有疑问联系QQ客服
                      <div class="modal-footer">
						<button id="ticket_create" type="button" class="btn btn-primary">确认添加</button>
					</div>
					</div>
					
				</div>
			</div>
		
        </div>
      </div><!--row-->
      <!--check pay-->
	   <div class="modal fade" id="check_pay" tabindex="-1" role="dialog" aria-labelledby="checkModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="checkModalLabel" class="ls-2">请你确认?</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description mt-3">当前开启工单需要支付 <code>{$config["pay_ticket_price"]}</code> 元.</p>
				</div>	 
		      </div>
			  <div class="modal-footer">
                    <button id="check_payl" type="button" class="btn btn-primary">确认提交</button>
               </div>
		    </div>
		  </div>
		</div>
	{include file='dialog.tpl'}
	{include file='user/footer.tpl'}
<script src="/theme/czssr/main/js/quill.min.js"></script>
<script>
// Quill.js

'use strict';

var QuillEditor = (function() {

	// Variables
	var $quill = $('[data-toggle="quill"]');

	// Methods

	function init($this) {

		// Get placeholder
		var placeholder = $this.data('quill-placeholder');

		// Init editor
		var quill = new Quill('#editor', {
			modules: {
				toolbar: [
					['bold', 'italic'],
					['link', 'blockquote', 'code', 'image'],
					[{
						'list': 'ordered'
					}, {
						'list': 'bullet'
					}]
				]
			},
			placeholder: placeholder,
			theme: 'snow'
		});

	}
        
	// Events

	if ($quill.length) {
		$quill.each(function() {
			init($(this));
		});
	}

})();
  
</script>	  
<script>
    $(document).ready(function () {
      {if $config["pay_ticket_price"] > 0}
      	$("#ticket_create").on("click", function() {
			$('#check_pay').modal();
	    });
      {/if}
        function ticket_create() {
		
            $("#result").modal();
            $('#check_pay').modal("hide");
            $$.getElementById('msg').innerHTML = '正在提交...'
			
			var quill_html = document.querySelector('#editor').children[0].innerHTML;
			//html = '<div class="ql-container ql-snow"><div class="ql-editor">'+html+"</div></div>";
			
			
            $.ajax({
                type: "POST",
                url: "/user/ticket",
                dataType: "json",
                data: {
                    content: quill_html,
                    title: $('#title').val()
                },
                success: function(data) {
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href='/user/ticket'", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: function (jqXHR) {
                    $("#result").modal();
                    $("#msg").html(jqXHR+"  发生了错误。");
                }
            });
        }
      {if $config["pay_ticket_price"] > 0}
        $("#check_payl").click(function () {
            ticket_create();
        });
      {else}
        $("#ticket_create").click(function () {
            ticket_create();
        });
       {/if}
    })

</script>