{include file='user/main.tpl'}
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Transshipment</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">中转规则</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/ticket/create" class="btn btn-sm btn-neutral">工单</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h4 class="mb-0">中转说明</h4>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
					<a class="btn btn-primary text-white btn-sm mb-3" href="javascript:void(0)" data-toggle="modal" data-target="#infomations">更多说明</a>
					<a class="btn btn-Secondary btn-sm mb-3" href="/user/relay/create">添加中转</a>
						<blockquote class="blockquote mb-0">
							<p class="description">中转规则一般由中国中转至其他国外节点.</p>
							<p class="description">请设置端口号为您自己的端口.</p>
							<p class="description">优先级越大, 代表其在多个符合条件的规则并存时会被优先采用, 当优先级一致时, 先添加的规则会被采用.</p>
							<p class="description">节点不设置中转时, 这个节点就可以当作一个普通的节点来做代理使用.</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
		{if $is_relay_able}
            <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">规则表</h3>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive">
				 {$rules->render()}
					<table class="table align-items-center table-flush">
					<thead class="thead-light">
						<tr>
							<th>ID</th>
                            <th>起源节点</th>
                            <th>目标节点</th>
                            <th>端口</th>
                            <th>优先级</th>
                            <th>操作</th>
						</tr>
					</thead>
					<tbody class="list">
					{foreach $rules as $rule}
					<tr>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$rule->id}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{if $rule->source_node_id == 0}所有节点{else}{$rule->Source_Node()->name}{/if}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{if $rule->Dist_Node() == null}不进行中转{else}{$rule->Dist_Node()->name}{/if}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{if $rule->port == 0}所有端口{else}{$rule->port}{/if}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$rule->priority}</span>
						</td>
						<td>
							<a class="btn btn-primary btn-sm" {if $rule->user_id == 0}disabled{else}href="/user/relay/{$rule->id}/edit"{/if}>编辑</a>
							<a class="btn btn-secondary btn-sm" id="delete" value="{$rule->id}" {if $rule->user_id == 0}disabled{else}href="javascript:void(0);" onClick="delete_modal_show('{$rule->id}')"{/if}>删除</a>
						</td>
					</tr>
					{/foreach}
					</tbody>
					</table>
				  {$rules->render()}
				</div>
              </div>
            </div><!--card-->
			
          <!-- Custom form validation -->
            <div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">链路表</h3>
              </div>
              <!-- Card body -->
              <div class="card-body">
                <!-- Light table -->
				<div class="table-responsive">
					<table class="table align-items-center table-flush">
					<thead class="thead-light">
						<tr>
							<th>端口</th>
                            <th>始发节点</th>
                            <th>终点节点</th>
                            <th>途径节点</th>
                            <th>状态</th>
						</tr>
					</thead>
					<tbody class="list">
					{foreach $pathset as $path}
					<tr>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$path->port}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$path->begin_node->name}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$path->end_node->name}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$path->path}</span>
						</td>
						<td>
							<span class="badge badge-dot mr-4"><i class="bg-warning"></i>{$path->status}</span>
						</td>
					</tr>
					{/foreach}
					</tbody>
					</table>
				</div>
              </div>
            </div><!--card-->
		{/if}
        </div>
      </div><!--row-->
		<!-- coupon  Modal -->
		<div class="modal fade" id="delete_modal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="deleteModalLabel">系统通知</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
		         <p class="description">
					<span class="mb-3">请您是否确认删除?</span>
				 </p> 
		      </div>
		      <div class="modal-footer">
		        <button id="delete_input" type="button" class="btn btn-primary" data-dismiss="modal">确认删除</button>
		      </div>
			  <div class="modal-footer">
		        <button type="button" class="btn btn-Secondary" data-dismiss="modal">Close</button>
		      </div>
		    </div>
		  </div>
		</div>
		<!-- info modal -->
		<div class="modal fade" id="infomations" tabindex="-1" role="dialog" aria-labelledby="infomationsModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="infomationsModalLabel">中转说明</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<p class="description">(1)&nbsp;什么是中转?&nbsp;&nbsp;答:&nbsp;中转是用一个中间服务器作为跳板连接另外一个代理服务器.</p>
				<p class="description">(2)&nbsp;为什么要使用中转?&nbsp;&nbsp;答:&nbsp;某些情况下中转可以改善延时和丢包, 特别对于游戏用户. 举个栗子, 如果直连阿里云香港有些时候你发现网络状况不佳, 你可以使用阿里云上海节点作为中转, 一般直连国内节点状况会好很多, 然后由阿里云上海中转到阿里云香港由于是阿里内网, 所以网络状况也会不错, 这样你的总体连接状况就得到了改善.</p>
				<p class="description">(3)&nbsp;中转后流量如何计算?&nbsp;&nbsp;答:&nbsp;由于同时使用了两个节点(中转节点和目标节点), 所以流量同时统计两个节点的流量, 因此中转适合游戏用户, 流量用的少.</p>
				<p class="description">(4)&nbsp;设置中转后连接哪个节点?&nbsp;&nbsp;答:&nbsp;连接中转节点(起源节点).</p>
		      </div>
			  <div class="modal-footer">
                    <button type="button" class="btn btn-Secondary" data-dismiss="modal">Close</button>
               </div>
		    </div>
		  </div>
		</div>
	  {include file='dialog.tpl'}
	  {include file='user/footer.tpl'}
	  
<script>

    function delete_modal_show(id) {
        deleteid = id;
        $("#delete_modal").modal();
    }

    $(document).ready(() => {

        {if !$is_relay_able}
        $("#result").modal();
        $('#msg').innerHTML = "为了中转的稳定, 您需要在<a href='/user/edit'>资料编辑</a>处设置协议为以下协议之一: <br>{foreach $relay_able_protocol_list as $single_text}{$single_text}<br>{/foreach}后方可设置中转规则!"
        {/if}

        function delete_id() {
            $.ajax({
                type: "DELETE",
                url: "/user/relay",
                dataType: "json",
                data: {
                    id: deleteid
                },
                success: function(data) {
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href=window.location.href", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: function (jqXHR) {
                    $("#result").modal();
                    $("#msg").html(jqXHR+"  发生了错误。");
                }
            });
        }

        $("#delete_input").click(() => {
            delete_id();
        });
    })
</script>
	