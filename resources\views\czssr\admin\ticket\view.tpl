{include file='admin/main.tpl'}
<link rel="stylesheet" href="/theme/czssr/main/css/quill.core.css" type="text/css">
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Check Ticket</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/ticket">工单系统</a></li>
                  <li class="breadcrumb-item active" aria-current="page">查看工单</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/node" class="btn btn-sm btn-neutral">节点</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">编辑工单 #{$ticket->id}</h3>
              </div>
              <!-- Card body -->
				<div class="bg-gradient-Secondary">
					<div class="card-body">
						<div class="form-group mt-3">
							<label class="form-control-label">内容(点击上传图片后会自动上传比较慢等缓存,别重复点)&nbsp;:&nbsp;</label>
							<div id="editor" data-toggle="quill">
			                    
							</div>
						</div>
                      <div class="modal-footer">
						<button id="submit" type="button" class="btn btn-primary">添加</button>
						<button id="close" type="button" class="btn btn-Secondary">添加并关闭</button>
						<button id="close_directly" type="button" class="btn btn-Secondary">直接关闭</button>
						<button  id="changetouser" class="btn btn-Secondary" onClick="changetouser_modal_show()">切换为该用户</button>
						
					  </div>
					</div>
					<div class="card-body">
					{$ticketset->render()}
						<div class="accordion" id="accordionExample">
						{foreach $ticketset as $ticket}
							<div class="card">
								<div class="card-header" id="heading" data-toggle="collapse" data-target="#collapse{$ticket->id}" aria-expanded="false" aria-controls="collapseOne">
								  {if $ticketset_nums+1 == $ticket->id}
                                  <h5 class="mb-0 badge-dot mr-4"><i class="bg-success"></i> {$ticket->datetime()} # {$ticket->User()->user_name}</h5>
                                  {else}
                                  <h5 class="mb-0"> {$ticket->datetime()} # {$ticket->User()->user_name}</h5>
                                  {/if}
								</div>
								<div id="collapse{$ticket->id}" class="collapse" aria-labelledby="heading" data-parent="#accordionExample">
									<div class="card-body col-md-auto">
									    <div v-html="content">{$ticket->content}</div>
									</div>
								</div>
							</div>
						{/foreach}
						</div>
					{$ticketset->render()}
					</div>
				</div>
				
			</div>
        </div>
      </div><!--row-->

		<div class="modal fade" id="changetouser_modal" tabindex="-1" role="dialog" aria-labelledby="ChangetouserModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="ChangetouserModalLabel" class="text-danger">确认要切换为该用户?</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
				<!-- Card body -->
				<div class="card-body">
					<p class="description badge-dot mr-4"><i class="bg-warning"></i>切换为该用户以后, 你随时可以通过菜单底部的「返回管理员身份」按钮返回本条工单.</p>
				</div>	 
		      </div>
			    <div class="modal-footer">
                    <button id="changetouser_input" type="button" class="btn btn-primary">确认</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
		    </div>
		  </div>
		</div>
	{include file='dialog.tpl'}
	{include file='admin/footer.tpl'}

<script src="/theme/czssr/main/js/quill.min.js"></script>	  

<script>
// Quill.js

'use strict';

var QuillEditor = (function() {

	// Variables
	var $quill = $('[data-toggle="quill"]');

	// Methods

	function init($this) {

		// Get placeholder
		var placeholder = $this.data('quill-placeholder');

		// Init editor
		var quill = new Quill('#editor', {
			modules: {
				toolbar: [
					['bold', 'italic'],
					['link', 'blockquote', 'code', 'image'],
					[{
						'list': 'ordered'
					}, {
						'list': 'bullet'
					}]
				]
			},
			placeholder: placeholder,
			theme: 'snow'
		});

	}
        
	// Events

	if ($quill.length) {
		$quill.each(function() {
			init($(this));
		});
	}

})();
  
</script> 
<script>
    function changetouser_modal_show() {
        $("#changetouser_modal").modal();
    }
    $(document).ready(function () {
        function submit() {
		
            $("#result").modal();
            $('#msg').html('正在提交...');
			
			var quill_html = document.querySelector('#editor').children[0].innerHTML;
           // var quill_markdown = document.querySelector('#editor').children[0].innerText;
			//html = '<div class="ql-container ql-snow"><div class="ql-editor">'+html+"</div></div>";
			
           $.ajax({
                type: "PUT",
                url: "/admin/ticket/{$id}",
                dataType: "json",
                data: {
                    content: quill_html,
                    status
                },
                success: data => {
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
                    $('#msg').html("发生错误："+jqXHR.status);
                }
            });
        }
        $("#submit").click(function () {
            status = 1;
            submit();
        });
		$("#close").click(function () {
            status = 0;
            submit();
        });
		$("#close_directly").click(function () {
            status = 0;
            $("#result").modal();
            $('#msg').html("正在提交...");
            $.ajax({
                type: "PUT",
                url: "/admin/ticket/{$id}",
                dataType: "json",
                data: {
                    content: '这条工单已被关闭',
                    status
                },
                success: data => {
                    if (data.ret) {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                        $("#msg").html(data.msg);
                    }
                },
                error: jqXHR => {
                    $("#result").modal();
					$('#msg').html("发生错误："+jqXHR.status);
                }
            });
        });
		function changetouser_id(){
			$.ajax({
				type:"POST",
				url:"/admin/user/changetouser",
				dataType:"json",
				data:{
					userid: {$ticket->User()->id},
					adminid: {$user->id},
					local: '/admin/ticket/' + {$ticket->id} + '/view'
				},
				success: data =>{
					if (data.ret) {
						$("#result").modal();
						$("#msg").html(data.msg);
						window.setTimeout("location.href='/user'", {$config['jump_delay']});
					} else {
						$("#result").modal();
						$("#msg").html(data.msg);
					}
				},
				error: jqXHR => {
					$("#result").modal();
					$('#msg').html("发生错误："+jqXHR.status);
				}
			});
		}
		$("#changetouser_input").click(function () {
			changetouser_id();
		});
    })

</script>