## 应用概述
## [点击在线观看android视频教程](https://cdn.jsdelivr.net/gh/lxiaoya1003/tuchuang@master/androidssr99.mp4)

<video width="300" height="150" controls="controls">
<source src="https://cdn.jsdelivr.net/gh/lxiaoya1003/tuchuang@master/androidssr99.mp4" type="video/mp4" />
</video>

ShadowsocksD 是在 Android 平台上的客户端软件，支持 Shadowsocks 协议。

## 应用下载

以下是各平台该应用的下载地址。

- Android：[ShadowsocksD](https://github.com/CGDF-Github/SSD-Android/releases)
- Android：[V2ray-plugin-Android](https://github.com/shadowsocks/v2ray-plugin-android/releases)
- Android：[Simple-obfs-Android](https://github.com/shadowsocks/simple-obfs-android/releases)
- ...

*请注意，为了使用的便捷，请务必安装上面 3 个 APP。*

## 获取订阅

此处将显示您的订阅链接，请注意为登录状态：

[cinwell website](/sublink?type=ssd ':include :type=markdown')

!> 这个 **订阅链接** 非常重要，你应当把它当做密码一样妥善保管。

## 配置 ShadowsocksD

打开 ShadowsocksD 点击左上角的加号图标，选择 **添加订阅**，在弹出的输入框中粘贴上方 **[获取订阅](#获取订阅)** 中的订阅链接并点击 **确定**。

![1](https://i.loli.net/2019/02/14/5c6452cd7fbe3.png ':size=600')

待订阅更新完毕之后，点击如下图该处图标，玩下滑动到 **路由** 的位置，将其更改为 **绕过局域网及中国大陆地址**，随后点击右上角的 **√** 进行保存。

![2](https://i.loli.net/2019/02/14/5c645598b1bc0.png ':size=600')

## 开始使用

点击选择您需要的节点，随后点击下方的 小飞机图标 连接即可。

如操作系统提示添加 VPN 配置，请点击 运行 并验证您的 密码、指纹等。
