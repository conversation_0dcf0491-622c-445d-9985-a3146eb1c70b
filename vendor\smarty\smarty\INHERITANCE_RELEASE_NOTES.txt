3.1.3"
New tags for inheritance parent and chilD
{parent}  == {$smarty.block.parent}
{child}  == {$smarty.block.child}
Both tags support the assign attribute like
{child assign=foo}

3.1.31
New tags for inheritance parent and child
{block_parent}  == {$smarty.block.parent}
{block_child}  == {$smarty.block.child}

Since 3.1.28 you can mix inheritance by extends resource with the {extends} tag.
A template called by extends resource can extend a subtemplate or chain buy the {extends} tag.
Since 3.1.31 this feature can be turned off by setting the new Smarty property Smarty::$extends_recursion to false.

3.1.28
Starting with version 3.1.28 template inheritance is no longer a compile time process.
All {block} tag parent/child relations are resolved at run time.
This does resolve all known existing restrictions (see below).

The $smarty::$inheritance_merge_compiled_includes property has been removed.
Any access to it is ignored.

New features:

Any code outside root {block} tags in child templates is now executed but any output will be ignored.

   {extends 'foo.tpl'}
   {$bar = 'on'}           // assigns variable $bar seen in parent templates
   {block 'buh'}{/block}

   {extends 'foo.tpl'}
   {$bar}           // the output of variable bar is ignored
   {block 'buh'}{/block}

{block} tags can be dynamically en/disabled by conditions.

   {block 'root'}
      {if $foo}
         {block 'v1'}
             ....
         {/block}
      {else}
         {block 'v1'}
             ....
         {/block}
      {/if}
   {/block}

{block} tags can have variable names.

   {block $foo}
        ....
   {/block}

Starting with 3.1.28 you can mix inheritance by extends resource with the {extends} tag.
A template called by extends resource can extend a subtemplate or chain buy the {extends} tag.

NOTE There is a BC break. If you used the extends resource {extends} tags have been ignored.

THE FOLLOWING RESTRICTIONS ARE NO LONGER EXISTING:
In Smarty 3.1 template inheritance is a compile time process. All the extending of {block} tags
is done at compile time and the parent and child templates are compiled in a single compiled template.
{include} subtemplate could also {block} tags. Such subtemplate could not compiled by it's own because
it could be used in other context where the {block} extended with a different result. For that reasion
the compiled code of {include} subtemplates gets also merged in compiled inheritance template.

Merging the code into a single compile template has some drawbacks.
1. You could not use variable file names in {include} Smarty would use the {include} of compilation time.
2. You could not use individual compile_id in {include}
3. Separate caching of subtemplate was not possible
4. Any change of the template directory structure between calls was not necessarily seen.

Starting with 3.1.15 some of the above conditions got checked and resulted in an exception. It turned out
that a couple of users did use some of above and now got exceptions.

To resolve this starting with 3.1.16 there is a new configuration parameter $inheritance_merge_compiled_includes.
For most backward compatibility its default setting is true.
With this setting all {include} subtemplate will be merge into the compiled inheritance template, but the above cases
could be rejected by exception.


If $smarty->inheritance_merge_compiled_includes = false; {include} subtemplate will not be merged.You must now manually merge all {include} subtemplate which do contain {block} tags. This is done by setting the "inline" option.
{include file='foo.bar' inline}

1. In case of a variable file name like {include file=$foo inline} you must use the variable in a compile_id  $smarty->compile_id = $foo;
2. If you use individual compile_id in {include file='foo.tpl' compile_id=$bar inline} it must be used in the   global compile_id as well  $smarty->compile_id = $bar;
3. If call templates with different template_dir configurations and a parent could same named child template from different folders
   you must make the folder name part of the compile_id.

