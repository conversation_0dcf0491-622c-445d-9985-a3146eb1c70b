<?php
/* Smarty version 3.1.33, created on 2022-03-23 21:44:17
  from '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/auth/register.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_623b24316c7614_26608860',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '9b078dcc277663215605b10fccfd941a17dfda72' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.cf/resources/views/czssr/auth/register.tpl',
      1 => 1648043036,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.tpl' => 1,
    'file:reg_tos.tpl' => 1,
    'file:footer.tpl' => 1,
  ),
),false)) {
function content_623b24316c7614_26608860 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:header.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<body>
<!-- 以下代码变黑白色 -->
<!--<style type="text/css">
html {
filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
-webkit-filter: grayscale(100%);}
</style>  -->
<!-- 以上代码变黑白色 -->
  <header class="header-global">
    <nav id="navbar-main" class="navbar navbar-main navbar-expand-lg navbar-transparent navbar-light headroom">
      <div class="container">
        <a class="navbar-brand mr-lg-5" href="/">
          <img src="/theme/czssr/main/picture/white.png" alt="brand">
          <span class="engname1"> <?php echo $_smarty_tpl->tpl_vars['config']->value['engname'];?>
</span>
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar_global" aria-controls="navbar_global" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button><!--菜单button-->
        <div class="navbar-collapse collapse" id="navbar_global">
          <div class="navbar-collapse-header">
            <div class="row">
              <div class="col-6 collapse-brand">
                <a href="/">
                  <img src="/theme/czssr/main/picture/blue.png" alt="brand">
                  <span class="engname3"> <?php echo $_smarty_tpl->tpl_vars['config']->value['engname'];?>
</span>
                </a>
              </div>
              <div class="col-6 collapse-close"><!--关闭button-->
                <button type="button" class="navbar-toggler" data-toggle="collapse" data-target="#navbar_global" aria-controls="navbar_global" aria-expanded="false" aria-label="Toggle navigation">
                  <span></span>
                  <span></span>
                </button>
              </div>
            </div>
          </div>
          <ul class="navbar-nav navbar-nav-hover align-items-lg-center">
				<li class="nav-item ">
				<?php if ($_smarty_tpl->tpl_vars['user']->value->isLogin) {?>
				<a href="/user" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-sign-in"></i>
					<span class="nav-link-inner--text">My Account</span>
				</a>
				<?php } else { ?>
				<a href="/auth/login" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-sign-in"></i>
					<span class="nav-link-inner--text">Login</span>
				</a>
				<?php }?>
				</li>
				<li class="nav-item ">
				<?php if ($_smarty_tpl->tpl_vars['config']->value['register_mode'] != 'close') {?>
				<a href="/auth/register" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-user-plus"></i>
					<span class="nav-link-inner--text">Sign Up</span>
				</a>
				<?php } else { ?>
				<a href="javascript:void(0);" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-user-plus"></i>
					<span class="nav-link-inner--text">Stop register</span>
				</a>
				<?php }?>
				</li>
				<li class="nav-item ">
				<a href="/password/reset" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-repeat"></i>
					<span class="nav-link-inner--text">Reset Password</span>
				</a>
				</li>
				<li class="nav-item ">
				<a href="/doc" class="nav-link" data-toggle="" role="button">
					<i class="fa fa-question-circle"></i>
					<span class="nav-link-inner--text">Support</span>
				</a>
				</li>
           </ul>
            <ul class="navbar-nav align-items-lg-center ml-lg-auto">
				<li class="nav-item">
				<!--<a class="nav-link nav-link-icon" href="#" target="_blank" data-toggle="tooltip" title="Star us on Telegram">
					<i class="fa fa-telegram"></i>
					<span class="nav-link-inner--text d-lg-none">Telegram</span>
				</a>-->
				
				</li>
            </ul>
        </div>
      </div>
    </nav>
  </header>
  <main>
    <section class="section section-shaped section-lg" style="min-height: calc(100vh);">
      <div class="shape shape-style-1 bg-gradient-default">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
      <div class="container pt-lg-md">
        <div class="row justify-content-center">
          <div class="col-lg-5">
            <div class="card bg-secondary shadow border-0">
			
              <div class="card-header bg-white">
                <div class="text-muted text-center "><small>Sign up with</small></div>
              <?php if ($_smarty_tpl->tpl_vars['config']->value['enable_qq'] == 'true') {?>
                <div class="btn-wrapper text-center">
                  <a href="<?php echo $_smarty_tpl->tpl_vars['login_url']->value;?>
" class="btn btn-neutral btn-icon" id="qqlogin">
                    <span class="btn-inner--icon">
                      <img alt="image" src="/theme/czssr/main/picture/qq.svg">
                    </span>
                    <span class="">Tencent</span>
                  </a>
                </div>
              <?php }?>
              </div>
			  
              <div class="card-body px-lg-5 py-lg-5">
			  <?php if ($_smarty_tpl->tpl_vars['config']->value['register_mode'] != 'close') {?>
                <form action="javascript:void(0);" method="POST">
				  <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
						<div class="input-group-prepend">
							<span class="input-group-text"><i class="ni ni-hat-3"></i></span>
						</div>
                    <input class="form-control" placeholder="昵称" type="text" id="name">
                  </div>
                 </div>
                  <div class="form-group">
                    <div class="input-group input-group-alternative">
                      <div class="input-group-prepend">
                        <span class="input-group-text"><i class="ni ni-email-83"></i></span>
                      </div>
                      <input class="form-control" placeholder="邮箱" type="email" id="email">
                    </div>
                  </div>
				  <?php if ($_smarty_tpl->tpl_vars['config']->value['enable_email_verify'] == 'true') {?>
				  <div class="form-group">
				    <div class="input-group input-group-alternative">
					  <div class="input-group-prepend">
                        <span class="input-group-text"><i class="ni ni-email-83"></i></span>
                      </div>
					  <input class="form-control" placeholder="验证码" type="text" id="email_code">
					  <button id="email_verify" class="btn btn-info" style="width:30%;float:right; margin-bottom:0px; z-index:99; padding: 10px 5px" type="button">GET Code</button>
                    </div>
                  </div>					
					<?php }?>
				   <?php if ($_smarty_tpl->tpl_vars['config']->value['register_mode'] == 'invite') {?>
				   <div class="form-group">
                    <div class="input-group input-group-alternative">
                      <div class="input-group-prepend">
                        <span class="input-group-text"><i class="ni ni-lock-circle-open"></i></span>
                      </div>
                      <input class="form-control" placeholder="仅开放邀请码注册" type="text" id="code">
                    </div>
                  </div>
				   <?php }?>
                  <div class="form-group">
                    <div class="input-group input-group-alternative">
                      <div class="input-group-prepend">
                        <span class="input-group-text"><i class="ni ni-lock-circle-open"></i></span>
                      </div>
                      <input class="form-control" placeholder="密码(至少8位)" type="password" id="passwd">
                    </div>
                  </div>
				  <div class="form-group">
                    <div class="input-group input-group-alternative">
                      <div class="input-group-prepend">
                        <span class="input-group-text"><i class="ni ni-lock-circle-open"></i></span>
                      </div>
                      <input class="form-control" placeholder="重复密码" type="password" id="repasswd">
                    </div>
                  </div>
				  <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
				  <div class="form-group">
                    <div class="input-group-alternative">
                        <div id="embed-captcha"></div>
                    </div>
                  </div>
                  <?php }?>
                  <?php if ($_smarty_tpl->tpl_vars['recaptcha_sitekey']->value != null) {?>
                  <div class="form-group">
                    <div class="input-group-alternative">
                        <div align="center" class="g-recaptcha" data-sitekey="<?php echo $_smarty_tpl->tpl_vars['recaptcha_sitekey']->value;?>
"></div>
                    </div>
                  </div>
                  <?php }?>
				  <?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
                  <div class="form-group mb-3">
                    <div class="input-group-alternative">
                        <div align="center" class="g-recaptcha" data-sitekey="<?php echo $_smarty_tpl->tpl_vars['recaptcha_sitekey']->value;?>
"></div>
						<div id="CaptchaDX"></div>
                    </div>
                  </div>
                  <?php }?>
                  <div class="custom-control custom-control-alternative custom-checkbox">
					<input class="custom-control-input" id="reg_tos" type="checkbox">
					<label class="custom-control-label" for="reg_tos">
						<span>同意注册,并已阅读<a href="#" id="reg_tos" data-toggle="modal" data-target="#modal-default">服务协议</a></span>
					</label>
				  </div>
                  <div class="text-center">
					<button type="button" class="btn btn-primary my-4" id="register">Create Account</button>
				  </div>
                </form>
			<?php } else { ?>
			    <div class="py-3 text-center">
                    <i class="ni ni-bell-55 ni-3x"></i>
                    <h5 class="heading mt-4">Stop Register</h5>
					<p id="telegram-alert">本站站暂停注册账号，有疑问请联系管理员。</p>
                </div>
			    <div class="text-center">
					<a href="/" class="btn btn-primary my-4">Back Home</a>
				</div>
			<?php }?>
				<div class="modal fade" id="modal-default" tabindex="-1" role="dialog" aria-labelledby="modal-default" aria-hidden="true">
					<div class="modal-dialog modal- modal-dialog-centered modal-" role="document">
						<div class="modal-content">
							<div class="modal-header">
							<h6 class="modal-title" id="modal-title-default">服务协议.</h6>
							<button type="button" class="close" data-dismiss="modal" aria-label="Close">
								<span aria-hidden="true" style="font-size:1em">×</span>
							</button>
							</div>
							<div class="modal-body">
								<div class="py-3 text-left">
									<?php $_smarty_tpl->_subTemplateRender('file:reg_tos.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
								</div>
							</div>
						</div>
					</div>
				</div>
				
              </div>
            </div>

          </div>
        </div>
      </div>
    </section>
 </main>
  <!-- Footer -->
<?php $_smarty_tpl->_subTemplateRender('file:footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


<?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
  <?php if (isset($_smarty_tpl->tpl_vars['geetest_html']->value)) {?>
    <?php echo '<script'; ?>
 src="//static.geetest.com/static/tools/gt.js"><?php echo '</script'; ?>
>
    <?php echo '<script'; ?>
>
        var handlerEmbed = function (captchaObj) {
            // 将验证码加到id为captcha的元素里

            captchaObj.onSuccess(function () {
                validate = captchaObj.getValidate();
            });

            captchaObj.appendTo("#embed-captcha");

            captcha = captchaObj;
            // 更多接口参考：http://www.geetest.com/install/sections/idx-client-sdk.html
        };

        initGeetest({
            gt: "<?php echo $_smarty_tpl->tpl_vars['geetest_html']->value->gt;?>
",
            challenge: "<?php echo $_smarty_tpl->tpl_vars['geetest_html']->value->challenge;?>
",
            product: "embed", // 产品形式，包括：float，embed，popup。注意只对PC版验证码有效
            offline: <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value->success) {?>0<?php } else { ?>1<?php }?> // 表示用户后台检测极验服务器是否宕机，与SDK配合，用户一般不需要关注
        }, handlerEmbed);
    <?php echo '</script'; ?>
>
  <?php }
}
if ($_smarty_tpl->tpl_vars['recaptcha_sitekey']->value != null) {?>
    <?php echo '<script'; ?>
 src="https://recaptcha.net/recaptcha/api.js" async defer><?php echo '</script'; ?>
>
<?php }
if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
	<?php echo '<script'; ?>
 src="https://cdn.dingxiang-inc.com/ctu-group/captcha-ui/index.js"><?php echo '</script'; ?>
>
<?php }?>
  <!--新注册部分-->
<?php echo '<script'; ?>
>
  function checkByteLength(str,minlen,maxlen) {
    if (str == null) return false;
    //为空返回false
    var l = str.length;
    var blen = 0;
    for(i=0; i<l; i++) {
      //循环取得检验值的长度
      if ((str.charCodeAt(i) & 0xff00) != 0) {
        blen ++;
      }
      blen ++;
    }
    if (blen > maxlen || blen < minlen) {
      //判断长度是否合法
      return false;
    }
    return true;
  }


  function validateUsername(value){
    var patn = /^[a-zA-Z]+[a-zA-Z0-9]+$/; 
    if(!checkByteLength(value,4,16)) return false;

    var pattern = /^[A-Za-z0-9\u4e00-\u9fa5]+$/gi;
	if (pattern.test(value)) {
      return true; 
    }else{
      return false;
    }
    
  }
   function validateEmail(value){
    var pattern = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
	if (pattern.test(value)) {
      return true; 
    }else{
      return false;
    }
  }   
$(document).ready(function(){
<?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
var appId = '<?php echo $_smarty_tpl->tpl_vars['config']->value["CaptchaDX_AppId"];?>
';
 var myCaptcha = _dx.Captcha(document.getElementById('CaptchaDX'), {
   appId: appId,
	type: 'basic', 
   style: 'oneclick',
   width: '310',
 });
var token = "";
myCaptcha.on('verifySuccess', function(security_code){
	  if (security_code != null || security_code != 'undefined' || security_code != ''){
	     token = security_code;  //security_code.split(':',1);
	  }
});
<?php }?>
     function register(){
			code = $("#code").val();
    	<?php if ($_smarty_tpl->tpl_vars['config']->value['register_mode'] == 'invite') {?>
				code = code;
           if ((getCookie('code'))!=''){
				code = getCookie('code');
           }
           if(code==''){
             swal('Oops...',"请输入你的邀请码",'warning');
             return;
           }
        <?php } else { ?>
           if ((getCookie('code'))!=''){
				code = getCookie('code');
           }
	    <?php }?>
		<?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
          if (typeof validate == 'undefined') {
            	swal('Oops...',"请滑动验证码来完成验证",'warning');
            	return;
          }
          if (!validate) {
             swal('Oops...',"请滑动验证码来完成验证",'warning');
             return;
          }
        <?php }?>
		<?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
        if (token == null || token == '' || token == 'undifined'){
           swal('Oops...',"请滑动验证码来完成验证",'warning');
		   return;
        }
        <?php }?>
		  if (document.getElementById('reg_tos').checked) {
		   	var reg_tos = 1;
		   } else {
		   	var reg_tos = 0;
		   	swal('Oops...','请阅读并同意服务协议','warning');
		   	return;
		   }
            
          if(!validateUsername($("#name").val())) {
  　　　　　swal('Oops...', "用户名不合法,仅支持4~16位字母数字或中文",'warning');
            return false;
          }
		/*邮箱检测*/
		if(!validateEmail($("#email").val())) {
  　　　　　swal('Oops...', "邮箱不合法,请检查后输入",'error');
            return false;
        }

            var email_arr = $("#email").val().split('@');
            var email_blacklist = ["qq.com","sina.com", "163.com","sina.cn", "gmail.com", "live.com", "163.com", "139.com", "outlook.com", "189.cn", "foxmail.com", "vip.qq.com", "hotmail.com", "126.com", "aliyun.com", "yeah.net", "sohu.com", "live.jp", "msn.com", "icloud.com"];
            if ($.inArray(email_arr[1], email_blacklist) == "-1") {
              swal('Oops...', "暂不支持此邮箱，请更换如QQ、谷歌、新浪、网易等常见邮箱。",'error');
              return false;
            }  

            $.ajax({
                type:"POST",
                url:"/auth/register",
                dataType:"json",
                data:{
				    name: $("#name").val(),
                    email: $("#email").val(),
                    passwd: $("#passwd").val(),
                    repasswd: $("#repasswd").val(),
				//	wechat: "wechat",
				//	imtype: "imtype",
				<?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
			         token: token,
			    <?php }?>
				<?php if ($_smarty_tpl->tpl_vars['recaptcha_sitekey']->value != null) {?>
                    recaptcha: grecaptcha.getResponse(),
                <?php }?>
				    code: code<?php if ($_smarty_tpl->tpl_vars['config']->value['enable_email_verify'] == 'true') {?>,
					emailcode: $("#email_code").val()<?php }
if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>,
                    geetest_challenge: validate.geetest_challenge,
                    geetest_validate: validate.geetest_validate,
                    geetest_seccode: validate.geetest_seccode
                        <?php }?>
					},
                success:function(data){
                    if(data.ret == 1){
                        swal({
                          title: data.msg, 
                          text: "注册成功！欢迎使用<?php echo $_smarty_tpl->tpl_vars['config']->value["appName"];?>
", 
                          type:"success"
                        });
                       document.getElementById("register").disabled = true;
                       window.setTimeout("location.href='/auth/login'", <?php echo $_smarty_tpl->tpl_vars['config']->value['jump_delay'];?>
);
                      
                    }else{
                    	swal('Oops...',data.msg,'error');
                        document.getElementById("register").disabled = false;
                        setCookie('code', '', 0);
                        $("#code").val(getCookie('code'));
                        <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
                        captcha.reset();
                        <?php }?>
					   <?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
						myCaptcha.reload();
				       <?php }?>
                    }
                },
                error:function(jqXHR){
					swal('Oops...',"发生错误："+jqXHR.status,'error');
                    document.getElementById("register").disabled = false;
                    <?php if ($_smarty_tpl->tpl_vars['geetest_html']->value != null) {?>
                    captcha.reset();
                    <?php }?>
					<?php if ($_smarty_tpl->tpl_vars['CaptchaDX_AppId']->value != null) {?>
					myCaptcha.reload();
				    <?php }?>
                }
            });
        }
        $("html").keydown(function(event){
            if(event.keyCode==13){
                register();
            }
        });
		
		$("#register").click(function(){
            register();
        });
    })
<?php echo '</script'; ?>
>
<?php if ($_smarty_tpl->tpl_vars['enable_email_verify']->value == 'true') {
echo '<script'; ?>
>
var wait=60;
function time(o) {
		if (wait == 0) {
			o.removeAttr("disabled");

			o.text("获取验证码");
			wait = 60;
		} else {

			o.attr("disabled","disabled");
			o.text("重新发送(" + wait + ")");
			wait--;
			setTimeout(function() {
				time(o)
			},
			1000)
		}
	}
    $(document).ready(function () {
      
        $("#email_verify").click(function () {
            if(!validateUsername($("#name").val())) {
  　　　　　	swal('Oops...', "用户名不合法,仅支持4~16位英文字母和数字",'error');
              return false;
            }
          /*邮箱检测*/
		if(!validateEmail($("#email").val())) {
  　　　　　swal('Oops...', "邮箱不合法,请检查后输入",'error');
            return false;
        }
          /* 邮箱验证 */
        var email_arr = $("#email").val().split('@');
            var email_blacklist = ["qq.com","sina.com", "163.com","sina.cn", "gmail.com", "live.com", "163.com", "139.com", "outlook.com", "189.cn", "foxmail.com", "vip.qq.com", "hotmail.com", "126.com", "aliyun.com", "yeah.net", "sohu.com", "live.jp", "msn.com", "icloud.com"];
            if ($.inArray(email_arr[1], email_blacklist) == "-1") {
              swal('Oops...', "暂不支持此邮箱，请更换如QQ、谷歌、新浪、网易等常见邮箱。",'error');
              return false;
            }  
			
			time($("#email_verify"));

            $.ajax({
                type: "POST",
                url: "send",
                dataType: "json",
                data: {
                    email: $("#email").val()
                },
                success: function (data) {
                    if (data.ret) {
					  swal("发送成功", data.msg, 'success');
                    } else {
						swal('Oops...', data.msg,'error');
                    }
                },
                error: function (jqXHR) {
				   swal('Oops...出现了一些错误', data.msg,'error');
                }
            });
        });
    })
<?php echo '</script'; ?>
>
<?php }
echo '<script'; ?>
>
		function getQueryVariable(variable)
	{
	       var query = window.location.search.substring(1);
	       var vars = query.split("&");
	       for (var i=0;i<vars.length;i++) {
	            	var pair = vars[i].split("=");
	            	if(pair[0] == variable){
	            		return pair[1];
	            	}
	       }
	       return "";
	}

		function setCookie(cname,cvalue,exdays)
	{
	  var d = new Date();
	  d.setTime(d.getTime()+(exdays*24*60*60*1000));
	  var expires = "expires="+d.toGMTString();
	  document.cookie = cname + "=" + cvalue + "; " + expires;
	}

		function getCookie(cname)
	{
	  var name = cname + "=";
	  var ca = document.cookie.split(';');
	  for(var i=0; i<ca.length; i++) 
	  {
	    var c = ca[i].trim();
	    if (c.indexOf(name)==0) return c.substring(name.length,c.length);
	  }
	  return "";
	}

		if (getQueryVariable('code')!=''){
		setCookie('code',getQueryVariable('code'),30);
		window.location.href='/auth/register'; 
	}

    <?php if ($_smarty_tpl->tpl_vars['config']->value['register_mode'] == 'invite') {?>
		if ((getCookie('code'))!=''){
		$("#code").val(getCookie('code'));
	}
	<?php }
echo '</script'; ?>
><?php }
}
