<?php
/* Smarty version 3.1.33, created on 2022-07-17 17:54:46
  from '/www/wwwroot/www.shadowingy.com/resources/views/czssr/user/node.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62d3dc6655bd37_82942453',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '25b1d5e684acf0bb721b91f30adcadac91838e0a' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.com/resources/views/czssr/user/node.tpl',
      1 => 1657083683,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:user/main.tpl' => 1,
    'file:user/footer.tpl' => 1,
  ),
),false)) {
function content_62d3dc6655bd37_82942453 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->smarty->ext->_tplFunction->registerTplFunctions($_smarty_tpl, array (
  'displayV2rayNode' => 
  array (
    'compiled_filepath' => '/www/wwwroot/www.shadowingy.com/storage/framework/smarty/compile/25b1d5e684acf0bb721b91f30adcadac91838e0a_0.file.node.tpl.php',
    'uid' => '25b1d5e684acf0bb721b91f30adcadac91838e0a',
    'call_name' => 'smarty_template_function_displayV2rayNode_18844018762d3dc663c6996_09424328',
  ),
));
?> <?php $_smarty_tpl->_subTemplateRender('file:user/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
 <style>
   .doudong:hover { transform: translateY(-2px);}
</style>
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Nodes</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">节点列表</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/code" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 充值</a>
              <a href="/user/shop" class="btn btn-sm btn-neutral">商店</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header --> 

	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
    <div class="row">
        <div class="col">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">节点使用说明</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						<blockquote class="blockquote mb-0">
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>用户等级：<?php echo $_smarty_tpl->tpl_vars['levels']->value;?>
</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>对应的节点使用<code>权限</code>根据你的用户等级和用户群组来决定的.</p>
							<p class="description badge-dot mr-4"><i class="bg-warning"></i>SH开头-上海电信, SD开头-山东移动, GD开头-广东, 没有代表直连</p>
						<p>
							<i class="ni ni-card ni-lg icon-ver"></i>当前套餐：<font color="#399AF2" size="3"><?php echo $_smarty_tpl->tpl_vars['level']->value->name;?>
</font> 套餐&nbsp;&nbsp;&nbsp;<a class="btn btn-success btn-sm" href="/user/shop" >点此升级</a>
						</p>
							<footer class="blockquote-footer text-danger">Someone famous in <cite title="Source Title">Source Title</cite></footer>
						</blockquote>
					</div>
				</div>
			</div>
										
        <?php if ($_smarty_tpl->tpl_vars['config']->value['show_free_nodes'] == "true") {?>
		<div class="card">
          <!-- Card header -->
          <div class="card-header">
            <h3 class="mb-0">新注册用户赠送100M流量，购买请点左边我的商店选择合适套餐购买使用。</h3>
          </div>
          <!-- Card body -->
			<div class="card-body bg-gradient-Secondary">
				<div class="row">
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['nodes']->value, 'node');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['node']->value) {
?>
				<?php if ($_smarty_tpl->tpl_vars['node']->value['class'] == 0) {?>
				  <div class="col-12 col-sm-12 col-lg-6">
					<div class="doudong">
						<div class="card card-stats">
							<div class="card-body">
							<div class="row align-items-center">
								<div class="col-auto">
								<!-- Avatar -->
										<div class="avatar avatar-xl rounded-circle">
											<img src="/images/prefix/<?php echo $_smarty_tpl->tpl_vars['node']->value['flag'];?>
" >
										</div>
								</div>
								<?php if ($_smarty_tpl->tpl_vars['node']->value['mu_only'] != 1 && ($_smarty_tpl->tpl_vars['node']->value['sort'] != 11 || $_smarty_tpl->tpl_vars['node']->value['sort'] != 12)) {?>
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" onclick="urlChange('<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
',0,<?php if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {
echo $_smarty_tpl->tpl_vars['relay_rule']->value->id;
} else { ?>0<?php }?>)">
			
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
										<!--<span class="bg-gradient-danger text-white">在线</span> 这个是离线danger-->
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];?>

									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>0%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
							<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['sort'] == 11) {?>
							    <div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" data-toggle="modal" data-target="#node-modal-<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
">
						
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];?>
 <!-- - V2ray-->
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>0%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
							<?php } elseif (($_smarty_tpl->tpl_vars['node']->value['sort'] == 0 || $_smarty_tpl->tpl_vars['node']->value['sort'] == 10) && $_smarty_tpl->tpl_vars['node']->value['mu_only'] != -1) {?>
							    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['nodes_muport']->value, 'single_muport');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['single_muport']->value) {
?>
								    <?php $_smarty_tpl->_assignInScope('relay_rule', null);?>
								<?php if ($_smarty_tpl->tpl_vars['node']->value['sort'] == 10 && $_smarty_tpl->tpl_vars['single_muport']->value['user']['is_multi_user'] != 2) {?>
									<?php $_smarty_tpl->_assignInScope('relay_rule', $_smarty_tpl->tpl_vars['tools']->value->pick_out_relay_rule($_smarty_tpl->tpl_vars['node']->value['id'],$_smarty_tpl->tpl_vars['single_muport']->value['server']->server,$_smarty_tpl->tpl_vars['relay_rules']->value));?>
								<?php }?>
								
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" onclick="urlChange('<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
',<?php echo $_smarty_tpl->tpl_vars['single_muport']->value['server']->server;?>
,<?php if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {
echo $_smarty_tpl->tpl_vars['relay_rule']->value->id;
} else { ?>0<?php }?>)">
			
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];
if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {?> - <?php echo $_smarty_tpl->tpl_vars['relay_rule']->value->dist_node()->name;
}?><!-- - 单端口<?php echo $_smarty_tpl->tpl_vars['single_muport']->value['server']->server;?>
 -->
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>0%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
								<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
							<?php }?>
							</div>
							</div>
						</div>
					</div>
				  </div>
				<?php }?>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>	
				</div>
			</div>
	    </div><!--card-->
	    <?php }?>
		
		
		<div class="card">
          <!-- Card header -->
          <div class="card-header">
		  <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['levelList']->value, 'level');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['level']->value) {
?>
			<?php if ($_smarty_tpl->tpl_vars['level']->value->level == 1) {?>
            <h3 class="mb-0"><?php echo $_smarty_tpl->tpl_vars['level']->value->name;?>
节点列表 - VIP1 | [中转|普匿][上网|刷推|油管]</h3>
			<?php }?>
		  <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
          </div>
          <!-- Card body -->
			<div class="card-body bg-gradient-Secondary">
				<div class="row">
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['nodes']->value, 'node');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['node']->value) {
?>
				<?php if ($_smarty_tpl->tpl_vars['node']->value['class'] == 1) {?>
				  <div class="col-12 col-sm-12 col-lg-6">
					<div class="doudong">
						<div class="card card-stats">
							<div class="card-body">
							<div class="row align-items-center">
								<div class="col-auto">
								<!-- Avatar -->
										<div class="avatar avatar-xl rounded-circle">
											<img src="/images/prefix/<?php echo $_smarty_tpl->tpl_vars['node']->value['flag'];?>
" >
										</div>
								</div>
								<?php if ($_smarty_tpl->tpl_vars['node']->value['mu_only'] != 1 && ($_smarty_tpl->tpl_vars['node']->value['sort'] != 11 || $_smarty_tpl->tpl_vars['node']->value['sort'] != 12)) {?>
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" <?php if ($_smarty_tpl->tpl_vars['user']->value->class >= 1) {?>onclick="urlChange('<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
',0,<?php if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {
echo $_smarty_tpl->tpl_vars['relay_rule']->value->id;
} else { ?>0<?php }?>)" <?php } else { ?>data-toggle="modal" data-target="#node-modal-ban" <?php }?>>
			
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<!--<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span>--> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];?>

									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>0%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
							<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['sort'] == 11) {?>
							    <div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" <?php if ($_smarty_tpl->tpl_vars['user']->value->class >= 1) {?> data-toggle="modal" data-target="#node-modal-<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
" <?php } else { ?> data-toggle="modal" data-target="#node-modal-ban" <?php }?>>
						
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span><!--<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span>--> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];?>
 <!-- - V2ray-->
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>6%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
							<?php } elseif (($_smarty_tpl->tpl_vars['node']->value['sort'] == 0 || $_smarty_tpl->tpl_vars['node']->value['sort'] == 10) && $_smarty_tpl->tpl_vars['node']->value['mu_only'] != -1) {?>
							    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['nodes_muport']->value, 'single_muport');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['single_muport']->value) {
?>
								    <?php $_smarty_tpl->_assignInScope('relay_rule', null);?>
								<?php if ($_smarty_tpl->tpl_vars['node']->value['sort'] == 10 && $_smarty_tpl->tpl_vars['single_muport']->value['user']['is_multi_user'] != 2) {?>
									<?php $_smarty_tpl->_assignInScope('relay_rule', $_smarty_tpl->tpl_vars['tools']->value->pick_out_relay_rule($_smarty_tpl->tpl_vars['node']->value['id'],$_smarty_tpl->tpl_vars['single_muport']->value['server']->server,$_smarty_tpl->tpl_vars['relay_rules']->value));?>
								<?php }?>
								
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" <?php if ($_smarty_tpl->tpl_vars['user']->value->class >= 1) {?>onclick="urlChange('<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
',<?php echo $_smarty_tpl->tpl_vars['single_muport']->value['server']->server;?>
,<?php if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {
echo $_smarty_tpl->tpl_vars['relay_rule']->value->id;
} else { ?>0<?php }?>)" <?php } else { ?> data-toggle="modal" data-target="#node-modal-ban" <?php }?>>
			
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span><!--<span class="bg-gradient-green text-white">在线--></span> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];
if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {?> - <?php echo $_smarty_tpl->tpl_vars['relay_rule']->value->dist_node()->name;
}?><!-- - 单端口<?php echo $_smarty_tpl->tpl_vars['single_muport']->value['server']->server;?>
 -->
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>8%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
								<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
							<?php }?>
							</div>
							</div>
						</div>
					</div>
				  </div>
				<?php }?>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>	
				</div>
			</div>
	    </div><!--card-->
		
		<div class="card">
          <!-- Card header -->
          <div class="card-header">
		  <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['levelList']->value, 'level');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['level']->value) {
?>
			<?php if ($_smarty_tpl->tpl_vars['level']->value->level == 2) {?>
            <h3 class="mb-0"><?php echo $_smarty_tpl->tpl_vars['level']->value->name;?>
节点列表 - VIP2 | [中转|隧道|高匿][游戏|影音|养号]</h3>
			<?php }?>
		  <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
          </div>
          <!-- Card body -->
			<div class="card-body bg-gradient-Secondary">
				<div class="row">
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['nodes']->value, 'node');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['node']->value) {
?>
				<?php if ($_smarty_tpl->tpl_vars['node']->value['class'] == 2) {?>
				  <div class="col-12 col-sm-12 col-lg-6">
					<div class="doudong">
						<div class="card card-stats">
							<div class="card-body">
							<div class="row align-items-center">
								<div class="col-auto">
								<!-- Avatar -->
										<div class="avatar avatar-xl rounded-circle">
											<img src="/images/prefix/<?php echo $_smarty_tpl->tpl_vars['node']->value['flag'];?>
" >
										</div>
								</div>
							<?php if ($_smarty_tpl->tpl_vars['node']->value['mu_only'] != 1 && ($_smarty_tpl->tpl_vars['node']->value['sort'] != 11 || $_smarty_tpl->tpl_vars['node']->value['sort'] != 12)) {?>
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" <?php if ($_smarty_tpl->tpl_vars['user']->value->class >= 2) {?>onclick="urlChange('<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
',0,<?php if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {
echo $_smarty_tpl->tpl_vars['relay_rule']->value->id;
} else { ?>0<?php }?>)" <?php } else { ?> data-toggle="modal" data-target="#node-modal-ban" <?php }?>>
			
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];?>

									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>11%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
							<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['sort'] == 11) {?>
							    <div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" <?php if ($_smarty_tpl->tpl_vars['user']->value->class >= 2) {?> data-toggle="modal" data-target="#node-modal-<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
" <?php } else { ?> data-toggle="modal" data-target="#node-modal-ban" <?php }?>>
						
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span><!--<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span>--> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];?>
 <!-- - V2ray-->
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>7%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
							<?php } elseif (($_smarty_tpl->tpl_vars['node']->value['sort'] == 0 || $_smarty_tpl->tpl_vars['node']->value['sort'] == 10) && $_smarty_tpl->tpl_vars['node']->value['mu_only'] != -1) {?>
							    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['nodes_muport']->value, 'single_muport');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['single_muport']->value) {
?>
								    <?php $_smarty_tpl->_assignInScope('relay_rule', null);?>
								<?php if ($_smarty_tpl->tpl_vars['node']->value['sort'] == 10 && $_smarty_tpl->tpl_vars['single_muport']->value['user']['is_multi_user'] != 2) {?>
									<?php $_smarty_tpl->_assignInScope('relay_rule', $_smarty_tpl->tpl_vars['tools']->value->pick_out_relay_rule($_smarty_tpl->tpl_vars['node']->value['id'],$_smarty_tpl->tpl_vars['single_muport']->value['server']->server,$_smarty_tpl->tpl_vars['relay_rules']->value));?>
								<?php }?>
								
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" <?php if ($_smarty_tpl->tpl_vars['user']->value->class >= 2) {?>onclick="urlChange('<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
',<?php echo $_smarty_tpl->tpl_vars['single_muport']->value['server']->server;?>
,<?php if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {
echo $_smarty_tpl->tpl_vars['relay_rule']->value->id;
} else { ?>0<?php }?>)" <?php } else { ?> data-toggle="modal" data-target="#node-modal-ban" <?php }?>>
			
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span><!--<span class="bg-gradient-green text-white">在线--></span> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];
if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {?> - <?php echo $_smarty_tpl->tpl_vars['relay_rule']->value->dist_node()->name;
}?><!-- - 单端口<?php echo $_smarty_tpl->tpl_vars['single_muport']->value['server']->server;?>
 -->
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>11%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
								<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
							<?php }?>
							</div>
							</div>
						</div>
					</div>
				  </div>
				<?php }?>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>	
				</div>
			</div>
	    </div><!--card-->
	
	    <div class="card">
          <!-- Card header -->
          <div class="card-header">
		  <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['levelList']->value, 'level');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['level']->value) {
?>
			<?php if ($_smarty_tpl->tpl_vars['level']->value->level == 3) {?>
            <h3 class="mb-0"><?php echo $_smarty_tpl->tpl_vars['level']->value->name;?>
节点列表 - VIP3 | [直连|隧道|高匿][中转|影音|养号]</h3>
			<?php }?>
		  <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
          </div>
          <!-- Card body -->
			<div class="card-body bg-gradient-Secondary">
				<div class="row">
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['nodes']->value, 'node');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['node']->value) {
?>
				<?php if ($_smarty_tpl->tpl_vars['node']->value['class'] == 3) {?>
				  <div class="col-12 col-sm-12 col-lg-6">
					<div class="doudong">
						<div class="card card-stats">
							<div class="card-body">
							<div class="row align-items-center">
								<div class="col-auto">
								<!-- Avatar -->
										<div class="avatar avatar-xl rounded-circle">
											<img src="/images/prefix/<?php echo $_smarty_tpl->tpl_vars['node']->value['flag'];?>
" >
										</div>
								</div>
							<?php if ($_smarty_tpl->tpl_vars['node']->value['mu_only'] != 1 && ($_smarty_tpl->tpl_vars['node']->value['sort'] != 11 || $_smarty_tpl->tpl_vars['node']->value['sort'] != 12)) {?>
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" <?php if ($_smarty_tpl->tpl_vars['user']->value->class >= 3) {?>onclick="urlChange('<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
',0,<?php if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {
echo $_smarty_tpl->tpl_vars['relay_rule']->value->id;
} else { ?>0<?php }?>)" <?php } else { ?> data-toggle="modal" data-target="#node-modal-ban" <?php }?>>
			
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];?>

									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
							<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['sort'] == 11) {?>
							    <div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" <?php if ($_smarty_tpl->tpl_vars['user']->value->class >= 3) {?> data-toggle="modal" data-target="#node-modal-<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
" <?php } else { ?> data-toggle="modal" data-target="#node-modal-ban" <?php }?>>
						
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span><!--<span class="bg-gradient-green text-white">在线--></span> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];?>
 <!-- - V2ray-->
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>12%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
							<?php } elseif (($_smarty_tpl->tpl_vars['node']->value['sort'] == 0 || $_smarty_tpl->tpl_vars['node']->value['sort'] == 10) && $_smarty_tpl->tpl_vars['node']->value['mu_only'] != -1) {?>
							    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['nodes_muport']->value, 'single_muport');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['single_muport']->value) {
?>
								    <?php $_smarty_tpl->_assignInScope('relay_rule', null);?>
								<?php if ($_smarty_tpl->tpl_vars['node']->value['sort'] == 10 && $_smarty_tpl->tpl_vars['single_muport']->value['user']['is_multi_user'] != 2) {?>
									<?php $_smarty_tpl->_assignInScope('relay_rule', $_smarty_tpl->tpl_vars['tools']->value->pick_out_relay_rule($_smarty_tpl->tpl_vars['node']->value['id'],$_smarty_tpl->tpl_vars['single_muport']->value['server']->server,$_smarty_tpl->tpl_vars['relay_rules']->value));?>
								<?php }?>
								
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" <?php if ($_smarty_tpl->tpl_vars['user']->value->class >= 3) {?>onclick="urlChange('<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
',<?php echo $_smarty_tpl->tpl_vars['single_muport']->value['server']->server;?>
,<?php if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {
echo $_smarty_tpl->tpl_vars['relay_rule']->value->id;
} else { ?>0<?php }?>)" <?php } else { ?> data-toggle="modal" data-target="#node-modal-ban" <?php }?>>
			
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span><!--<span class="bg-gradient-green text-white">在线--></span> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];
if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {?> - <?php echo $_smarty_tpl->tpl_vars['relay_rule']->value->dist_node()->name;
}?><!-- - 单端口<?php echo $_smarty_tpl->tpl_vars['single_muport']->value['server']->server;?>
 -->
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>5%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
								<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
							<?php }?>
							</div>
							</div>
						</div>
					</div>
				  </div>
				<?php }?>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>	
				</div>
			</div>
	    </div><!--card-->
	    
	    <div class="card">
          <!-- Card header -->
          <div class="card-header">
		  <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['levelList']->value, 'level');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['level']->value) {
?>
			<?php if ($_smarty_tpl->tpl_vars['level']->value->level == 4) {?>
            <h3 class="mb-0"><?php echo $_smarty_tpl->tpl_vars['level']->value->name;?>
节点列表 - VIP4 | [回国|隧道|高匿][中转|奈非|游戏]</h3>
			<?php }?>
		  <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
          </div>
          <!-- Card body -->
			<div class="card-body bg-gradient-Secondary">
				<div class="row">
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['nodes']->value, 'node');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['node']->value) {
?>
				<?php if ($_smarty_tpl->tpl_vars['node']->value['class'] == 4) {?>
				  <div class="col-12 col-sm-12 col-lg-6">
					<div class="doudong">
						<div class="card card-stats">
							<div class="card-body">
							<div class="row align-items-center">
								<div class="col-auto">
								<!-- Avatar -->
										<div class="avatar avatar-xl rounded-circle">
											<img src="/images/prefix/<?php echo $_smarty_tpl->tpl_vars['node']->value['flag'];?>
" >
										</div>
								</div>
							<?php if ($_smarty_tpl->tpl_vars['node']->value['mu_only'] != 1 && ($_smarty_tpl->tpl_vars['node']->value['sort'] != 11 || $_smarty_tpl->tpl_vars['node']->value['sort'] != 12)) {?>
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" <?php if ($_smarty_tpl->tpl_vars['user']->value->class >= 3) {?>onclick="urlChange('<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
',0,<?php if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {
echo $_smarty_tpl->tpl_vars['relay_rule']->value->id;
} else { ?>0<?php }?>)" <?php } else { ?> data-toggle="modal" data-target="#node-modal-ban" <?php }?>>
			
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];?>

									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
							<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['sort'] == 11) {?>
							    <div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" <?php if ($_smarty_tpl->tpl_vars['user']->value->class >= 3) {?> data-toggle="modal" data-target="#node-modal-<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
" <?php } else { ?> data-toggle="modal" data-target="#node-modal-ban" <?php }?>>
						
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span><!--<span class="bg-gradient-green text-white">在线--></span> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];?>
 <!-- - V2ray-->
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>5%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
							<?php } elseif (($_smarty_tpl->tpl_vars['node']->value['sort'] == 0 || $_smarty_tpl->tpl_vars['node']->value['sort'] == 10) && $_smarty_tpl->tpl_vars['node']->value['mu_only'] != -1) {?>
							    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['nodes_muport']->value, 'single_muport');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['single_muport']->value) {
?>
								    <?php $_smarty_tpl->_assignInScope('relay_rule', null);?>
								<?php if ($_smarty_tpl->tpl_vars['node']->value['sort'] == 10 && $_smarty_tpl->tpl_vars['single_muport']->value['user']['is_multi_user'] != 2) {?>
									<?php $_smarty_tpl->_assignInScope('relay_rule', $_smarty_tpl->tpl_vars['tools']->value->pick_out_relay_rule($_smarty_tpl->tpl_vars['node']->value['id'],$_smarty_tpl->tpl_vars['single_muport']->value['server']->server,$_smarty_tpl->tpl_vars['relay_rules']->value));?>
								<?php }?>
								
								<div class="col ml--2">
								<h4 class="mb-0">
									<a href="javascript:void(0);" <?php if ($_smarty_tpl->tpl_vars['user']->value->class >= 3) {?>onclick="urlChange('<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
',<?php echo $_smarty_tpl->tpl_vars['single_muport']->value['server']->server;?>
,<?php if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {
echo $_smarty_tpl->tpl_vars['relay_rule']->value->id;
} else { ?>0<?php }?>)" <?php } else { ?> data-toggle="modal" data-target="#node-modal-ban" <?php }?>>
			
									<?php if ($_smarty_tpl->tpl_vars['node']->value['online'] == "1") {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } elseif ($_smarty_tpl->tpl_vars['node']->value['online'] == '0') {?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span> |
									<?php } else { ?>
										<span class="bg-gradient-green text-white"><i class="fa fa-chrome fa-spin" style="font-size:15px;color:bg-gradient-green"></i>在线</span><!--<span class="bg-gradient-green text-white">在线--></span> |
									<?php }?>
									<?php echo $_smarty_tpl->tpl_vars['node']->value['name'];
if ($_smarty_tpl->tpl_vars['relay_rule']->value != null) {?> - <?php echo $_smarty_tpl->tpl_vars['relay_rule']->value->dist_node()->name;
}?><!-- - 单端口<?php echo $_smarty_tpl->tpl_vars['single_muport']->value['server']->server;?>
 -->
									</a>
								</h4>
								<p class="text-sm text-muted mb-1 mt-2">节点说明 : <?php echo $_smarty_tpl->tpl_vars['node']->value['info'];?>
</p>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-dashboard icon-ver"></i></span>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_rate'];?>
 倍率</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-plane icon-ver"></i></span>
									<strong><?php if ($_smarty_tpl->tpl_vars['user']->value->isAdmin()) {
if ($_smarty_tpl->tpl_vars['node']->value['online_user'] == -1) {?>N/A<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['online_user'];
}
}?></strong> |-->
								<span class="text-nowrap text-warning"><i class="fa fa-cloud icon-ver"></i></span>
									<strong>负载均衡：<?php if ($_smarty_tpl->tpl_vars['node']->value['latest_load'] == -1) {?>3%<?php } else {
echo $_smarty_tpl->tpl_vars['node']->value['latest_load'];?>
%<?php }?></strong> |稳定运行<i class="fa fa-opera fa-spin" style="font-size:15px;color:green"></i>
								<!--<span class="text-nowrap text-warning"><i class="fa fa-exchange fa-rotate-90"></i></span>
									<strong>限速：<?php echo $_smarty_tpl->tpl_vars['node']->value['bandwidth'];?>
</strong> |
								<span class="text-nowrap text-warning"><i class="fa fa-bar-chart icon-ver"></i></span>
									<?php if ($_smarty_tpl->tpl_vars['node']->value['traffic_limit'] > 0) {?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
/<?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_limit'];?>
</strong> 
									<?php } else { ?>
									<strong><?php echo $_smarty_tpl->tpl_vars['node']->value['traffic_used'];?>
GB</strong>
									<?php }?>-->
								</div>
								<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
							<?php }?>
							</div>
							</div>
						</div>
					</div>
				  </div>
				<?php }?>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>	
				</div>
			</div>
	    </div><!--card-->
		
		</div><!--col-->
	</div><!--row-->

	


	<!-- VMess  Modal -->
	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['nodes']->value, 'node');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['node']->value) {
?>
	<?php if ($_smarty_tpl->tpl_vars['user']->value->class >= $_smarty_tpl->tpl_vars['node']->value->class) {?>
		<?php if ($_smarty_tpl->tpl_vars['node']->value['sort'] == 11) {?>
		<div class="modal fade" id="node-modal-<?php echo $_smarty_tpl->tpl_vars['node']->value['id'];?>
" tabindex="-1" role="dialog" aria-labelledby="VMessModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="VMessModalLabel">
				<?php if ($_smarty_tpl->tpl_vars['config']->value['enable_flag'] == 'true') {?>
				<img src="/images/prefix/<?php echo $_smarty_tpl->tpl_vars['node']->value['flag'];?>
" height="22" width="40" />
				<?php }?>
				 <?php echo $_smarty_tpl->tpl_vars['node']->value['name'];?>
</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
			    <?php $_smarty_tpl->smarty->ext->_tplFunction->callTemplateFunction($_smarty_tpl, 'displayV2rayNode', array('node'=>$_smarty_tpl->tpl_vars['node']->value), true);?>

		      </div>
		      <div class="modal-footer">
		        <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
		      </div>
		    </div>
		  </div>
		</div>
		<?php }?>
	<?php }?>
	<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
	
	    <div class="modal fade" id="node-modal-ban" tabindex="-1" role="dialog" aria-labelledby="banModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
		    <div class="modal-content">
		      <div class="modal-header">
		        <h4 id="banModalLabel" class="text-danger">您没有足够的权限</h4>
		        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
		      </div>
		      <div class="modal-body">
			    <p class="description mb-2">您当前等级不足以使用该节点, 如需升级请<a href="/user/shop"> 点击这里 </a>升级套餐</p>
		      </div>
		      <div class="modal-footer">
		        <button type="button" class="btn btn-Secondary" data-dismiss="modal">Close</button>
		      </div>
		    </div>
		  </div>
		</div>
		
	
		<div class="modal fade" tabindex="-1" role="dialog" id="nodeinfo" aria-labelledby="nodeinfoModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-dialog-centered" role="document">
			<div class="modal-content">
			<div class="modal-header">
				<h4 id="nodeinfoModalLabel">节点详细信息</h4>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
		          <span aria-hidden="true">&times;</span>
		        </button>
			</div>
			
					<iframe id="infoifram" style="display: block;border: none;width: 100%;height: 600px;margin: 0;padding: 0;over"></iframe>

			</div>
		  </div>
		</div>

	  <?php $_smarty_tpl->_subTemplateRender('file:user/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  

<?php echo '<script'; ?>
>
	function urlChange(id, is_mu, rule_id) {
		var site = './node/' + id + '?ismu=' + is_mu + '&relay_rule=' + rule_id;
		if (id == 'guide') {
			var doc = document.getElementById('infoifram').contentWindow.document;
			doc.open();
			doc.write('<img src="../images/node.gif" style="width: 100%;height: 100%; border: none;"/>');
			doc.close();
		}
		else {
			document.getElementById('infoifram').src = site;
		}
		$("#nodeinfo").modal();
	}
<?php echo '</script'; ?>
>
<?php }
/* smarty_template_function_displayV2rayNode_18844018762d3dc663c6996_09424328 */
if (!function_exists('smarty_template_function_displayV2rayNode_18844018762d3dc663c6996_09424328')) {
function smarty_template_function_displayV2rayNode_18844018762d3dc663c6996_09424328(Smarty_Internal_Template $_smarty_tpl,$params) {
$params = array_merge(array('node'=>null), $params);
foreach ($params as $key => $value) {
$_smarty_tpl->tpl_vars[$key] = new Smarty_Variable($value, $_smarty_tpl->isRenderingCache);
}
?>

	<?php $_smarty_tpl->_assignInScope('server_explode', explode(";",$_smarty_tpl->tpl_vars['node']->value['server']));?>
	<p class="description mb-2">地址：<span class="card-tag tag-blue"><?php echo $_smarty_tpl->tpl_vars['server_explode']->value[4];?>
</span></p>

	<p class="description mb-2">端口：<span class="card-tag tag-volcano"><?php echo $_smarty_tpl->tpl_vars['server_explode']->value[4];?>
</span></p>

	<p class="description mb-2">协议参数：<span class="card-tag tag-green"><?php echo $_smarty_tpl->tpl_vars['server_explode']->value[4];?>
</span></p>

	<p class="description mb-2">用户 UUID：<span class="card-tag tag-geekblue"><?php echo $_smarty_tpl->tpl_vars['user']->value->getUuid();?>
</span></p>

	<p class="description mb-2">流量比例：<span class="card-tag tag-red">1</span></p>

	<p class="description mb-2">AlterId：<span class="card-tag tag-purple"><?php echo $_smarty_tpl->tpl_vars['server_explode']->value[2];?>
</span></p>

	<p class="description mb-2">VMess链接：
		<a href="javascript:void(0);" class="copy-text" data-clipboard-text="<?php echo App\Utils\URL::getV2Url($_smarty_tpl->tpl_vars['user']->value,$_smarty_tpl->tpl_vars['node']->value['raw_node']);?>
">点击复制</a>
	</p>
<?php
}}
/*/ smarty_template_function_displayV2rayNode_18844018762d3dc663c6996_09424328 */
}
