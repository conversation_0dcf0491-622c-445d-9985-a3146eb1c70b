{include file='admin/main.tpl'}
<link href="/theme/czssr/main/css/quill.snow.css" rel="stylesheet">
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Create Ann</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/admin">管理中心</a></li>
				  <li class="breadcrumb-item"><a href="/admin/announcement">公告系统</a></li>
                  <li class="breadcrumb-item active" aria-current="page">编辑公告 #{$ann->id}</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user" class="btn btn-sm btn-neutral"><i class="ni ni-bulb-61 ni-lg"></i> 前台</a>
              <a href="/admin/ticket" class="btn btn-sm btn-neutral">工单</a>
            </div>
          </div>
        </div>
      </div>
    </div><!-- Header -->
	<!-- Page content -->
    <div class="container-fluid mt--6">
      <!-- Table -->
      <div class="row justify-content-center">
        <div class="col-lg-10">
			<div class="card">
              <!-- Card header -->
              <div class="card-header">
                <h3 class="mb-0">编辑公告</h3>
              </div>
              <!-- Card body -->
				<div class="card bg-gradient-Secondary">
					<div class="card-body">
						
						<div class="form-group mt-3">
							<label class="form-control-label">内容(点击上传图片后会自动上传比较慢等缓存,别重复点)&nbsp;:&nbsp;</label>
    						<div id="editor">
								<div v-html="content">{$ann->content}</div>
							</div>
						</div>
						
                      <div class="modal-footer">
						<button id="submit" type="button" class="btn btn-primary">确认修改</button>
					  </div>
					</div>
					
				</div>
			</div>
		
        </div>
      </div><!--row-->
	  
	{include file='dialog.tpl'}
	{include file='admin/footer.tpl'}

<script src="/theme/czssr/main/js/quill.min.js"></script>

<script>
var toolbarOptions = [
  ['bold', 'italic', 'underline', 'strike', 'blockquote', 'code-block', 'link', 'image', 'video'],        // 切换按钮
  [{ 'header': 1 }, { 'header': 2 }, { 'list': 'ordered'}, { 'list': 'bullet' }, { 'align': [] }],               // 用户自定义按钮值
  [{ 'script': 'sub'}, { 'script': 'super' }, { 'indent': '-1'}, { 'indent': '+1' }],      // 上标/下标// 减少缩进/缩进

  [/*{ 'size': ['small', 'large', 'huge']}*/ { 'header': [1, 2, 3, 4, 5, 6, false] }, { 'font': [] }],  //颜色//背景// 用户自定义下拉//大小//字体//
  [{ 'color': [] }, { 'background': [] }],
  ['clean']                                         // 清除格式
];

var quill = new Quill('#editor', {
  modules: {
    toolbar: toolbarOptions
  },
  theme: 'snow'
});

</script>	  
<script>
    $(document).ready(function () {
        function submit() {
		
            $("#result").modal();
            $('#msg').html('正在提交...');
			
			var quill_html = document.querySelector('#editor').children[0].innerHTML;
            var quill_markdown = document.querySelector('#editor').children[0].innerText;
			//html = '<div class="ql-container ql-snow"><div class="ql-editor">'+html+"</div></div>";
			
            $.ajax({
                type: "PUT",
                url: "/admin/announcement/{$ann->id}",
                dataType: "json",
                data: {
                    content: quill_html,
                    markdown: quill_markdown
                },
                success: function(data) {
                    if (data.ret) {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                        window.setTimeout("location.href=top.document.referrer", {$config['jump_delay']});
                    } else {
                        $("#result").modal();
                        $('#msg').html(data.msg);
                    }
                },
                error: function(jqXHR) {
                    $("#result").modal();
                    $('#msg').html("发生错误："+jqXHR.status);
                }
            });
        }

        $("#submit").click(function () {
            submit();
        });
    })

</script>