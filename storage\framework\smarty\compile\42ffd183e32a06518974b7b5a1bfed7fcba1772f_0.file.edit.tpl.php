<?php
/* Smarty version 3.1.33, created on 2022-07-17 23:02:51
  from '/www/wwwroot/www.shadowingy.com/resources/views/czssr/user/edit.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_62d4249b112b23_90507331',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '42ffd183e32a06518974b7b5a1bfed7fcba1772f' => 
    array (
      0 => '/www/wwwroot/www.shadowingy.com/resources/views/czssr/user/edit.tpl',
      1 => 1576596874,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:user/main.tpl' => 1,
    'file:dialog.tpl' => 1,
    'file:user/footer.tpl' => 1,
  ),
),false)) {
function content_62d4249b112b23_90507331 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:user/main.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
     <!-- Header -->
    <div class="header bg-primary pb-6">
      <div class="container-fluid">
        <div class="header-body">
          <div class="row align-items-center py-4">
            <div class="col-lg-6 col-7">
              <h6 class="h2 text-white d-inline-block mb-0">Advanced Settings</h6>
              <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
                  <li class="breadcrumb-item"><a href="/user">用户中心</a></li>
                  <li class="breadcrumb-item active" aria-current="page">连接设置</li>
                </ol>
              </nav>
            </div>
            <div class="col-lg-6 col-5 text-right">
              <a href="/user/ticket/create" class="btn btn-sm btn-neutral">工单</a>
              <a href="/user/node" class="btn btn-sm btn-neutral">查看节点</a>
            </div>
          </div>
          <!-- Card stats -->

        </div>
      </div>
    </div><!-- Header -->
	<div class="container-fluid mt--6">
	  <div class="row row-example">
		<div class="col-lg-7 col-md-7">
          <div class="card">
            <div class="card-header bg-transparent">
                <h3 class="mb-0"><i class="ni ni-atom ni-lg"></i>&nbsp;协议和混淆设置</h3>
            </div>
            <div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>当前协议:&nbsp;<code id="ajax-user-protocol">[<?php if (App\Utils\URL::CanProtocolConnect($_smarty_tpl->tpl_vars['user']->value->protocol) == 3) {?>SS/SSD/SSR<?php } else { ?>SSR<?php }?> 可连接] <?php echo $_smarty_tpl->tpl_vars['user']->value->protocol;?>
</code></p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>如果需要兼容 SS/SSD 请设置为 origin 或选择带_compatible的兼容选项.</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>auth_chain 系为实验性协议, 可能造成不稳定或无法使用, 开启前请询问是否支持.</p>
				<?php $_smarty_tpl->_assignInScope('protocol_list', $_smarty_tpl->tpl_vars['config_service']->value->getSupportParam('protocol'));?>
				<select id="protocol" class="form-control form-control-sm" value="<?php echo $_smarty_tpl->tpl_vars['user']->value->protocol;?>
">
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['protocol_list']->value, 'protocol');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['protocol']->value) {
?>
					<option value="<?php echo $_smarty_tpl->tpl_vars['protocol']->value;?>
" <?php if ($_smarty_tpl->tpl_vars['user']->value->protocol == $_smarty_tpl->tpl_vars['protocol']->value) {?>selected<?php }?>>[<?php if (App\Utils\URL::CanProtocolConnect($_smarty_tpl->tpl_vars['protocol']->value) == 3) {?>SS/SSD/SSR<?php } else { ?>SSR<?php }?> 可连接] <?php echo $_smarty_tpl->tpl_vars['protocol']->value;?>
</option>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
				</select>
            </div>
			<div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>当前混淆:&nbsp;<code id="ajax-user-obfs">[<?php if (App\Utils\URL::CanObfsConnect($_smarty_tpl->tpl_vars['user']->value->obfs) >= 3) {?>SS/SSD/SSR<?php } elseif (App\Utils\URL::CanObfsConnect($_smarty_tpl->tpl_vars['user']->value->obfs) == 1) {?>SSR<?php } else { ?>SS/SSD<?php }?> 可连接] <?php echo $_smarty_tpl->tpl_vars['user']->value->obfs;?>
</code></p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>如果需要兼容 SS/SSD 请设置为 plain 或选择带_compatible的兼容选项.</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>SS/SSD 和 SSR 支持的混淆类型有所不同, simple_obfs_* 为 SS/SSD 的混淆方式, 其他为 SSR 的混淆方式.</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>如果使用 SS/SSD 作为客户端, 请确保自己知道如何下载并使用混淆插件.</p>
				<?php $_smarty_tpl->_assignInScope('obfs_list', $_smarty_tpl->tpl_vars['config_service']->value->getSupportParam('obfs'));?>
				<select id="obfs" class="form-control form-control-sm" value="<?php echo $_smarty_tpl->tpl_vars['user']->value->obfs;?>
">
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['obfs_list']->value, 'obfs');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['obfs']->value) {
?>
					<option value="<?php echo $_smarty_tpl->tpl_vars['obfs']->value;?>
" <?php if ($_smarty_tpl->tpl_vars['user']->value->obfs == $_smarty_tpl->tpl_vars['obfs']->value) {?>selected<?php }?>>[<?php if (App\Utils\URL::CanObfsConnect($_smarty_tpl->tpl_vars['obfs']->value) >= 3) {?>SS/SSD/SSR<?php } else {
if (App\Utils\URL::CanObfsConnect($_smarty_tpl->tpl_vars['obfs']->value) == 1) {?>SSR<?php } else { ?>SS/SSD<?php }
}?> 可连接] <?php echo $_smarty_tpl->tpl_vars['obfs']->value;?>
</option>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
				</select>
            </div>
			<div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>当前混淆参数:&nbsp;<code id="ajax-user-obfs-param"><?php if ($_smarty_tpl->tpl_vars['user']->value->obfs_param == null) {?>无<?php } else {
echo $_smarty_tpl->tpl_vars['user']->value->obfs_param;
}?></code></p>
				<div class="p-4 bg-secondary">
					<input id="obfs-param" type="text" class="form-control form-control-alternative"  placeholder="请输入混淆参数">
				</div>
            </div>
			<div class="modal-footer">
               <button id="ssr-update" type="button" class="btn btn-danger">提交更改</button>
            </div>
          </div>
		  
		<?php if ($_smarty_tpl->tpl_vars['config']->value['port_price'] >= 0 || $_smarty_tpl->tpl_vars['config']->value['port_price_specify'] >= 0) {?>
		  <div class="card">
            <div class="card-header bg-transparent">
               <h3 class="mb-0"><i class="fa fa-repeat fa-lg"></i>&nbsp;重置端口</h3>
            </div>
			<?php if ($_smarty_tpl->tpl_vars['config']->value['port_price'] >= 0) {?>
            <div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>随机更换一个端口, 价格:&nbsp;<code><?php echo $_smarty_tpl->tpl_vars['config']->value['port_price'];?>
</code>元/次</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>重置后请更新订阅, 1分钟左右生效.</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>当前使用端口:&nbsp;<code id="ajax-user-port"><?php echo $_smarty_tpl->tpl_vars['user']->value->port;?>
</code></p>
            </div>
			<div class="modal-footer">
               <button id="portreset" type="button" class="btn btn-danger">确认提交</button>
            </div>
			<?php }?>
			<?php if ($_smarty_tpl->tpl_vars['config']->value['port_price_specify'] >= 0) {?>
			<div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>不想摇号？来钦定端口吧～！</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>价格:&nbsp;<code><?php echo $_smarty_tpl->tpl_vars['config']->value['port_price_specify'];?>
</code>元/次</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>端口范围:&nbsp;<code><?php echo $_smarty_tpl->tpl_vars['config']->value['min_port'];?>
～<?php echo $_smarty_tpl->tpl_vars['config']->value['max_port'];?>
</code></p>
				<div class="p-4 bg-secondary">
					<input id="port-specify" type="text" class="form-control form-control-alternative" placeholder="请输入你想要的端口号">
				</div>
            </div>
			<div class="modal-footer">
               <button id="portspecify" type="button" class="btn btn-danger">确认提交</button>
            </div>
			<?php }?>
          </div>
		<?php }?>
		</div>
		
		<div class="col-lg-5 col-md-5">
			<div class="card">
            <div class="card-header bg-transparent">
               <h3 class="mb-0"><i class="ni ni-lock-circle-open ni-lg"></i>&nbsp;连接密码</h3>
            </div>
            <div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>当前连接密码:&nbsp;<code id="ajax-user-passwd"><?php echo $_smarty_tpl->tpl_vars['user']->value->passwd;?>
</code>&nbsp;&nbsp;您需要了解的是&nbsp;:</p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>为了确保您的安全, 节点连接密码不允许自定义. 点击提交按钮将会自动生成由随机字母和数字组成的连接密码. </p>
                <p class="description badge-dot mr-4"><i class="bg-warning"></i>修改连接密码同时也会自动为您重新生成 V2Ray 节点的 UUID.</p>
                <p class="description badge-dot mr-4"><i class="bg-warning"></i>修改密码后, 请立刻更新各个客户端上的连接信息.</p>
				<div class="p-4 bg-secondary">
					<input type="text" class="form-control form-control-alternative" value="<?php echo $_smarty_tpl->tpl_vars['user']->value->passwd;?>
" readonly disabled />
				</div>
            </div>
			<div class="modal-footer">
               <button id="ss-pwd-update" type="button" class="btn btn-primary">确认提交</button>
            </div>
          </div>
		  <div class="card">
            <div class="card-header bg-transparent">
               <h3 class="mb-0"><i class="ni ni-key-25 ni-lg"></i>&nbsp;加密方式</h3>
            </div>
            <div class="card-body">
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>当前加密方式:&nbsp;<code id="ajax-user-method">[<?php if (App\Utils\URL::CanMethodConnect($_smarty_tpl->tpl_vars['user']->value->method) == 2) {?>SS/SSD<?php } else { ?>SS/SSR<?php }?>可连接] <?php echo $_smarty_tpl->tpl_vars['user']->value->method;?>
</code></p>
				<p class="description badge-dot mr-4"><i class="bg-warning"></i>SS/SSD/SSR 各个客户端支持的加密方式有所不同, 请根据实际情况来进行选择.</p>
				<?php $_smarty_tpl->_assignInScope('method_list', $_smarty_tpl->tpl_vars['config_service']->value->getSupportParam('method'));?>
				<select id="method" class="form-control form-control-sm" value="<?php echo $_smarty_tpl->tpl_vars['user']->value->method;?>
">
				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['method_list']->value, 'method');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['method']->value) {
?>
					<option value="<?php echo $_smarty_tpl->tpl_vars['method']->value;?>
" <?php if ($_smarty_tpl->tpl_vars['user']->value->method == $_smarty_tpl->tpl_vars['method']->value) {?>selected<?php }?>>[<?php if (App\Utils\URL::CanMethodConnect($_smarty_tpl->tpl_vars['method']->value) == 2) {?>SS/SSD<?php } else { ?>SS/SSR<?php }?> 可连接] <?php echo $_smarty_tpl->tpl_vars['method']->value;?>
</option>
				<?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
				</select>
            </div>
			<div class="modal-footer">
               <button id="method-update" type="button" class="btn btn-primary">确认提交</button>
            </div>
          </div>
        </div>
		
	  </div><!--row-->
	  
	  <?php $_smarty_tpl->_subTemplateRender('file:dialog.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	  <?php $_smarty_tpl->_subTemplateRender('file:user/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
	 
<?php }
}
